interface Env {
    bgdb: D1Database;
}

interface RespondInviteRequest {
    action: 'accept' | 'decline';
}

// 响应邀请
export const onRequestPost: PagesFunction<Env> = async (context) => {
    try {
        const { env, data, request } = context;
        const url = new URL(request.url);
        const invitationId = url.pathname.split('/').slice(-2, -1)[0]; // 获取邀请ID
        const body: RespondInviteRequest = await request.json();
        
        if (!invitationId || !body.action) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '缺少必填字段：invitation_id, action' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const db = env.bgdb;
        const userId = data.user?.userId;

        if (!userId) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '用户未登录' 
                }), 
                { status: 401, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 获取邀请信息
        const invitation = await db.prepare(`
            SELECT * FROM group_invitations 
            WHERE id = ? AND invitee_user_id = ?
        `).bind(invitationId, String(userId)).first();

        if (!invitation) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '邀请不存在或无权限' 
                }), 
                { status: 404, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 检查邀请状态
        if (invitation.status !== 'pending') {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '邀请已处理' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 检查邀请是否过期
        const now = new Date();
        const expiresAt = new Date(invitation.expires_at);
        if (now > expiresAt) {
            // 更新邀请状态为过期
            await db.prepare(`
                UPDATE group_invitations 
                SET status = 'expired', responded_at = ?
                WHERE id = ?
            `).bind(now.toISOString(), invitationId).run();

            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '邀请已过期' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        if (body.action === 'accept') {
            // 再次检查公共群聊数量限制
            const publicGroupCount = await db.prepare(`
                SELECT COUNT(*) as count FROM group_members gm
                JOIN groups_new g ON gm.group_id = g.id
                WHERE gm.member_id = ? AND gm.member_type = 'user' 
                AND g.created_by != ?
            `).bind(String(userId), String(userId)).first();

            if (publicGroupCount && publicGroupCount.count >= 10) {
                return new Response(
                    JSON.stringify({ 
                        success: false, 
                        message: '您已达到公共群聊数量上限（10个）' 
                    }), 
                    { status: 400, headers: { 'Content-Type': 'application/json' } }
                );
            }

            // 检查用户是否已经在群聊中
            const existingMember = await db.prepare(`
                SELECT id FROM group_members 
                WHERE group_id = ? AND member_id = ? AND member_type = 'user'
            `).bind(invitation.group_id, String(userId)).first();

            if (existingMember) {
                // 更新邀请状态
                await db.prepare(`
                    UPDATE group_invitations 
                    SET status = 'accepted', responded_at = ?
                    WHERE id = ?
                `).bind(now.toISOString(), invitationId).run();

                return new Response(
                    JSON.stringify({ 
                        success: false, 
                        message: '您已在该群聊中' 
                    }), 
                    { status: 400, headers: { 'Content-Type': 'application/json' } }
                );
            }

            // 添加用户到群聊
            await db.prepare(`
                INSERT INTO group_members (group_id, member_id, member_type, role, invited_by)
                VALUES (?, ?, 'user', ?, ?)
            `).bind(
                invitation.group_id,
                String(userId),
                invitation.role,
                invitation.inviter_id
            ).run();

            // 更新邀请状态
            await db.prepare(`
                UPDATE group_invitations 
                SET status = 'accepted', responded_at = ?
                WHERE id = ?
            `).bind(now.toISOString(), invitationId).run();

            return new Response(
                JSON.stringify({
                    success: true,
                    message: '已成功加入群聊',
                    data: {
                        group_id: invitation.group_id,
                        action: 'accepted'
                    }
                }),
                {
                    status: 200,
                    headers: { 'Content-Type': 'application/json' }
                }
            );

        } else if (body.action === 'decline') {
            // 拒绝邀请
            await db.prepare(`
                UPDATE group_invitations 
                SET status = 'declined', responded_at = ?
                WHERE id = ?
            `).bind(now.toISOString(), invitationId).run();

            return new Response(
                JSON.stringify({
                    success: true,
                    message: '已拒绝邀请',
                    data: {
                        group_id: invitation.group_id,
                        action: 'declined'
                    }
                }),
                {
                    status: 200,
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        }

        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '无效的操作' 
            }), 
            { status: 400, headers: { 'Content-Type': 'application/json' } }
        );

    } catch (error) {
        console.error('响应邀请失败:', error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误，请稍后重试' 
            }), 
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
};
