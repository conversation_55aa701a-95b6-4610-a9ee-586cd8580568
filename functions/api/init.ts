import { groups } from '../../src/config/groups';

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

// 从Agent Store获取智能体数据
const getAgentsFromStore = () => {
  // 模拟Agent数据 - 与Agent Store保持一致
  const mockAgents = [
    {
      id: 'super-agent-001',
      name: '超级智能体',
      display_name: '超级智能体',
      description: '负责任务分配和协调的超级智能体，具有管理员权限，可以调度其他智能体参与群聊',
      api_type: 'openai',
      avatar_url: '',
      status: 1, // 1=启用, 0=禁用, 2=维护中
      capabilities: ['任务分配', '意图理解', '协调管理', '决策制定'],
      avg_rating: 4.8,
      total_tasks: 1250,
      completed_tasks: 1188,
      success_rate: 95,
      created_at: '2024-01-01T00:00:00Z',
      last_used_at: '2024-01-29T10:30:00Z',
      is_super_agent: true,
      personality: 'super_agent',
      model: 'gpt-4'
    },
    {
      id: 'coding-agent-001',
      name: '编程助手',
      display_name: '编程助手',
      description: '专业的编程和技术问题解答Agent，擅长多种编程语言和框架',
      api_type: 'openai',
      avatar_url: '',
      status: 1,
      capabilities: ['编程', '代码审查', '技术文档', '调试'],
      avg_rating: 4.4,
      total_tasks: 856,
      completed_tasks: 753,
      success_rate: 88,
      created_at: '2024-01-01T00:00:00Z',
      last_used_at: '2024-01-29T09:15:00Z',
      is_super_agent: false,
      personality: 'coding_assistant',
      model: 'gpt-4'
    },
    {
      id: 'writing-agent-001',
      name: '写作助手',
      display_name: '写作助手',
      description: '专业的文案写作和内容创作Agent，能够处理各种写作需求',
      api_type: 'claude',
      avatar_url: '',
      status: 1,
      capabilities: ['文案写作', '内容策划', '翻译', '校对'],
      avg_rating: 4.6,
      total_tasks: 642,
      completed_tasks: 591,
      success_rate: 92,
      created_at: '2024-01-01T00:00:00Z',
      last_used_at: '2024-01-29T08:45:00Z',
      is_super_agent: false,
      personality: 'writing_assistant',
      model: 'claude-3'
    },
    {
      id: 'analysis-agent-001',
      name: '数据分析师',
      display_name: '数据分析师',
      description: '专业的数据分析和商业洞察Agent，擅长数据处理和可视化',
      api_type: 'qwen',
      avatar_url: '',
      status: 1,
      capabilities: ['数据分析', '统计建模', '可视化', '报告生成'],
      avg_rating: 4.2,
      total_tasks: 324,
      completed_tasks: 275,
      success_rate: 85,
      created_at: '2024-01-01T00:00:00Z',
      last_used_at: '2024-01-28T16:20:00Z',
      is_super_agent: false,
      personality: 'data_analyst',
      model: 'qwen-max'
    },
    {
      id: 'design-agent-001',
      name: '设计顾问',
      display_name: '设计顾问',
      description: '专业的UI/UX设计和创意指导Agent，提供设计建议和创意方案',
      api_type: 'deepseek',
      avatar_url: '',
      status: 1, // 改为启用状态，让其能在群聊中使用
      capabilities: ['UI设计', 'UX设计', '创意策划', '品牌设计'],
      avg_rating: 4.5,
      total_tasks: 198,
      completed_tasks: 178,
      success_rate: 90,
      created_at: '2024-01-01T00:00:00Z',
      last_used_at: '2024-01-27T14:10:00Z',
      is_super_agent: false,
      personality: 'design_consultant',
      model: 'deepseek-chat'
    }
  ];

  // 转换为群聊系统需要的格式
  return mockAgents.filter(agent => agent.status === 1).map(agent => ({
    id: agent.id,
    name: agent.display_name || agent.name,
    personality: agent.personality,
    model: agent.model,
    avatar: agent.avatar_url || '',
    custom_prompt: `你是${agent.name}，${agent.description}。你的主要能力包括：${agent.capabilities.join('、')}。`,
    tags: agent.capabilities,
    is_super_agent: agent.is_super_agent || false
  }));
};

export async function onRequestOptions() {
  return new Response(null, { headers: corsHeaders });
}

export async function onRequestGet(context) {
    try {
      // 获取真实的智能体数据
      const characters = getAgentsFromStore();

      // 更新群组配置，使用真实的智能体ID
      const updatedGroups = groups.map(group => ({
        ...group,
        members: group.members.filter(memberId =>
          characters.some(char => char.id === memberId)
        ).concat(
          // 确保超级智能体在每个群组中
          characters.filter(char => char.is_super_agent).map(char => char.id)
        ).filter((id, index, arr) => arr.indexOf(id) === index) // 去重
      }));

      return Response.json({
        code: 200,
        data: {
          groups: updatedGroups,
          characters: characters,
          user: context.data.user || null
        }
      }, { headers: corsHeaders });
    } catch (error) {
      console.error(error);
      return Response.json(
        { error: error.message },
        { status: 500, headers: corsHeaders }
      );
    }
  }