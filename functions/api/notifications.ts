import { Env } from '../types';

export async function onRequest(context: { request: Request; env: Env }) {
  const { request, env } = context;
  const url = new URL(request.url);

  // 处理 CORS
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  }

  try {
    if (request.method === 'GET') {
      // 获取通知列表
      return await getNotifications(env);
    } else if (request.method === 'POST') {
      // 创建通知
      const body = await request.json();
      return await createNotification(env, body);
    } else if (request.method === 'PUT') {
      // 标记通知为已读
      const body = await request.json();
      return await markNotificationAsRead(env, body);
    } else {
      return new Response(JSON.stringify({ success: false, message: '不支持的请求方法' }), {
        status: 405,
        headers: { 'Content-Type': 'application/json' },
      });
    }
  } catch (error) {
    console.error('通知API错误:', error);
    return new Response(JSON.stringify({ 
      success: false, 
      message: '服务器内部错误',
      error: error.message 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

async function getNotifications(env: Env) {
  try {
    // 模拟通知数据
    const mockNotifications = [
      {
        id: '1',
        type: 'mention',
        title: '@提醒',
        content: '张三在"技术讨论群"中@了您',
        sender: { id: '2', name: '张三' },
        groupName: '技术讨论群',
        timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
        read: false,
        actionUrl: '/chat?id=1'
      },
      {
        id: '2',
        type: 'message',
        title: '新消息',
        content: '您有3条新消息',
        groupName: '产品讨论群',
        timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
        read: false,
        actionUrl: '/chat?id=2'
      },
      {
        id: '3',
        type: 'group_invite',
        title: '群聊邀请',
        content: '李四邀请您加入"设计团队"群聊',
        sender: { id: '3', name: '李四' },
        groupName: '设计团队',
        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        read: true,
        actionUrl: '/chat?invite=abc123'
      },
      {
        id: '4',
        type: 'system',
        title: '系统通知',
        content: '您的账户余额不足，请及时充值',
        timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
        read: false,
        actionUrl: '/profile?tab=wallet'
      }
    ];

    const unreadCount = mockNotifications.filter(n => !n.read).length;

    return new Response(JSON.stringify({
      success: true,
      data: {
        notifications: mockNotifications,
        unread_count: unreadCount
      }
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('获取通知失败:', error);
    return new Response(JSON.stringify({ 
      success: false, 
      message: '获取通知失败',
      error: error.message 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

async function createNotification(env: Env, data: any) {
  // TODO: 实现创建通知的逻辑
  return new Response(JSON.stringify({
    success: true,
    message: '通知创建成功'
  }), {
    status: 200,
    headers: { 'Content-Type': 'application/json' },
  });
}

async function markNotificationAsRead(env: Env, data: any) {
  // TODO: 实现标记通知为已读的逻辑
  return new Response(JSON.stringify({
    success: true,
    message: '通知已标记为已读'
  }), {
    status: 200,
    headers: { 'Content-Type': 'application/json' },
  });
}
