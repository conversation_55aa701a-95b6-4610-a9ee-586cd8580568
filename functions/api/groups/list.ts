interface Env {
    bgdb: D1Database;
}

interface GroupListQuery {
    page?: number;
    limit?: number;
    group_type?: string;
    search?: string;
    user_id?: number;
}

export const onRequestGet: PagesFunction<Env> = async (context) => {
    try {
        const { env, data, request } = context;
        const url = new URL(request.url);
        
        // 解析查询参数
        const query: GroupListQuery = {
            page: parseInt(url.searchParams.get('page') || '1'),
            limit: Math.min(parseInt(url.searchParams.get('limit') || '20'), 100),
            group_type: url.searchParams.get('group_type') || undefined,
            search: url.searchParams.get('search') || undefined,
            user_id: data.user?.userId
        };

        const db = env.bgdb;
        let whereConditions = ['1=1'];
        let params: any[] = [];

        // 构建查询条件
        if (query.group_type) {
            whereConditions.push('g.group_type = ?');
            params.push(query.group_type);
        }

        if (query.search) {
            whereConditions.push('(g.name LIKE ? OR g.description LIKE ?)');
            const searchTerm = `%${query.search}%`;
            params.push(searchTerm, searchTerm);
        }

        // 如果指定了用户ID，只返回该用户参与的群组
        if (query.user_id) {
            whereConditions.push(`EXISTS (
                SELECT 1 FROM group_members gm 
                WHERE gm.group_id = g.id 
                AND gm.member_type = 'user' 
                AND gm.member_id = ?
            )`);
            params.push(query.user_id);
        }

        // 计算偏移量
        const offset = (query.page! - 1) * query.limit!;

        // 查询群组列表 - 区分我的群聊和公共群聊
        const groupsQuery = `
            SELECT
                g.*,
                u.nickname as creator_name,
                COUNT(gm.id) as member_count,
                COUNT(CASE WHEN gm.member_type = 'user' THEN 1 END) as user_count,
                COUNT(CASE WHEN gm.member_type = 'agent' THEN 1 END) as agent_count,
                MAX(m.created_at) as last_message_time,
                CASE
                    WHEN g.created_by = ? THEN 'my_groups'
                    ELSE 'public_groups'
                END as group_category
            FROM groups_new g
            LEFT JOIN users u ON g.created_by = u.id
            LEFT JOIN group_members gm ON g.id = gm.group_id
            LEFT JOIN messages m ON g.id = m.group_id
            WHERE ${whereConditions.join(' AND ')}
            GROUP BY g.id
            ORDER BY g.created_at DESC
            LIMIT ? OFFSET ?
        `;

        const groups = await db.prepare(groupsQuery)
            .bind(query.user_id, ...params, query.limit, offset)
            .all();

        // 查询总数
        const countQuery = `
            SELECT COUNT(DISTINCT g.id) as total
            FROM groups_new g
            ${query.user_id ? 'INNER JOIN group_members gm ON g.id = gm.group_id AND gm.member_type = \'user\' AND gm.member_id = ?' : ''}
            WHERE ${whereConditions.join(' AND ')}
        `;

        const countParams = query.user_id ? [query.user_id, ...params] : params;
        const countResult = await db.prepare(countQuery)
            .bind(...countParams)
            .first();

        // 为每个群组获取成员预览
        const groupsWithMembers = await Promise.all(
            groups.results.map(async (group) => {
                // 获取前几个成员作为预览
                const memberPreview = await db.prepare(`
                    SELECT 
                        gm.member_type,
                        gm.member_id,
                        CASE 
                            WHEN gm.member_type = 'user' THEN u.nickname
                            WHEN gm.member_type = 'agent' THEN a.display_name
                        END as member_name,
                        CASE 
                            WHEN gm.member_type = 'user' THEN u.avatar_url
                            WHEN gm.member_type = 'agent' THEN a.avatar_url
                        END as avatar_url
                    FROM group_members gm
                    LEFT JOIN users u ON gm.member_type = 'user' AND gm.member_id = u.id
                    LEFT JOIN agents a ON gm.member_type = 'agent' AND gm.member_id = a.id
                    WHERE gm.group_id = ?
                    ORDER BY gm.joined_at
                    LIMIT 5
                `).bind(group.id).all();

                return {
                    ...group,
                    member_preview: memberPreview.results,
                    last_message_time: group.last_message_time || null
                };
            })
        );

        // 按分类组织群聊数据
        const myGroups = groupsWithMembers.filter(g => g.group_category === 'my_groups').slice(0, 5);
        const publicGroups = groupsWithMembers.filter(g => g.group_category === 'public_groups').slice(0, 10);

        return new Response(
            JSON.stringify({
                success: true,
                data: {
                    my_groups: myGroups,
                    public_groups: publicGroups,
                    limits: {
                        my_groups_max: 5,
                        public_groups_max: 10,
                        my_groups_count: myGroups.length,
                        public_groups_count: publicGroups.length
                    },
                    pagination: {
                        page: query.page,
                        limit: query.limit,
                        total: countResult.total,
                        pages: Math.ceil(countResult.total / query.limit!)
                    }
                }
            }),
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('获取群组列表失败:', error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误，请稍后重试' 
            }), 
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
};
