interface Env {
    bgdb: D1Database;
}

interface GroupInfo {
    id: number;
    name: string;
    description: string;
    type: string;
    created_by: string;
    created_at: string;
    updated_at: string;
    member_count: number;
    agent_count: number;
    last_message?: string;
    last_message_time?: string;
}

export const onRequestGet: PagesFunction<Env> = async (context) => {
    try {
        const { env, data, request } = context;
        const db = env.bgdb;
        let userId = data.user?.userId;

        // 开发模式下，如果没有用户信息，使用默认管理员用户
        if (!userId && env.AUTH_ACCESS === '0') {
            userId = '1'; // 默认管理员用户ID
        }

        if (!userId) {
            return new Response(
                JSON.stringify({
                    success: false,
                    message: '用户未登录'
                }),
                { status: 401, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 获取我的群聊（用户创建的群聊，最多5个）
        const myGroupsQuery = `
            SELECT
                g.id,
                g.name,
                g.description,
                g.group_type as type,
                g.created_by,
                g.created_at,
                g.updated_at,
                COUNT(DISTINCT CASE WHEN gm.member_type = 'user' THEN gm.member_id END) as member_count,
                COUNT(DISTINCT CASE WHEN gm.member_type = 'agent' THEN gm.member_id END) as agent_count
            FROM groups_new g
            LEFT JOIN group_members gm ON g.id = gm.group_id
            WHERE g.created_by = ?
            GROUP BY g.id
            ORDER BY g.created_at DESC
            LIMIT 5
        `;

        const myGroups = await db.prepare(myGroupsQuery)
            .bind(String(userId))
            .all();

        // 获取公共群聊（用户加入但不是创建者的群聊，最多10个）
        const publicGroupsQuery = `
            SELECT
                g.id,
                g.name,
                g.description,
                g.group_type as type,
                g.created_by,
                g.created_at,
                g.updated_at,
                COUNT(DISTINCT CASE WHEN gm.member_type = 'user' THEN gm.member_id END) as member_count,
                COUNT(DISTINCT CASE WHEN gm.member_type = 'agent' THEN gm.member_id END) as agent_count
            FROM groups_new g
            INNER JOIN group_members ugm ON g.id = ugm.group_id
                AND ugm.member_type = 'user'
                AND ugm.member_id = ?
            LEFT JOIN group_members gm ON g.id = gm.group_id
            WHERE g.created_by != ?
            GROUP BY g.id
            ORDER BY g.created_at DESC
            LIMIT 10
        `;

        const publicGroups = await db.prepare(publicGroupsQuery)
            .bind(String(userId), String(userId))
            .all();

        return new Response(
            JSON.stringify({
                success: true,
                data: {
                    my_groups: myGroups.results || [],
                    public_groups: publicGroups.results || []
                }
            }),
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('获取群组列表失败:', error);
        return new Response(
            JSON.stringify({
                success: false,
                message: '服务器错误，请稍后重试'
            }),
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
};
