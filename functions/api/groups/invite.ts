interface Env {
    bgdb: D1Database;
}

interface InviteRequest {
    group_id: number;
    invitees: string[]; // 用户名或邮箱列表
    role?: string;
    message?: string;
}

interface RespondInviteRequest {
    action: 'accept' | 'decline';
}

// 发送邀请
export const onRequestPost: PagesFunction<Env> = async (context) => {
    try {
        const { env, data, request } = context;
        const body: InviteRequest = await request.json();
        
        if (!body.group_id || !body.invitees || body.invitees.length === 0) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '缺少必填字段：group_id, invitees' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const db = env.bgdb;
        const userId = data.user?.userId;

        if (!userId) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '用户未登录' 
                }), 
                { status: 401, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 验证用户是否有邀请权限（群主或管理员）
        const memberRole = await db.prepare(`
            SELECT role FROM group_members 
            WHERE group_id = ? AND member_id = ? AND member_type = 'user'
        `).bind(body.group_id, String(userId)).first();

        if (!memberRole || (memberRole.role !== 'admin' && memberRole.role !== 'owner')) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '没有邀请权限' 
                }), 
                { status: 403, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 检查群聊是否存在
        const group = await db.prepare(`
            SELECT * FROM groups_new WHERE id = ?
        `).bind(body.group_id).first();

        if (!group) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '群聊不存在' 
                }), 
                { status: 404, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 检查被邀请用户的公共群聊数量限制
        const inviteResults = [];
        
        for (const invitee of body.invitees) {
            try {
                // 查找用户
                const targetUser = await db.prepare(`
                    SELECT id, nickname FROM users 
                    WHERE nickname = ? OR phone = ?
                `).bind(invitee, invitee).first();

                if (!targetUser) {
                    inviteResults.push({
                        invitee,
                        success: false,
                        message: '用户不存在'
                    });
                    continue;
                }

                // 检查用户是否已经在群聊中
                const existingMember = await db.prepare(`
                    SELECT id FROM group_members 
                    WHERE group_id = ? AND member_id = ? AND member_type = 'user'
                `).bind(body.group_id, String(targetUser.id)).first();

                if (existingMember) {
                    inviteResults.push({
                        invitee,
                        success: false,
                        message: '用户已在群聊中'
                    });
                    continue;
                }

                // 检查公共群聊数量限制（被邀请加入的群聊）
                const publicGroupCount = await db.prepare(`
                    SELECT COUNT(*) as count FROM group_members gm
                    JOIN groups_new g ON gm.group_id = g.id
                    WHERE gm.member_id = ? AND gm.member_type = 'user' 
                    AND g.created_by != ?
                `).bind(String(targetUser.id), String(targetUser.id)).first();

                if (publicGroupCount && publicGroupCount.count >= 10) {
                    inviteResults.push({
                        invitee,
                        success: false,
                        message: '用户已达到公共群聊数量上限（10个）'
                    });
                    continue;
                }

                // 创建邀请记录
                const invitationId = `inv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7天后过期

                await db.prepare(`
                    INSERT INTO group_invitations 
                    (id, group_id, inviter_id, invitee_user_id, invitation_type, role, status, invitation_message, expires_at)
                    VALUES (?, ?, ?, ?, 'internal', ?, 'pending', ?, ?)
                `).bind(
                    invitationId,
                    body.group_id,
                    String(userId),
                    String(targetUser.id),
                    body.role || 'member',
                    body.message || `邀请您加入群聊"${group.name}"`,
                    expiresAt.toISOString()
                ).run();

                inviteResults.push({
                    invitee,
                    success: true,
                    message: '邀请发送成功',
                    invitation_id: invitationId
                });

            } catch (error) {
                console.error(`邀请用户 ${invitee} 失败:`, error);
                inviteResults.push({
                    invitee,
                    success: false,
                    message: '邀请发送失败'
                });
            }
        }

        return new Response(
            JSON.stringify({
                success: true,
                data: {
                    group_id: body.group_id,
                    invite_results: inviteResults
                }
            }),
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('发送邀请失败:', error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误，请稍后重试' 
            }), 
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
};

// 获取邀请列表
export const onRequestGet: PagesFunction<Env> = async (context) => {
    try {
        const { env, data, request } = context;
        const url = new URL(request.url);
        const groupId = url.searchParams.get('group_id');
        const userId = data.user?.userId;

        if (!userId) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '用户未登录' 
                }), 
                { status: 401, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const db = env.bgdb;
        let query = `
            SELECT 
                gi.*,
                g.name as group_name,
                u1.nickname as inviter_name,
                u2.nickname as invitee_name
            FROM group_invitations gi
            JOIN groups_new g ON gi.group_id = g.id
            LEFT JOIN users u1 ON gi.inviter_id = u1.id
            LEFT JOIN users u2 ON gi.invitee_user_id = u2.id
            WHERE gi.invitee_user_id = ?
            ORDER BY gi.created_at DESC
        `;

        const invitations = await db.prepare(query).bind(String(userId)).all();

        return new Response(
            JSON.stringify({
                success: true,
                data: invitations.results
            }),
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('获取邀请列表失败:', error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误，请稍后重试' 
            }), 
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
};
