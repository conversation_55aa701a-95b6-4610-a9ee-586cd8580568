interface Env {
    bgdb: D1Database;
}

interface CreateGroupRequest {
    name: string;
    description?: string;
    group_type: 'mixed' | 'ai_only' | 'human_only';
    max_members?: number;
    agent_ids?: string[];
    user_ids?: string[];
}

export const onRequestPost: PagesFunction<Env> = async (context) => {
    try {
        const { env, data, request } = context;
        const body: CreateGroupRequest = await request.json();
        
        // 验证必填字段
        if (!body.name || !body.group_type) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '缺少必填字段：name, group_type' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const db = env.bgdb;
        // 如果没有认证用户，使用默认用户ID（用于开发测试）
        const userId = data.user?.userId || 1;

        // 创建群组
        const groupResult = await db.prepare(`
            INSERT INTO groups_new (name, description, group_type, max_members, created_by)
            VALUES (?, ?, ?, ?, ?)
        `).bind(
            body.name,
            body.description || '',
            body.group_type,
            body.max_members || 50,
            userId
        ).run();

        let groupId = groupResult.lastRowId;

        // 如果 lastRowId 不可用，查询最新创建的群组
        if (!groupId) {
            const latestGroup = await db.prepare(`
                SELECT id FROM groups_new
                WHERE created_by = ?
                ORDER BY created_at DESC
                LIMIT 1
            `).bind(userId).first();

            if (latestGroup) {
                groupId = latestGroup.id;
            } else {
                throw new Error('Failed to create group: no group ID returned');
            }
        }

        // 添加创建者为管理员
        await db.prepare(`
            INSERT INTO group_members (group_id, member_id, member_type, role)
            VALUES (?, ?, 'user', 'admin')
        `).bind(groupId, String(userId)).run();

        // 添加其他用户成员
        if (body.user_ids && body.user_ids.length > 0) {
            for (const uid of body.user_ids) {
                if (uid && uid !== userId) { // 避免重复添加创建者，并确保uid不为空
                    await db.prepare(`
                        INSERT INTO group_members (group_id, member_id, member_type, role)
                        VALUES (?, ?, 'user', 'member')
                    `).bind(groupId, String(uid)).run();
                }
            }
        }

        // 如果是混合群聊且没有指定Agent，自动添加超级智能体
        if (body.group_type === 'mixed' && (!body.agent_ids || body.agent_ids.length === 0)) {
            // 使用模拟数据中的超级智能体ID
            const superAgentId = 'super-agent-001';
            body.agent_ids = [superAgentId];
        }

        // 添加Agent成员
        if (body.agent_ids && body.agent_ids.length > 0) {
            for (const agentId of body.agent_ids) {
                if (agentId) { // 确保agentId不为空
                    // 对于模拟数据，直接添加Agent成员
                    // 在实际环境中，这里应该验证Agent是否存在且可用
                    await db.prepare(`
                        INSERT INTO group_members (group_id, member_id, member_type, role)
                        VALUES (?, ?, 'agent', 'member')
                    `).bind(groupId, String(agentId)).run();
                }
            }
        }

        // 返回创建成功的群组信息
        const groupInfo = {
            id: groupId,
            name: body.name,
            description: body.description || '',
            group_type: body.group_type,
            max_members: body.max_members || 50,
            created_by: userId,
            agent_ids: body.agent_ids || [],
            user_ids: body.user_ids || [],
            created_at: new Date().toISOString()
        };

        return new Response(
            JSON.stringify({
                success: true,
                message: '群组创建成功',
                data: groupInfo
            }),
            {
                status: 201,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('群组创建失败:', error);
        console.error('错误详情:', error.message);
        console.error('错误堆栈:', error.stack);
        return new Response(
            JSON.stringify({
                success: false,
                message: '服务器错误，请稍后重试',
                error: error.message,
                stack: error.stack
            }),
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
};

// 获取群组详细信息
async function getGroupInfo(db: D1Database, groupId: number) {
    // 获取群组基本信息
    const group = await db.prepare(`
        SELECT * FROM groups_new WHERE id = ?
    `).bind(groupId).first();

    // 获取成员信息
    const members = await db.prepare(`
        SELECT 
            gm.*,
            CASE 
                WHEN gm.member_type = 'user' THEN u.nickname
                WHEN gm.member_type = 'agent' THEN a.display_name
            END as member_name,
            CASE 
                WHEN gm.member_type = 'user' THEN u.avatar_url
                WHEN gm.member_type = 'agent' THEN a.avatar_url
            END as avatar_url
        FROM group_members gm
        LEFT JOIN users u ON gm.member_type = 'user' AND gm.member_id = u.id
        LEFT JOIN agents a ON gm.member_type = 'agent' AND gm.member_id = a.id
        WHERE gm.group_id = ?
        ORDER BY gm.joined_at
    `).bind(groupId).all();

    // 分类成员
    const users = members.results.filter(m => m.member_type === 'user');
    const agents = members.results.filter(m => m.member_type === 'agent');

    return {
        ...group,
        member_count: members.results.length,
        user_count: users.length,
        agent_count: agents.length,
        members: {
            users: users.map(u => ({
                id: u.member_id,
                name: u.member_name,
                avatar_url: u.avatar_url,
                role: u.role,
                is_muted: u.is_muted,
                joined_at: u.joined_at
            })),
            agents: agents.map(a => ({
                id: a.member_id,
                name: a.member_name,
                avatar_url: a.avatar_url,
                role: a.role,
                is_muted: a.is_muted,
                joined_at: a.joined_at
            }))
        }
    };
}
