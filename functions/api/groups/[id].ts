interface Env {
    bgdb: D1Database;
}

// 获取群聊详情
export const onRequestGet: PagesFunction<Env> = async (context) => {
    try {
        const { env, data, request } = context;
        const url = new URL(request.url);
        const groupId = url.pathname.split('/').pop();
        
        if (!groupId || isNaN(parseInt(groupId))) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '无效的群聊ID' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const db = env.bgdb;
        const userId = data.user?.userId;

        if (!userId) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '用户未登录' 
                }), 
                { status: 401, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 获取群聊基本信息
        const group = await db.prepare(`
            SELECT * FROM groups_new WHERE id = ?
        `).bind(groupId).first();

        if (!group) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '群聊不存在' 
                }), 
                { status: 404, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 检查用户是否是群聊成员
        const membership = await db.prepare(`
            SELECT * FROM group_members 
            WHERE group_id = ? AND member_id = ? AND member_type = 'user'
        `).bind(groupId, userId).first();

        if (!membership) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '没有权限访问此群聊' 
                }), 
                { status: 403, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 获取成员信息
        const members = await db.prepare(`
            SELECT 
                gm.*,
                CASE 
                    WHEN gm.member_type = 'user' THEN u.nickname
                    WHEN gm.member_type = 'agent' THEN a.display_name
                END as member_name,
                CASE 
                    WHEN gm.member_type = 'user' THEN u.avatar_url
                    WHEN gm.member_type = 'agent' THEN a.avatar_url
                END as avatar_url
            FROM group_members gm
            LEFT JOIN users u ON gm.member_type = 'user' AND gm.member_id = u.id
            LEFT JOIN agents a ON gm.member_type = 'agent' AND gm.member_id = a.id
            WHERE gm.group_id = ?
            ORDER BY gm.joined_at
        `).bind(groupId).all();

        // 获取最近消息
        const recentMessages = await db.prepare(`
            SELECT 
                m.*,
                CASE 
                    WHEN m.sender_type = 'user' THEN u.nickname
                    WHEN m.sender_type = 'agent' THEN a.display_name
                END as sender_name
            FROM messages m
            LEFT JOIN users u ON m.sender_type = 'user' AND m.sender_id = u.id
            LEFT JOIN agents a ON m.sender_type = 'agent' AND m.sender_id = a.id
            WHERE m.group_id = ?
            ORDER BY m.created_at DESC
            LIMIT 50
        `).bind(groupId).all();

        const groupInfo = {
            ...group,
            members: members.results,
            recent_messages: recentMessages.results.reverse() // 按时间正序排列
        };

        return new Response(
            JSON.stringify({
                success: true,
                data: groupInfo
            }),
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('获取群聊详情失败:', error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误，请稍后重试' 
            }), 
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
};

// 删除群聊
export const onRequestDelete: PagesFunction<Env> = async (context) => {
    try {
        const { env, data, request } = context;
        const url = new URL(request.url);
        const groupId = url.pathname.split('/').pop();
        
        if (!groupId || isNaN(parseInt(groupId))) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '无效的群聊ID' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const db = env.bgdb;
        const userId = data.user?.userId;

        if (!userId) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '用户未登录' 
                }), 
                { status: 401, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 检查群聊是否存在
        const group = await db.prepare(`
            SELECT * FROM groups_new WHERE id = ?
        `).bind(groupId).first();

        if (!group) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '群聊不存在' 
                }), 
                { status: 404, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 检查权限：只有群聊创建者或管理员可以删除
        const membership = await db.prepare(`
            SELECT * FROM group_members 
            WHERE group_id = ? AND member_id = ? AND member_type = 'user' 
            AND (role = 'admin' OR role = 'owner')
        `).bind(groupId, userId).first();

        const isCreator = group.created_by === userId;

        if (!membership && !isCreator) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '没有权限删除此群聊' 
                }), 
                { status: 403, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 开始事务删除
        // 1. 删除群聊消息
        await db.prepare(`DELETE FROM messages WHERE group_id = ?`).bind(groupId).run();
        
        // 2. 删除群聊成员
        await db.prepare(`DELETE FROM group_members WHERE group_id = ?`).bind(groupId).run();
        
        // 3. 删除群聊本身
        await db.prepare(`DELETE FROM groups_new WHERE id = ?`).bind(groupId).run();

        return new Response(
            JSON.stringify({
                success: true,
                message: '群聊删除成功'
            }),
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('删除群聊失败:', error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误，请稍后重试' 
            }), 
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
};
