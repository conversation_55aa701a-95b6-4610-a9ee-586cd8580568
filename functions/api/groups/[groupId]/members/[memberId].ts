interface Env {
    bgdb: D1Database;
}

interface UpdateMemberRequest {
    member_type: 'user' | 'agent';
    role?: string;
}

// 更新成员角色
export const onRequestPut: PagesFunction<Env> = async (context) => {
    try {
        const { env, data, request } = context;
        const url = new URL(request.url);
        const pathParts = url.pathname.split('/');
        const groupId = pathParts[pathParts.indexOf('groups') + 1];
        const memberId = pathParts[pathParts.indexOf('members') + 1];
        
        const body: UpdateMemberRequest = await request.json();
        
        if (!groupId || !memberId || !body.member_type) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '缺少必填字段：groupId, memberId, member_type' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const db = env.bgdb;
        const userId = data.user?.userId;

        if (!userId) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '用户未登录' 
                }), 
                { status: 401, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 验证用户是否有管理权限
        const currentMember = await db.prepare(`
            SELECT role FROM group_members 
            WHERE group_id = ? AND member_id = ? AND member_type = 'user'
        `).bind(groupId, String(userId)).first();

        if (!currentMember || (currentMember.role !== 'admin' && currentMember.role !== 'owner')) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '没有管理权限' 
                }), 
                { status: 403, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 获取目标成员信息
        const targetMember = await db.prepare(`
            SELECT * FROM group_members 
            WHERE group_id = ? AND member_id = ? AND member_type = ?
        `).bind(groupId, memberId, body.member_type).first();

        if (!targetMember) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '成员不存在' 
                }), 
                { status: 404, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 权限检查：只有群主可以设置其他群主，管理员不能操作群主
        if (body.role === 'owner' && currentMember.role !== 'owner') {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '只有群主可以转让群主权限' 
                }), 
                { status: 403, headers: { 'Content-Type': 'application/json' } }
            );
        }

        if (targetMember.role === 'owner' && currentMember.role !== 'owner') {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '管理员无法操作群主' 
                }), 
                { status: 403, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 如果要设置新的群主，需要将当前群主降级
        if (body.role === 'owner') {
            await db.prepare(`
                UPDATE group_members 
                SET role = 'admin' 
                WHERE group_id = ? AND role = 'owner' AND member_type = 'user'
            `).bind(groupId).run();
        }

        // 更新成员角色
        await db.prepare(`
            UPDATE group_members 
            SET role = ? 
            WHERE group_id = ? AND member_id = ? AND member_type = ?
        `).bind(body.role || 'member', groupId, memberId, body.member_type).run();

        return new Response(
            JSON.stringify({
                success: true,
                message: '成员角色更新成功'
            }),
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('更新成员角色失败:', error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误，请稍后重试' 
            }), 
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
};

// 移除成员
export const onRequestDelete: PagesFunction<Env> = async (context) => {
    try {
        const { env, data, request } = context;
        const url = new URL(request.url);
        const pathParts = url.pathname.split('/');
        const groupId = pathParts[pathParts.indexOf('groups') + 1];
        const memberId = pathParts[pathParts.indexOf('members') + 1];
        
        const body: UpdateMemberRequest = await request.json();
        
        if (!groupId || !memberId || !body.member_type) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '缺少必填字段：groupId, memberId, member_type' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const db = env.bgdb;
        const userId = data.user?.userId;

        if (!userId) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '用户未登录' 
                }), 
                { status: 401, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 验证用户是否有管理权限
        const currentMember = await db.prepare(`
            SELECT role FROM group_members 
            WHERE group_id = ? AND member_id = ? AND member_type = 'user'
        `).bind(groupId, String(userId)).first();

        if (!currentMember || (currentMember.role !== 'admin' && currentMember.role !== 'owner')) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '没有管理权限' 
                }), 
                { status: 403, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 获取目标成员信息
        const targetMember = await db.prepare(`
            SELECT * FROM group_members 
            WHERE group_id = ? AND member_id = ? AND member_type = ?
        `).bind(groupId, memberId, body.member_type).first();

        if (!targetMember) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '成员不存在' 
                }), 
                { status: 404, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 权限检查：管理员不能移除群主
        if (targetMember.role === 'owner' && currentMember.role !== 'owner') {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '管理员无法移除群主' 
                }), 
                { status: 403, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 不能移除自己
        if (body.member_type === 'user' && memberId === String(userId)) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '不能移除自己，请使用退出群聊功能' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 移除成员
        await db.prepare(`
            DELETE FROM group_members 
            WHERE group_id = ? AND member_id = ? AND member_type = ?
        `).bind(groupId, memberId, body.member_type).run();

        return new Response(
            JSON.stringify({
                success: true,
                message: '成员移除成功'
            }),
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('移除成员失败:', error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误，请稍后重试' 
            }), 
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
};
