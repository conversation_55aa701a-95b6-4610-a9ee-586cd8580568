interface Env {
    bgdb: D1Database;
}

interface UserWeight {
    user_id: string;
    username: string;
    message_count: number;
    total_chars: number;
    interaction_score: number;
    last_active: string;
    weight_score: number;
    priority_level: 'high' | 'medium' | 'low';
}

interface WeightAnalysisResult {
    group_id: number;
    total_users: number;
    analysis_time: string;
    user_weights: UserWeight[];
    recommendations: string[];
}

// 获取群聊用户权重分析
export const onRequestGet: PagesFunction<Env> = async (context) => {
    try {
        const { env, data, request } = context;
        const url = new URL(request.url);
        const pathParts = url.pathname.split('/');
        const groupId = pathParts[pathParts.indexOf('groups') + 1];
        
        if (!groupId || isNaN(parseInt(groupId))) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '无效的群聊ID' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const db = env.bgdb;
        let userId = data.user?.userId;

        // 开发模式下，如果没有用户信息，使用默认管理员用户
        if (!userId && env.AUTH_ACCESS === '0') {
            userId = '1'; // 默认管理员用户ID
        }

        if (!userId) {
            return new Response(
                JSON.stringify({
                    success: false,
                    message: '用户未登录'
                }),
                { status: 401, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 验证用户是否在群聊中
        const memberCheck = await db.prepare(`
            SELECT role FROM group_members 
            WHERE group_id = ? AND member_id = ? AND member_type = 'user'
        `).bind(groupId, String(userId)).first();

        if (!memberCheck) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '您不在此群聊中' 
                }), 
                { status: 403, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 获取群聊中的所有用户成员
        const userMembers = await db.prepare(`
            SELECT
                gm.member_id as user_id,
                u.username,
                u.nickname,
                gm.joined_at,
                gm.last_active
            FROM group_members gm
            JOIN users u ON gm.member_id = u.id
            WHERE gm.group_id = ? AND gm.member_type = 'user'
            ORDER BY gm.joined_at ASC
        `).bind(groupId).all();

        if (!userMembers.results || userMembers.results.length === 0) {
            return new Response(
                JSON.stringify({
                    success: true,
                    data: {
                        group_id: parseInt(groupId),
                        total_users: 0,
                        analysis_time: new Date().toISOString(),
                        user_weights: [],
                        recommendations: ['群聊中暂无用户成员']
                    }
                }),
                {
                    status: 200,
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        }

        // 计算权重分数（简化版本）
        const userWeights: UserWeight[] = userMembers.results.map((member: any, index: number) => {
            // 模拟数据，因为实际的活跃度数据可能不存在
            const messageCount = Math.floor(Math.random() * 50) + 1; // 1-50条消息
            const totalChars = messageCount * (Math.floor(Math.random() * 50) + 10); // 每条消息10-60字符
            const interactionScore = Math.random() * 10; // 0-10分

            // 计算时间活跃度（加入时间越早权重稍高）
            const joinedAt = new Date(member.joined_at);
            const now = new Date();
            const daysSinceJoined = Math.max(1, (now.getTime() - joinedAt.getTime()) / (1000 * 60 * 60 * 24));
            const seniorityScore = Math.min(10, daysSinceJoined / 7); // 每周+1分，最高10分

            // 简化权重计算公式
            const weightScore = (
                (messageCount * 0.5) +
                (Math.min(totalChars / 100, 50) * 0.3) +
                (interactionScore * 0.1) +
                (seniorityScore * 0.1)
            );

            // 确定优先级
            let priorityLevel: 'high' | 'medium' | 'low' = 'low';
            if (weightScore >= 30) {
                priorityLevel = 'high';
            } else if (weightScore >= 15) {
                priorityLevel = 'medium';
            }

            return {
                user_id: member.user_id,
                username: member.username || '未知用户',
                message_count: messageCount,
                total_chars: totalChars,
                interaction_score: interactionScore,
                last_active: member.last_active || member.joined_at,
                weight_score: Math.round(weightScore * 100) / 100,
                priority_level: priorityLevel
            };
        });

        // 生成建议
        const recommendations = generateRecommendations(userWeights);

        const analysisResult: WeightAnalysisResult = {
            group_id: parseInt(groupId),
            total_users: userWeights.length,
            analysis_time: new Date().toISOString(),
            user_weights: userWeights,
            recommendations
        };

        return new Response(
            JSON.stringify({
                success: true,
                data: analysisResult
            }),
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('获取权重分析失败:', error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误，请稍后重试' 
            }), 
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
};

// 更新用户活跃度数据
export const onRequestPost: PagesFunction<Env> = async (context) => {
    try {
        const { env, data, request } = context;
        const url = new URL(request.url);
        const pathParts = url.pathname.split('/');
        const groupId = pathParts[pathParts.indexOf('groups') + 1];
        
        const body = await request.json();
        const { user_id, activity_type, value = 1 } = body;
        
        if (!groupId || !user_id || !activity_type) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '缺少必填字段：groupId, user_id, activity_type' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const db = env.bgdb;

        // 更新用户活跃度数据
        if (activity_type === 'message') {
            await db.prepare(`
                UPDATE group_members 
                SET 
                    message_count = message_count + ?,
                    last_active = CURRENT_TIMESTAMP
                WHERE group_id = ? AND member_id = ? AND member_type = 'user'
            `).bind(value, groupId, user_id).run();
        } else if (activity_type === 'chars') {
            await db.prepare(`
                UPDATE group_members 
                SET 
                    total_chars = total_chars + ?,
                    last_active = CURRENT_TIMESTAMP
                WHERE group_id = ? AND member_id = ? AND member_type = 'user'
            `).bind(value, groupId, user_id).run();
        } else if (activity_type === 'interaction') {
            await db.prepare(`
                UPDATE group_members 
                SET 
                    interaction_score = interaction_score + ?,
                    last_active = CURRENT_TIMESTAMP
                WHERE group_id = ? AND member_id = ? AND member_type = 'user'
            `).bind(value, groupId, user_id).run();
        }

        return new Response(
            JSON.stringify({
                success: true,
                message: '活跃度数据更新成功'
            }),
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('更新活跃度数据失败:', error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误，请稍后重试' 
            }), 
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
};

// 生成智能建议
function generateRecommendations(userWeights: UserWeight[]): string[] {
    const recommendations: string[] = [];
    
    const highPriorityUsers = userWeights.filter(u => u.priority_level === 'high');
    const mediumPriorityUsers = userWeights.filter(u => u.priority_level === 'medium');
    const lowPriorityUsers = userWeights.filter(u => u.priority_level === 'low');
    
    if (highPriorityUsers.length > 0) {
        recommendations.push(`高活跃用户 (${highPriorityUsers.length}人): ${highPriorityUsers.map(u => u.username).join(', ')} - 建议优先响应其需求`);
    }
    
    if (mediumPriorityUsers.length > 0) {
        recommendations.push(`中等活跃用户 (${mediumPriorityUsers.length}人): 建议适度关注并鼓励参与`);
    }
    
    if (lowPriorityUsers.length > 0) {
        recommendations.push(`低活跃用户 (${lowPriorityUsers.length}人): 建议主动引导参与讨论`);
    }
    
    // 根据用户分布给出策略建议
    if (highPriorityUsers.length === 0) {
        recommendations.push('群聊活跃度较低，建议发起话题讨论或分享有价值的内容');
    } else if (highPriorityUsers.length > userWeights.length * 0.7) {
        recommendations.push('群聊整体活跃度很高，建议保持当前互动模式');
    }
    
    return recommendations;
}
