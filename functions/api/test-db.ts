interface Env {
    bgdb: D1Database;
    AUTH_ACCESS: string;
}

export const onRequestGet: PagesFunction<Env> = async (context) => {
    try {
        const { env } = context;
        const db = env.bgdb;
        
        // 测试数据库连接
        const result = await db.prepare(
            "SELECT * FROM users"
        ).all();
        
        return new Response(JSON.stringify({
            success: true,
            tables: result,
            env: Object.keys(env)
        }), {
            headers: { 'Content-Type': 'application/json' }
        });
    } catch (error) {
        return new Response(JSON.stringify({
            success: false,
            error: error.message,
            stack: error.stack
        }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        });
    }
};

export const onRequestPost: PagesFunction<Env> = async (context) => {
    try {
        const { env, request } = context;
        const db = env.bgdb;

        // 只在开发模式下允许
        if (env.AUTH_ACCESS !== '0') {
            return new Response(
                JSON.stringify({
                    success: false,
                    message: '此API仅在开发模式下可用'
                }),
                { status: 403, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const body = await request.json();
        const { action, data } = body;

        switch (action) {
            case 'insert_user':
                return await insertUser(db, data);
            case 'insert_agent':
                return await insertAgent(db, data);
            case 'add_group_member':
                return await addGroupMember(db, data);
            case 'add_message':
                return await addMessage(db, data);
            default:
                return new Response(
                    JSON.stringify({
                        success: false,
                        message: '未知的操作类型'
                    }),
                    { status: 400, headers: { 'Content-Type': 'application/json' } }
                );
        }

    } catch (error) {
        console.error('测试数据API错误:', error);
        return new Response(
            JSON.stringify({
                success: false,
                message: '服务器错误，请稍后重试'
            }),
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
};

async function insertUser(db: D1Database, userData: any) {
    try {
        // 检查用户是否已存在
        const existingUser = await db.prepare(`
            SELECT id FROM users WHERE username = ? OR phone = ?
        `).bind(userData.username, userData.phone).first();

        if (existingUser) {
            return new Response(
                JSON.stringify({
                    success: false,
                    message: '用户已存在'
                }),
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 插入新用户
        const result = await db.prepare(`
            INSERT INTO users (username, password_hash, email, nickname, phone, role, status, balance, created_at)
            VALUES (?, ?, ?, ?, ?, 'user', 1, 1000, datetime('now'))
        `).bind(
            userData.username,
            userData.password, // 在实际应用中应该加密
            `${userData.username}@example.com`,
            userData.nickname,
            userData.phone
        ).run();

        return new Response(
            JSON.stringify({
                success: true,
                data: { id: result.meta.last_row_id }
            }),
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('插入用户失败:', error);
        return new Response(
            JSON.stringify({
                success: false,
                message: '插入用户失败'
            }),
            { status: 500, headers: { 'Content-Type': 'application/json' } }
        );
    }
}

async function insertAgent(db: D1Database, agentData: any) {
    try {
        // 检查智能体是否已存在
        const existingAgent = await db.prepare(`
            SELECT id FROM agents WHERE name = ?
        `).bind(agentData.name).first();

        if (existingAgent) {
            return new Response(
                JSON.stringify({
                    success: false,
                    message: '智能体已存在'
                }),
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 插入新智能体
        const result = await db.prepare(`
            INSERT INTO agents (name, display_name, description, api_url, api_key, api_type, avatar_url, status, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, 1, datetime('now'))
        `).bind(
            agentData.name,
            agentData.display_name,
            agentData.description,
            agentData.api_url || 'http://localhost:3000/api/chat',
            agentData.api_key || 'test-key',
            agentData.api_type || 'dify',
            agentData.avatar_url
        ).run();

        return new Response(
            JSON.stringify({
                success: true,
                data: { id: result.meta.last_row_id }
            }),
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('插入智能体失败:', error);
        return new Response(
            JSON.stringify({
                success: false,
                message: '插入智能体失败'
            }),
            { status: 500, headers: { 'Content-Type': 'application/json' } }
        );
    }
}

async function addGroupMember(db: D1Database, memberData: any) {
    try {
        // 检查成员是否已在群聊中
        const existingMember = await db.prepare(`
            SELECT id FROM group_members
            WHERE group_id = ? AND member_id = ? AND member_type = ?
        `).bind(memberData.group_id, memberData.member_id, memberData.member_type).first();

        if (existingMember) {
            return new Response(
                JSON.stringify({
                    success: false,
                    message: '成员已在群聊中'
                }),
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 添加群聊成员
        const result = await db.prepare(`
            INSERT INTO group_members (group_id, member_id, member_type, role, joined_at)
            VALUES (?, ?, ?, ?, datetime('now'))
        `).bind(
            memberData.group_id,
            memberData.member_id,
            memberData.member_type,
            memberData.role || 'member'
        ).run();

        return new Response(
            JSON.stringify({
                success: true,
                data: { id: result.meta.last_row_id }
            }),
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('添加群聊成员失败:', error);
        return new Response(
            JSON.stringify({
                success: false,
                message: '添加群聊成员失败'
            }),
            { status: 500, headers: { 'Content-Type': 'application/json' } }
        );
    }
}

async function addMessage(db: D1Database, messageData: any) {
    try {
        // 插入消息
        const result = await db.prepare(`
            INSERT INTO messages (group_id, sender_id, sender_type, content, message_type, created_at)
            VALUES (?, ?, ?, ?, ?, datetime('now'))
        `).bind(
            messageData.group_id,
            messageData.sender_id,
            messageData.sender_type,
            messageData.content,
            messageData.message_type || 'text'
        ).run();

        // 暂时跳过用户活跃度数据更新，因为表不存在
        // TODO: 在数据库初始化中添加 user_activity_stats 表

        return new Response(
            JSON.stringify({
                success: true,
                data: { id: result.meta.last_row_id }
            }),
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('添加消息失败:', error);
        return new Response(
            JSON.stringify({
                success: false,
                message: '添加消息失败'
            }),
            { status: 500, headers: { 'Content-Type': 'application/json' } }
        );
    }
}