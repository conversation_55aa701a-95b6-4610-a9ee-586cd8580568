interface Env {
    bgdb: D1Database;
}

interface TaskUpdateRequest {
    task_id: number;
    status?: 'pending' | 'in_progress' | 'completed' | 'failed';
    result?: string;
    priority?: number;
    agent_feedback?: string;
}

interface TaskReassignRequest {
    task_id: number;
    new_agent_id: number;
    reason?: string;
}

// 更新任务状态
export const onRequestPut: PagesFunction<Env> = async (context) => {
    try {
        const { env, request } = context;
        const body: TaskUpdateRequest = await request.json();
        
        if (!body.task_id) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '缺少task_id参数' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const db = env.bgdb;

        // 检查任务是否存在
        const existingTask = await db.prepare(`
            SELECT * FROM task_assignments WHERE id = ?
        `).bind(body.task_id).first();

        if (!existingTask) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '任务不存在' 
                }), 
                { status: 404, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 构建更新SQL
        const updateFields = [];
        const updateParams = [];

        if (body.status !== undefined) {
            updateFields.push('status = ?');
            updateParams.push(body.status);
            
            // 如果状态变为完成或失败，设置完成时间
            if (body.status === 'completed' || body.status === 'failed') {
                updateFields.push('completed_at = CURRENT_TIMESTAMP');
            }
        }

        if (body.result !== undefined) {
            updateFields.push('result = ?');
            updateParams.push(body.result);
        }

        if (body.priority !== undefined) {
            updateFields.push('priority = ?');
            updateParams.push(body.priority);
        }

        if (updateFields.length === 0) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '没有需要更新的字段' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        updateParams.push(body.task_id);

        const updateSQL = `
            UPDATE task_assignments 
            SET ${updateFields.join(', ')}
            WHERE id = ?
        `;

        await db.prepare(updateSQL).bind(...updateParams).run();

        // 如果任务完成，更新Agent性能统计
        if (body.status === 'completed' || body.status === 'failed') {
            await updateAgentPerformanceFromTask(db, existingTask, body.status === 'completed');
        }

        // 获取更新后的任务信息
        const updatedTask = await db.prepare(`
            SELECT 
                ta.*,
                a.display_name as agent_name,
                g.name as group_name
            FROM task_assignments ta
            LEFT JOIN agents a ON ta.assigned_agent_id = a.id
            LEFT JOIN groups_new g ON ta.group_id = g.id
            WHERE ta.id = ?
        `).bind(body.task_id).first();

        return new Response(
            JSON.stringify({ 
                success: true,
                message: '任务更新成功',
                data: updatedTask
            }), 
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('任务更新失败:', error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误，请稍后重试' 
            }), 
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
};

// 重新分配任务
export const onRequestPost: PagesFunction<Env> = async (context) => {
    try {
        const { env, request } = context;
        const body: TaskReassignRequest = await request.json();
        
        if (!body.task_id || !body.new_agent_id) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '缺少必填参数：task_id, new_agent_id' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const db = env.bgdb;

        // 检查任务是否存在且可以重新分配
        const existingTask = await db.prepare(`
            SELECT * FROM task_assignments WHERE id = ?
        `).bind(body.task_id).first();

        if (!existingTask) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '任务不存在' 
                }), 
                { status: 404, headers: { 'Content-Type': 'application/json' } }
            );
        }

        if (existingTask.status === 'completed') {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '已完成的任务不能重新分配' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 检查新Agent是否存在且可用
        const newAgent = await db.prepare(`
            SELECT * FROM agents WHERE id = ? AND status = 1
        `).bind(body.new_agent_id).first();

        if (!newAgent) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '目标Agent不存在或不可用' 
                }), 
                { status: 404, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 更新任务分配
        await db.prepare(`
            UPDATE task_assignments 
            SET assigned_agent_id = ?, status = 'pending', assigned_at = CURRENT_TIMESTAMP
            WHERE id = ?
        `).bind(body.new_agent_id, body.task_id).run();

        // 记录重新分配历史（可选）
        if (body.reason) {
            await db.prepare(`
                INSERT INTO messages (group_id, sender_id, sender_type, content, message_type, metadata)
                VALUES (?, 1, 'agent', ?, 'task_reassign', ?)
            `).bind(
                existingTask.group_id,
                `任务已重新分配给 ${newAgent.display_name}`,
                JSON.stringify({
                    task_id: body.task_id,
                    old_agent_id: existingTask.assigned_agent_id,
                    new_agent_id: body.new_agent_id,
                    reason: body.reason
                })
            ).run();
        }

        // 获取更新后的任务信息
        const updatedTask = await db.prepare(`
            SELECT 
                ta.*,
                a.display_name as agent_name,
                g.name as group_name
            FROM task_assignments ta
            LEFT JOIN agents a ON ta.assigned_agent_id = a.id
            LEFT JOIN groups_new g ON ta.group_id = g.id
            WHERE ta.id = ?
        `).bind(body.task_id).first();

        return new Response(
            JSON.stringify({ 
                success: true,
                message: `任务已重新分配给 ${newAgent.display_name}`,
                data: updatedTask
            }), 
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('任务重新分配失败:', error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误，请稍后重试' 
            }), 
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
};

// 取消任务
export const onRequestDelete: PagesFunction<Env> = async (context) => {
    try {
        const { env, request } = context;
        const url = new URL(request.url);
        const taskId = url.pathname.split('/').pop();
        
        if (!taskId || isNaN(parseInt(taskId))) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '无效的任务ID' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const db = env.bgdb;

        // 检查任务是否存在
        const existingTask = await db.prepare(`
            SELECT * FROM task_assignments WHERE id = ?
        `).bind(taskId).first();

        if (!existingTask) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '任务不存在' 
                }), 
                { status: 404, headers: { 'Content-Type': 'application/json' } }
            );
        }

        if (existingTask.status === 'completed') {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '已完成的任务不能取消' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 更新任务状态为失败
        await db.prepare(`
            UPDATE task_assignments 
            SET status = 'failed', completed_at = CURRENT_TIMESTAMP, result = '任务已取消'
            WHERE id = ?
        `).bind(taskId).run();

        return new Response(
            JSON.stringify({ 
                success: true,
                message: '任务已取消'
            }), 
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('任务取消失败:', error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误，请稍后重试' 
            }), 
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
};

// 从任务更新Agent性能统计
async function updateAgentPerformanceFromTask(db: D1Database, task: any, isCompleted: boolean) {
    const today = new Date().toISOString().split('T')[0];
    const duration = task.completed_at ? 
        new Date(task.completed_at).getTime() - new Date(task.assigned_at).getTime() : 0;

    // 获取今日现有统计
    const existing = await db.prepare(`
        SELECT * FROM agent_performance WHERE agent_id = ? AND date = ?
    `).bind(task.assigned_agent_id, today).first();

    if (existing) {
        // 更新现有记录
        const newCompletedTasks = existing.completed_tasks + (isCompleted ? 1 : 0);
        const newFailedTasks = existing.failed_tasks + (isCompleted ? 0 : 1);
        const newTotalTasks = existing.total_tasks;

        let newAvgResponseTime = existing.avg_response_time;
        if (duration > 0) {
            const totalResponseTime = existing.avg_response_time * existing.total_tasks;
            newAvgResponseTime = (totalResponseTime + duration) / newTotalTasks;
        }

        await db.prepare(`
            UPDATE agent_performance SET
                completed_tasks = ?,
                failed_tasks = ?,
                avg_response_time = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE agent_id = ? AND date = ?
        `).bind(
            newCompletedTasks,
            newFailedTasks,
            newAvgResponseTime,
            task.assigned_agent_id,
            today
        ).run();
    }
}
