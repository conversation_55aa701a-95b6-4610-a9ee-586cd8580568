interface Env {
    bgdb: D1Database;
}

interface TaskListQuery {
    group_id?: number;
    agent_id?: number;
    status?: string;
    page?: number;
    limit?: number;
    start_date?: string;
    end_date?: string;
}

export const onRequestGet: PagesFunction<Env> = async (context) => {
    try {
        const { env, request } = context;
        const url = new URL(request.url);
        
        // 解析查询参数
        const query: TaskListQuery = {
            group_id: url.searchParams.get('group_id') ? parseInt(url.searchParams.get('group_id')!) : undefined,
            agent_id: url.searchParams.get('agent_id') ? parseInt(url.searchParams.get('agent_id')!) : undefined,
            status: url.searchParams.get('status') || undefined,
            page: parseInt(url.searchParams.get('page') || '1'),
            limit: Math.min(parseInt(url.searchParams.get('limit') || '20'), 100),
            start_date: url.searchParams.get('start_date') || undefined,
            end_date: url.searchParams.get('end_date') || undefined
        };

        const db = env.bgdb;
        let whereConditions = ['1=1'];
        let params: any[] = [];

        // 构建查询条件
        if (query.group_id) {
            whereConditions.push('ta.group_id = ?');
            params.push(query.group_id);
        }

        if (query.agent_id) {
            whereConditions.push('ta.assigned_agent_id = ?');
            params.push(query.agent_id);
        }

        if (query.status) {
            whereConditions.push('ta.status = ?');
            params.push(query.status);
        }

        if (query.start_date) {
            whereConditions.push('ta.assigned_at >= ?');
            params.push(query.start_date);
        }

        if (query.end_date) {
            whereConditions.push('ta.assigned_at <= ?');
            params.push(query.end_date);
        }

        // 计算偏移量
        const offset = (query.page! - 1) * query.limit!;

        // 查询任务列表
        const tasksQuery = `
            SELECT 
                ta.*,
                a.display_name as agent_name,
                a.avatar_url as agent_avatar,
                g.name as group_name,
                m.content as trigger_message,
                u.nickname as trigger_user_name
            FROM task_assignments ta
            LEFT JOIN agents a ON ta.assigned_agent_id = a.id
            LEFT JOIN groups_new g ON ta.group_id = g.id
            LEFT JOIN messages m ON ta.message_id = m.id
            LEFT JOIN users u ON m.sender_id = u.id AND m.sender_type = 'user'
            WHERE ${whereConditions.join(' AND ')}
            ORDER BY ta.assigned_at DESC
            LIMIT ? OFFSET ?
        `;

        const tasks = await db.prepare(tasksQuery)
            .bind(...params, query.limit, offset)
            .all();

        // 查询总数
        const countQuery = `
            SELECT COUNT(*) as total
            FROM task_assignments ta
            WHERE ${whereConditions.join(' AND ')}
        `;

        const countResult = await db.prepare(countQuery)
            .bind(...params)
            .first();

        // 处理任务数据
        const processedTasks = tasks.results.map(task => ({
            ...task,
            duration: task.completed_at ? 
                new Date(task.completed_at).getTime() - new Date(task.assigned_at).getTime() : null,
            is_overdue: task.status === 'pending' && 
                new Date().getTime() - new Date(task.assigned_at).getTime() > 300000 // 5分钟超时
        }));

        // 统计信息
        const stats = await getTaskStats(db, whereConditions, params);

        return new Response(
            JSON.stringify({ 
                success: true,
                data: {
                    tasks: processedTasks,
                    pagination: {
                        page: query.page,
                        limit: query.limit,
                        total: countResult.total,
                        pages: Math.ceil(countResult.total / query.limit!)
                    },
                    stats: stats
                }
            }), 
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('获取任务列表失败:', error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误，请稍后重试' 
            }), 
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
};

// 获取任务统计信息
async function getTaskStats(db: D1Database, whereConditions: string[], params: any[]) {
    const statsQuery = `
        SELECT 
            COUNT(*) as total_tasks,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_tasks,
            COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_tasks,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_tasks,
            COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_tasks,
            AVG(CASE 
                WHEN status = 'completed' AND completed_at IS NOT NULL 
                THEN (julianday(completed_at) - julianday(assigned_at)) * 24 * 60 * 60 * 1000 
            END) as avg_completion_time,
            COUNT(CASE 
                WHEN status = 'pending' AND (julianday('now') - julianday(assigned_at)) * 24 * 60 * 60 > 300 
                THEN 1 
            END) as overdue_tasks
        FROM task_assignments ta
        WHERE ${whereConditions.join(' AND ')}
    `;

    const stats = await db.prepare(statsQuery).bind(...params).first();

    return {
        total_tasks: stats.total_tasks || 0,
        pending_tasks: stats.pending_tasks || 0,
        in_progress_tasks: stats.in_progress_tasks || 0,
        completed_tasks: stats.completed_tasks || 0,
        failed_tasks: stats.failed_tasks || 0,
        overdue_tasks: stats.overdue_tasks || 0,
        success_rate: stats.total_tasks > 0 ? 
            Math.round((stats.completed_tasks / stats.total_tasks) * 100) : 0,
        avg_completion_time: stats.avg_completion_time ? Math.round(stats.avg_completion_time) : 0
    };
}
