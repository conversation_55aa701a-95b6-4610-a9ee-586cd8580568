interface Env {
    bgdb: D1Database;
}

export const onRequestGet: PagesFunction<Env> = async (context) => {
    try {
        const { env } = context;
        const db = env.bgdb;

        // 检查关键表是否存在
        const groupsTableExists = await db.prepare(`
            SELECT name FROM sqlite_master WHERE type='table' AND name='groups_new';
        `).first();

        const messagesTableExists = await db.prepare(`
            SELECT name FROM sqlite_master WHERE type='table' AND name='messages';
        `).first();

        const usersTableExists = await db.prepare(`
            SELECT name FROM sqlite_master WHERE type='table' AND name='users';
        `).first();

        if (!groupsTableExists || !messagesTableExists || !usersTableExists) {
            // 执行完整的数据库初始化
            console.log('开始创建数据库表...');

            // 删除所有现有表（强制重建）
            try {
                await db.prepare('DROP TABLE IF EXISTS agents').run();
                await db.prepare('DROP TABLE IF EXISTS groups_new').run();
                await db.prepare('DROP TABLE IF EXISTS groups').run();
                await db.prepare('DROP TABLE IF EXISTS messages').run();
                await db.prepare('DROP TABLE IF EXISTS users').run();
                await db.prepare('DROP TABLE IF EXISTS departments').run();
                await db.prepare('DROP TABLE IF EXISTS recharge_keys').run();
                await db.prepare('DROP TABLE IF EXISTS billing_transactions').run();
                await db.prepare('DROP TABLE IF EXISTS currency_transactions').run();
                console.log('已删除所有现有表');
            } catch (error) {
                console.log('删除表时出错（可能表不存在）:', error);
            }

            // 创建agents表
            await db.prepare(`
                CREATE TABLE IF NOT EXISTS agents (
                  id INTEGER PRIMARY KEY AUTOINCREMENT,
                  name VARCHAR(100) NOT NULL UNIQUE,
                  display_name VARCHAR(100) NOT NULL,
                  description TEXT,
                  api_url TEXT NOT NULL,
                  api_key TEXT,
                  api_type VARCHAR(50) DEFAULT 'dify',
                  avatar_url TEXT,
                  status INTEGER DEFAULT 1,
                  is_super_agent BOOLEAN DEFAULT FALSE,
                  created_by INTEGER,
                  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                  last_used_at TIMESTAMP
                );
            `).run();

            // 创建groups_new表
            await db.prepare(`
                CREATE TABLE IF NOT EXISTS groups_new (
                  id INTEGER PRIMARY KEY AUTOINCREMENT,
                  name VARCHAR(200) NOT NULL,
                  description TEXT,
                  group_type VARCHAR(50) DEFAULT 'mixed',
                  max_members INTEGER DEFAULT 50,
                  created_by INTEGER,
                  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            `).run();

            // 创建group_members表
            await db.prepare(`
                CREATE TABLE IF NOT EXISTS group_members (
                  id INTEGER PRIMARY KEY AUTOINCREMENT,
                  group_id INTEGER NOT NULL,
                  member_id TEXT NOT NULL,
                  member_type VARCHAR(20) NOT NULL DEFAULT 'agent',
                  role VARCHAR(20) DEFAULT 'member',
                  joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                  invited_by TEXT,
                  last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                  message_count INTEGER DEFAULT 0,
                  total_chars INTEGER DEFAULT 0,
                  interaction_score REAL DEFAULT 0.0,
                  FOREIGN KEY (group_id) REFERENCES groups_new(id) ON DELETE CASCADE,
                  UNIQUE(group_id, member_id, member_type)
                );
            `).run();

            // 创建messages表
            await db.prepare(`
                CREATE TABLE IF NOT EXISTS messages (
                  id INTEGER PRIMARY KEY AUTOINCREMENT,
                  group_id INTEGER NOT NULL,
                  sender_id INTEGER NOT NULL,
                  sender_type VARCHAR(20) NOT NULL DEFAULT 'user',
                  content TEXT NOT NULL,
                  message_type VARCHAR(20) DEFAULT 'text',
                  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                  FOREIGN KEY (group_id) REFERENCES groups_new(id) ON DELETE CASCADE
                );
            `).run();

            // 创建users表
            await db.prepare(`
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    phone VARCHAR(11),
                    nickname VARCHAR(50),
                    avatar_url TEXT,
                    status INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    username TEXT,
                    password_hash TEXT,
                    email TEXT,
                    role TEXT DEFAULT 'user',
                    department_id TEXT,
                    balance REAL DEFAULT 0.0,
                    source TEXT DEFAULT 'local',
                    account_expires_at TIMESTAMP
                );
            `).run();

            // 创建充值密钥表
            await db.prepare(`
                CREATE TABLE IF NOT EXISTS recharge_keys (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key_code TEXT UNIQUE NOT NULL,
                    amount REAL NOT NULL,
                    extend_days INTEGER DEFAULT 30,
                    status INTEGER DEFAULT 0,
                    used_by INTEGER,
                    used_at TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP,
                    FOREIGN KEY (used_by) REFERENCES users(id)
                );
            `).run();

            // 创建交易记录表
            await db.prepare(`
                CREATE TABLE IF NOT EXISTS billing_transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    type TEXT NOT NULL,
                    amount REAL NOT NULL,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                );
            `).run();

            // 创建通知表
            await db.prepare(`
                CREATE TABLE IF NOT EXISTS notifications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    type VARCHAR(50) NOT NULL,
                    title VARCHAR(200) NOT NULL,
                    content TEXT NOT NULL,
                    sender_id INTEGER,
                    group_id INTEGER,
                    group_name VARCHAR(200),
                    action_url TEXT,
                    read_status INTEGER DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id),
                    FOREIGN KEY (sender_id) REFERENCES users(id),
                    FOREIGN KEY (group_id) REFERENCES groups_new(id)
                );
            `).run();

            console.log('数据库表创建完成，开始插入测试数据...');

            // 插入超级智能体
            await db.prepare(`
                INSERT OR IGNORE INTO agents (name, display_name, description, api_url, api_type, is_super_agent, status)
                VALUES ('super-agent', '超级智能体', '负责任务调度和意图理解的核心智能体', 'internal://super-agent', 'internal', TRUE, 1);
            `).run();

            // 插入示例Agent
            await db.prepare(`
                INSERT OR IGNORE INTO agents (name, display_name, description, api_url, api_type, status)
                VALUES
                ('dify-assistant', 'Dify助手', '通用AI助手，支持多种任务', 'https://api.dify.ai/v1', 'dify', 1),
                ('code-helper', '代码助手', '专业的编程和代码分析助手', 'https://api.example.com/code', 'custom', 1);
            `).run();

            // 创建默认群组
            await db.prepare(`
                INSERT OR IGNORE INTO groups_new (name, description, group_type, created_by)
                VALUES ('智能助手群', '包含多个AI助手的混合群聊', 'mixed', 1);
            `).run();

            // 添加群组成员
            await db.prepare(`
                INSERT OR IGNORE INTO group_members (group_id, member_id, member_type, role)
                VALUES
                (1, 1, 'agent', 'member'),
                (1, 2, 'agent', 'member'),
                (1, 3, 'agent', 'member');
            `).run();

            // 插入admin用户
            await db.prepare(`
                INSERT OR IGNORE INTO users (username, password_hash, email, nickname, role, balance, phone, status, account_expires_at)
                VALUES ('admin', 'admin123', '<EMAIL>', '系统管理员', 'admin', 10000.0, '***********', 1, datetime('now', '+1 year'));
            `).run();

            // 插入测试充值密钥
            await db.prepare(`
                INSERT OR IGNORE INTO recharge_keys (key_code, amount, extend_days, status)
                VALUES
                ('RECHARGE100', 100, 30, 0),
                ('RECHARGE500', 500, 90, 0),
                ('RECHARGE1000', 1000, 180, 0),
                ('TESTKEY2024', 200, 60, 0);
            `).run();

            // 创建group_invitations表
            await db.prepare(`
                CREATE TABLE IF NOT EXISTS group_invitations (
                  id TEXT PRIMARY KEY,
                  group_id INTEGER NOT NULL,
                  inviter_id TEXT NOT NULL,
                  invitee_email TEXT,
                  invitee_phone TEXT,
                  invitee_user_id TEXT,
                  invitation_type TEXT DEFAULT 'internal',
                  role TEXT DEFAULT 'member',
                  status TEXT DEFAULT 'pending',
                  invitation_message TEXT,
                  expires_at TIMESTAMP,
                  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                  responded_at TIMESTAMP,
                  FOREIGN KEY (group_id) REFERENCES groups_new(id) ON DELETE CASCADE
                );
            `).run();

            console.log('数据库初始化完成');

            return new Response(
                JSON.stringify({
                    success: true,
                    message: '数据库初始化成功，所有表和测试数据已创建'
                }),
                {
                    status: 200,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                }
            );
        } else {
            return new Response(
                JSON.stringify({
                    success: true,
                    message: '数据库已存在，无需初始化'
                }),
                {
                    status: 200,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                }
            );
        }

    } catch (error) {
        console.error('数据库初始化失败:', error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '数据库初始化失败: ' + error.message 
            }), 
            {
                status: 500,
                headers: {
                    'Content-Type': 'application/json',
                },
            }
        );
    }
};
