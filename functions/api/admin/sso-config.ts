interface Env {
  bgdb: D1Database;
  bgkv: KVNamespace;
  JWT_SECRET: string;
  AUTH_ACCESS: string;
}

// 验证管理员权限
async function verifyAdminAuth(request: Request, env: Env) {
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('未提供认证令牌');
  }

  const token = authHeader.substring(7);

  try {
    const payload = await verifyToken(token, env.JWT_SECRET);
    if (!payload) {
      throw new Error('认证令牌无效');
    }

    const userId = payload.userId;

    const db = env.bgdb;
    const user = await db.prepare(`
      SELECT id, username, role
      FROM users
      WHERE id = ? AND status = 1
    `).bind(userId).first();

    if (!user || user.role !== 'admin') {
      throw new Error('权限不足');
    }

    return user;
  } catch (error) {
    throw new Error('认证失败');
  }
}

interface SSOConfig {
  enabled: boolean;
  type: 'ldap' | 'radius';
  server: string;
  port: number;
  baseDN?: string;
  bindDN?: string;
  bindPassword?: string;
  userFilter?: string;
  secret?: string;
}

export async function onRequestGet(context: { request: Request; env: Env }) {
  const { request, env } = context;

  try {
    // 验证管理员权限
    await verifyAdminAuth(request, env);

    // 从KV存储中获取SSO配置
    const configStr = await env.bgkv.get('sso_config');
    let config: SSOConfig = {
      enabled: false,
      type: 'ldap',
      server: '',
      port: 389,
      baseDN: '',
      bindDN: '',
      bindPassword: '',
      userFilter: '',
      secret: ''
    };

    if (configStr) {
      config = JSON.parse(configStr);
    }

    return new Response(JSON.stringify({
      success: true,
      data: config
    }), {
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('获取SSO配置失败:', error);
    return new Response(JSON.stringify({
      success: false,
      message: error instanceof Error ? error.message : '获取SSO配置失败'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

export async function onRequestPost(context: { request: Request; env: Env }) {
  const { request, env } = context;

  try {
    // 验证管理员权限
    await verifyAdminAuth(request, env);

    const config: SSOConfig = await request.json();

    // 验证配置
    if (config.enabled) {
      if (!config.server || !config.port) {
        return new Response(JSON.stringify({
          success: false,
          message: '服务器地址和端口不能为空'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      if (config.type === 'ldap') {
        if (!config.baseDN || !config.bindDN || !config.bindPassword) {
          return new Response(JSON.stringify({
            success: false,
            message: 'LDAP配置不完整'
          }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          });
        }
      } else if (config.type === 'radius') {
        if (!config.secret) {
          return new Response(JSON.stringify({
            success: false,
            message: 'RADIUS共享密钥不能为空'
          }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          });
        }
      }
    }

    // 保存配置到KV存储
    await env.bgkv.put('sso_config', JSON.stringify(config));

    return new Response(JSON.stringify({
      success: true,
      message: 'SSO配置保存成功'
    }), {
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('保存SSO配置失败:', error);
    return new Response(JSON.stringify({
      success: false,
      message: error instanceof Error ? error.message : '保存SSO配置失败'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// JWT Token验证函数
async function verifyToken(token: string, secret: string): Promise<any> {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    const [headerEncoded, payloadEncoded, signature] = parts;

    // 验证签名
    const expectedSignature = await generateSignature(
      `${headerEncoded}.${payloadEncoded}`,
      secret
    );

    if (signature !== expectedSignature) {
      return null;
    }

    // 解码payload
    const payload = JSON.parse(atob(payloadEncoded.replace(/-/g, '+').replace(/_/g, '/')));

    // 检查过期时间
    if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
      return null;
    }

    return payload;
  } catch (error) {
    return null;
  }
}

// 生成签名
async function generateSignature(data: string, secret: string): Promise<string> {
  const encoder = new TextEncoder();
  const key = await crypto.subtle.importKey(
    'raw',
    encoder.encode(secret),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );

  const signature = await crypto.subtle.sign('HMAC', key, encoder.encode(data));
  return btoa(String.fromCharCode(...new Uint8Array(signature)))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}
