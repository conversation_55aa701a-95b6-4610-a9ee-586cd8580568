interface Env {
  bgdb: D1Database;
  bgkv: KVNamespace;
  JWT_SECRET: string;
  AUTH_ACCESS: string;
}

// 验证管理员权限
async function verifyAdminAuth(request: Request, env: Env) {
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('未提供认证令牌');
  }

  const token = authHeader.substring(7);

  try {
    const payload = await verifyToken(token, env.JWT_SECRET);
    if (!payload) {
      throw new Error('认证令牌无效');
    }

    const userId = payload.userId;

    const db = env.bgdb;
    const user = await db.prepare(`
      SELECT id, username, role
      FROM users
      WHERE id = ? AND status = 1
    `).bind(userId).first();

    if (!user || user.role !== 'admin') {
      throw new Error('权限不足');
    }

    return user;
  } catch (error) {
    throw new Error('认证失败');
  }
}

// JWT Token验证函数
async function verifyToken(token: string, secret: string): Promise<any> {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    const [headerEncoded, payloadEncoded, signature] = parts;

    // 验证签名
    const expectedSignature = await generateSignature(
      `${headerEncoded}.${payloadEncoded}`,
      secret
    );

    if (signature !== expectedSignature) {
      return null;
    }

    // 解码payload
    const payload = JSON.parse(atob(payloadEncoded.replace(/-/g, '+').replace(/_/g, '/')));

    // 检查过期时间
    if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
      return null;
    }

    return payload;
  } catch (error) {
    return null;
  }
}

// 生成签名
async function generateSignature(data: string, secret: string): Promise<string> {
  const encoder = new TextEncoder();
  const key = await crypto.subtle.importKey(
    'raw',
    encoder.encode(secret),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );

  const signature = await crypto.subtle.sign('HMAC', key, encoder.encode(data));
  return btoa(String.fromCharCode(...new Uint8Array(signature)))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

interface SSOConfig {
  enabled: boolean;
  type: 'ldap' | 'radius';
  server: string;
  port: number;
  baseDN?: string;
  bindDN?: string;
  bindPassword?: string;
  userFilter?: string;
  secret?: string;
}

interface SSOUser {
  username: string;
  email: string;
  nickname: string;
}

// 模拟LDAP用户同步（实际项目中需要使用真实的LDAP库）
async function syncLDAPUsers(config: SSOConfig): Promise<SSOUser[]> {
  // 这里是模拟数据，实际项目中需要连接真实的LDAP服务器
  const mockUsers: SSOUser[] = [
    {
      username: 'john.doe',
      email: '<EMAIL>',
      nickname: 'John Doe'
    },
    {
      username: 'jane.smith',
      email: '<EMAIL>',
      nickname: 'Jane Smith'
    },
    {
      username: 'bob.wilson',
      email: '<EMAIL>',
      nickname: 'Bob Wilson'
    }
  ];

  return mockUsers;
}

// 模拟RADIUS用户同步（实际项目中需要使用真实的RADIUS库）
async function syncRADIUSUsers(config: SSOConfig): Promise<SSOUser[]> {
  // 这里是模拟数据，实际项目中需要连接真实的RADIUS服务器
  const mockUsers: SSOUser[] = [
    {
      username: 'alice.brown',
      email: '<EMAIL>',
      nickname: 'Alice Brown'
    },
    {
      username: 'charlie.davis',
      email: '<EMAIL>',
      nickname: 'Charlie Davis'
    }
  ];

  return mockUsers;
}

export async function onRequestPost(context: { request: Request; env: Env }) {
  const { request, env } = context;

  try {
    // 验证管理员权限
    await verifyAdminAuth(request, env);

    // 获取SSO配置
    const configStr = await env.bgkv.get('sso_config');
    if (!configStr) {
      return new Response(JSON.stringify({
        success: false,
        message: 'SSO配置不存在'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const config: SSOConfig = JSON.parse(configStr);
    if (!config.enabled) {
      return new Response(JSON.stringify({
        success: false,
        message: 'SSO未启用'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    let ssoUsers: SSOUser[] = [];

    // 根据SSO类型同步用户
    if (config.type === 'ldap') {
      ssoUsers = await syncLDAPUsers(config);
    } else if (config.type === 'radius') {
      ssoUsers = await syncRADIUSUsers(config);
    }

    let syncedCount = 0;
    let updatedCount = 0;

    // 同步用户到数据库
    for (const ssoUser of ssoUsers) {
      try {
        // 检查用户是否已存在
        const existingUser = await env.bgdb.prepare(
          'SELECT id FROM users WHERE username = ?'
        ).bind(ssoUser.username).first();

        if (existingUser) {
          // 更新现有用户
          await env.bgdb.prepare(`
            UPDATE users 
            SET email = ?, nickname = ?, source = ?, updated_at = datetime('now')
            WHERE username = ?
          `).bind(
            ssoUser.email,
            ssoUser.nickname,
            config.type,
            ssoUser.username
          ).run();
          updatedCount++;
        } else {
          // 创建新用户
          await env.bgdb.prepare(`
            INSERT INTO users (username, email, nickname, password_hash, role, balance, status, source, created_at, updated_at)
            VALUES (?, ?, ?, '', 'user', 10000, 1, ?, datetime('now'), datetime('now'))
          `).bind(
            ssoUser.username,
            ssoUser.email,
            ssoUser.nickname,
            config.type
          ).run();
          syncedCount++;
        }
      } catch (error) {
        console.error(`同步用户 ${ssoUser.username} 失败:`, error);
      }
    }

    return new Response(JSON.stringify({
      success: true,
      message: `SSO用户同步完成，新增 ${syncedCount} 个用户，更新 ${updatedCount} 个用户`,
      data: {
        synced: syncedCount,
        updated: updatedCount,
        total: ssoUsers.length
      }
    }), {
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('SSO用户同步失败:', error);
    return new Response(JSON.stringify({
      success: false,
      message: error instanceof Error ? error.message : 'SSO用户同步失败'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
