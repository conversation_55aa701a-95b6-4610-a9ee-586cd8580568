interface Env {
  bgdb: D1Database;
  bgkv: KVNamespace;
  JWT_SECRET: string;
  AUTH_ACCESS: string;
}

// 验证管理员权限
async function verifyAdminAuth(request: Request, env: Env) {
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('未提供认证令牌');
  }

  const token = authHeader.substring(7);
  
  try {
    const payload = await verifyToken(token, env.JWT_SECRET);
    if (!payload) {
      throw new Error('认证令牌无效');
    }

    const userId = payload.userId;
    
    const db = env.bgdb;
    const user = await db.prepare(`
      SELECT id, username, role
      FROM users
      WHERE id = ? AND status = 1
    `).bind(userId).first();

    if (!user || user.role !== 'admin') {
      throw new Error('权限不足');
    }
    
    return user;
  } catch (error) {
    throw new Error('认证失败');
  }
}

// JWT Token验证函数
async function verifyToken(token: string, secret: string): Promise<any> {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    const [headerEncoded, payloadEncoded, signature] = parts;

    // 验证签名
    const expectedSignature = await generateSignature(
      `${headerEncoded}.${payloadEncoded}`,
      secret
    );

    if (signature !== expectedSignature) {
      return null;
    }

    // 解码payload
    const payload = JSON.parse(atob(payloadEncoded.replace(/-/g, '+').replace(/_/g, '/')));

    // 检查过期时间
    if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
      return null;
    }

    return payload;
  } catch (error) {
    return null;
  }
}

// 生成签名
async function generateSignature(data: string, secret: string): Promise<string> {
  const encoder = new TextEncoder();
  const key = await crypto.subtle.importKey(
    'raw',
    encoder.encode(secret),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );

  const signature = await crypto.subtle.sign('HMAC', key, encoder.encode(data));
  return btoa(String.fromCharCode(...new Uint8Array(signature)))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

// 生成随机密钥
function generateRandomKey(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 16; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

export async function onRequestPost(context: { request: Request; env: Env }) {
  const { request, env } = context;

  try {
    // 验证管理员权限
    const admin = await verifyAdminAuth(request, env);

    const { amount, extend_days, expires_at } = await request.json();

    // 验证参数
    if (!amount || amount <= 0) {
      return new Response(JSON.stringify({
        success: false,
        message: '充值金额必须大于0'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (!extend_days || extend_days <= 0) {
      return new Response(JSON.stringify({
        success: false,
        message: '延长天数必须大于0'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 生成唯一密钥
    let keyCode: string;
    let attempts = 0;
    const maxAttempts = 10;

    do {
      keyCode = generateRandomKey();
      attempts++;
      
      // 检查密钥是否已存在
      const existingKey = await env.bgdb.prepare(`
        SELECT id FROM recharge_keys WHERE key_code = ?
      `).bind(keyCode).first();
      
      if (!existingKey) {
        break;
      }
      
      if (attempts >= maxAttempts) {
        return new Response(JSON.stringify({
          success: false,
          message: '生成密钥失败，请重试'
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        });
      }
    } while (true);

    // 插入新的充值密钥
    const result = await env.bgdb.prepare(`
      INSERT INTO recharge_keys (key_code, amount, extend_days, status, expires_at, created_at)
      VALUES (?, ?, ?, 0, ?, datetime('now'))
    `).bind(keyCode, amount, extend_days, expires_at || null).run();

    if (!result.success) {
      return new Response(JSON.stringify({
        success: false,
        message: '创建充值密钥失败'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      message: '充值密钥生成成功',
      data: {
        key_code: keyCode,
        amount: amount,
        extend_days: extend_days,
        expires_at: expires_at
      }
    }), {
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('生成充值密钥失败:', error);
    return new Response(JSON.stringify({
      success: false,
      message: error instanceof Error ? error.message : '生成充值密钥失败'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
