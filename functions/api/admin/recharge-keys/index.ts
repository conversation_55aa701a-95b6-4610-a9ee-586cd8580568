interface Env {
  bgdb: D1Database;
  bgkv: KVNamespace;
  JWT_SECRET: string;
  AUTH_ACCESS: string;
}

// 验证管理员权限
async function verifyAdminAuth(request: Request, env: Env) {
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('未提供认证令牌');
  }

  const token = authHeader.substring(7);
  
  try {
    const payload = await verifyToken(token, env.JWT_SECRET);
    if (!payload) {
      throw new Error('认证令牌无效');
    }

    const userId = payload.userId;
    
    const db = env.bgdb;
    const user = await db.prepare(`
      SELECT id, username, role
      FROM users
      WHERE id = ? AND status = 1
    `).bind(userId).first();

    if (!user || user.role !== 'admin') {
      throw new Error('权限不足');
    }
    
    return user;
  } catch (error) {
    throw new Error('认证失败');
  }
}

// JWT Token验证函数
async function verifyToken(token: string, secret: string): Promise<any> {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    const [headerEncoded, payloadEncoded, signature] = parts;

    // 验证签名
    const expectedSignature = await generateSignature(
      `${headerEncoded}.${payloadEncoded}`,
      secret
    );

    if (signature !== expectedSignature) {
      return null;
    }

    // 解码payload
    const payload = JSON.parse(atob(payloadEncoded.replace(/-/g, '+').replace(/_/g, '/')));

    // 检查过期时间
    if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
      return null;
    }

    return payload;
  } catch (error) {
    return null;
  }
}

// 生成签名
async function generateSignature(data: string, secret: string): Promise<string> {
  const encoder = new TextEncoder();
  const key = await crypto.subtle.importKey(
    'raw',
    encoder.encode(secret),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );

  const signature = await crypto.subtle.sign('HMAC', key, encoder.encode(data));
  return btoa(String.fromCharCode(...new Uint8Array(signature)))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

export async function onRequestGet(context: { request: Request; env: Env }) {
  const { request, env } = context;

  try {
    // 验证管理员权限
    const admin = await verifyAdminAuth(request, env);

    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const offset = (page - 1) * limit;

    // 获取充值密钥列表
    const keys = await env.bgdb.prepare(`
      SELECT 
        rk.*,
        u.username as used_by_username
      FROM recharge_keys rk
      LEFT JOIN users u ON rk.used_by = u.id
      ORDER BY rk.created_at DESC
      LIMIT ? OFFSET ?
    `).bind(limit, offset).all();

    // 获取总数
    const countResult = await env.bgdb.prepare(`
      SELECT COUNT(*) as total FROM recharge_keys
    `).first();

    const total = countResult?.total || 0;

    return new Response(JSON.stringify({
      success: true,
      data: keys.results.map((key: any) => ({
        id: key.id,
        key_code: key.key_code,
        amount: key.amount,
        extend_days: key.extend_days,
        status: key.status,
        used_by: key.used_by_username,
        used_at: key.used_at,
        created_at: key.created_at,
        expires_at: key.expires_at
      })),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }), {
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('获取充值密钥列表失败:', error);
    return new Response(JSON.stringify({
      success: false,
      message: error instanceof Error ? error.message : '获取充值密钥列表失败'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
