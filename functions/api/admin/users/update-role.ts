interface Env {
  bgdb: D1Database;
  bgkv: KVNamespace;
  JWT_SECRET: string;
  AUTH_ACCESS: string;
}

// 验证管理员权限
async function verifyAdminAuth(request: Request, env: Env) {
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('未提供认证令牌');
  }

  const token = authHeader.substring(7);

  try {
    const payload = await verifyToken(token, env.JWT_SECRET);
    if (!payload) {
      throw new Error('认证令牌无效');
    }

    const userId = payload.userId;

    const db = env.bgdb;
    const user = await db.prepare(`
      SELECT id, username, role
      FROM users
      WHERE id = ? AND status = 1
    `).bind(userId).first();

    if (!user || user.role !== 'admin') {
      throw new Error('权限不足');
    }

    return user;
  } catch (error) {
    throw new Error('认证失败');
  }
}

// JWT Token验证函数
async function verifyToken(token: string, secret: string): Promise<any> {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    const [headerEncoded, payloadEncoded, signature] = parts;

    // 验证签名
    const expectedSignature = await generateSignature(
      `${headerEncoded}.${payloadEncoded}`,
      secret
    );

    if (signature !== expectedSignature) {
      return null;
    }

    // 解码payload
    const payload = JSON.parse(atob(payloadEncoded.replace(/-/g, '+').replace(/_/g, '/')));

    // 检查过期时间
    if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
      return null;
    }

    return payload;
  } catch (error) {
    return null;
  }
}

// 生成签名
async function generateSignature(data: string, secret: string): Promise<string> {
  const encoder = new TextEncoder();
  const key = await crypto.subtle.importKey(
    'raw',
    encoder.encode(secret),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );

  const signature = await crypto.subtle.sign('HMAC', key, encoder.encode(data));
  return btoa(String.fromCharCode(...new Uint8Array(signature)))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

export async function onRequestPost(context: { request: Request; env: Env }) {
  const { request, env } = context;

  try {
    // 验证管理员权限
    await verifyAdminAuth(request, env);

    const { userId, role } = await request.json();

    // 验证参数
    if (!userId || !role) {
      return new Response(JSON.stringify({
        success: false,
        message: '用户ID和角色不能为空'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 验证角色值
    const validRoles = ['user', 'department_admin', 'admin'];
    if (!validRoles.includes(role)) {
      return new Response(JSON.stringify({
        success: false,
        message: '无效的角色'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 检查用户是否存在
    const user = await env.bgdb.prepare(
      'SELECT id, username, role FROM users WHERE id = ?'
    ).bind(userId).first();

    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        message: '用户不存在'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 不允许修改admin用户的角色
    if (user.role === 'admin') {
      return new Response(JSON.stringify({
        success: false,
        message: '不能修改管理员用户的角色'
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 更新用户角色
    await env.bgdb.prepare(`
      UPDATE users 
      SET role = ?, updated_at = datetime('now')
      WHERE id = ?
    `).bind(role, userId).run();

    return new Response(JSON.stringify({
      success: true,
      message: '用户角色更新成功'
    }), {
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('更新用户角色失败:', error);
    return new Response(JSON.stringify({
      success: false,
      message: error instanceof Error ? error.message : '更新用户角色失败'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
