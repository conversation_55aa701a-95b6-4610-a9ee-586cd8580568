interface Env {
  bgkv: KVNamespace;
  JWT_SECRET: string;
  bgdb: D1Database;
}

// 验证管理员权限
async function verifyAdminAuth(request: Request, env: Env) {
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('未提供认证令牌');
  }

  const token = authHeader.substring(7);
  
  try {
    const payload = await verifyToken(token, env.JWT_SECRET);
    if (!payload) {
      throw new Error('认证令牌无效');
    }

    const userId = payload.userId;
    
    const db = env.bgdb;
    const user = await db.prepare(`
      SELECT id, username, role
      FROM users
      WHERE id = ? AND status = 1
    `).bind(userId).first();

    if (!user || user.role !== 'admin') {
      throw new Error('权限不足');
    }
    
    return user;
  } catch (error) {
    throw new Error('认证失败');
  }
}

// 更新用户状态
export const onRequestPatch: PagesFunction<Env> = async (context) => {
  try {
    const { request, env, params } = context;
    
    // 验证管理员权限
    await verifyAdminAuth(request, env);
    
    const userId = params.id;
    const body = await request.json();
    const { status } = body;
    
    // 验证状态值
    if (status !== 0 && status !== 1) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          message: '无效的状态值' 
        }), 
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    
    const db = env.bgdb;
    
    // 检查用户是否存在
    const user = await db.prepare(`
      SELECT id, role FROM users WHERE id = ?
    `).bind(userId).first();
    
    if (!user) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          message: '用户不存在' 
        }), 
        {
          status: 404,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    
    // 不能禁用管理员账户
    if (user.role === 'admin' && status === 0) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          message: '不能禁用管理员账户' 
        }), 
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    
    // 更新用户状态
    const result = await db.prepare(`
      UPDATE users 
      SET status = ?, updated_at = datetime('now')
      WHERE id = ?
    `).bind(status, userId).run();
    
    if (result.success) {
      return new Response(
        JSON.stringify({
          success: true,
          message: status === 1 ? '用户已启用' : '用户已禁用'
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    } else {
      throw new Error('更新用户状态失败');
    }
    
  } catch (error) {
    console.error('更新用户状态失败:', error);
    return new Response(
      JSON.stringify({ 
        success: false, 
        message: error.message || '服务器内部错误' 
      }), 
      {
        status: error.message === '权限不足' ? 403 : 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};

// JWT Token验证函数
async function verifyToken(token: string, secret: string): Promise<any> {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    const [headerEncoded, payloadEncoded, signature] = parts;

    // 验证签名
    const expectedSignature = await generateSignature(
      `${headerEncoded}.${payloadEncoded}`,
      secret
    );

    if (signature !== expectedSignature) {
      return null;
    }

    // 解码payload
    const payload = JSON.parse(atob(payloadEncoded.replace(/-/g, '+').replace(/_/g, '/')));

    // 检查过期时间
    if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
      return null;
    }

    return payload;
  } catch (error) {
    return null;
  }
}

// 生成签名
async function generateSignature(input: string, secret: string): Promise<string> {
  const encoder = new TextEncoder();
  const key = await crypto.subtle.importKey(
    'raw',
    encoder.encode(secret),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );

  const signature = await crypto.subtle.sign(
    'HMAC',
    key,
    encoder.encode(input)
  );

  return btoa(String.fromCharCode(...new Uint8Array(signature)))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}
