interface Env {
  bgkv: KVNamespace;
  JWT_SECRET: string;
  bgdb: D1Database;
}

// 验证管理员权限
async function verifyAdminAuth(request: Request, env: Env) {
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('未提供认证令牌');
  }

  const token = authHeader.substring(7);
  
  try {
    const payload = await verifyToken(token, env.JWT_SECRET);
    if (!payload) {
      throw new Error('认证令牌无效');
    }

    const userId = payload.userId;
    
    const db = env.bgdb;
    const user = await db.prepare(`
      SELECT id, username, role
      FROM users
      WHERE id = ? AND status = 1
    `).bind(userId).first();

    if (!user || user.role !== 'admin') {
      throw new Error('权限不足');
    }
    
    return user;
  } catch (error) {
    throw new Error('认证失败');
  }
}

// 获取用户列表
export const onRequestGet: PagesFunction<Env> = async (context) => {
  try {
    const { request, env } = context;
    
    // 验证管理员权限
    await verifyAdminAuth(request, env);
    
    const db = env.bgdb;
    
    // 获取所有用户
    const users = await db.prepare(`
      SELECT id, username, email, nickname, role, department_id, balance, status, created_at, updated_at
      FROM users
      ORDER BY created_at DESC
    `).all();

    return new Response(
      JSON.stringify({
        success: true,
        data: users.results
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
    
  } catch (error) {
    console.error('获取用户列表失败:', error);
    return new Response(
      JSON.stringify({ 
        success: false, 
        message: error.message || '服务器内部错误' 
      }), 
      {
        status: error.message === '权限不足' ? 403 : 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};

// 创建用户
export const onRequestPost: PagesFunction<Env> = async (context) => {
  try {
    const { request, env } = context;
    
    // 验证管理员权限
    await verifyAdminAuth(request, env);
    
    const body = await request.json();
    const { username, password, email, nickname, role, department_id, initial_balance } = body;
    
    // 验证必填字段
    if (!username || !password || !email) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          message: '用户名、密码和邮箱为必填字段' 
        }), 
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    
    // 验证角色
    if (role && !['user', 'department_admin'].includes(role)) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          message: '无效的用户角色' 
        }), 
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    
    const db = env.bgdb;
    
    // 检查用户名是否已存在
    const existingUser = await db.prepare(`
      SELECT id FROM users WHERE username = ?
    `).bind(username).first();
    
    if (existingUser) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          message: '用户名已存在' 
        }), 
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    
    // 检查邮箱是否已存在
    const existingEmail = await db.prepare(`
      SELECT id FROM users WHERE email = ?
    `).bind(email).first();
    
    if (existingEmail) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          message: '邮箱已存在' 
        }), 
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    
    // 创建用户（这里简化处理，实际应该使用bcrypt等安全的哈希算法）
    const result = await db.prepare(`
      INSERT INTO users (username, password_hash, email, nickname, role, department_id, balance, status, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, 1, datetime('now'), datetime('now'))
    `).bind(
      username,
      password, // 实际应该哈希处理
      email,
      nickname || username,
      role || 'user',
      department_id || null,
      initial_balance || 100.0
    ).run();
    
    if (result.success) {
      // 创建初始余额记录
      if (initial_balance && initial_balance > 0) {
        await db.prepare(`
          INSERT INTO billing_transactions (user_id, type, amount, description, created_at)
          VALUES (?, 'income', ?, '账户初始化', datetime('now'))
        `).bind(result.meta.last_row_id, initial_balance).run();
      }
      
      return new Response(
        JSON.stringify({
          success: true,
          message: '用户创建成功',
          data: { id: result.meta.last_row_id }
        }),
        {
          status: 201,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    } else {
      throw new Error('创建用户失败');
    }
    
  } catch (error) {
    console.error('创建用户失败:', error);
    return new Response(
      JSON.stringify({ 
        success: false, 
        message: error.message || '服务器内部错误' 
      }), 
      {
        status: error.message === '权限不足' ? 403 : 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};

// JWT Token验证函数
async function verifyToken(token: string, secret: string): Promise<any> {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    const [headerEncoded, payloadEncoded, signature] = parts;

    // 验证签名
    const expectedSignature = await generateSignature(
      `${headerEncoded}.${payloadEncoded}`,
      secret
    );

    if (signature !== expectedSignature) {
      return null;
    }

    // 解码payload
    const payload = JSON.parse(atob(payloadEncoded.replace(/-/g, '+').replace(/_/g, '/')));

    // 检查过期时间
    if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
      return null;
    }

    return payload;
  } catch (error) {
    return null;
  }
}

// 生成签名
async function generateSignature(input: string, secret: string): Promise<string> {
  const encoder = new TextEncoder();
  const key = await crypto.subtle.importKey(
    'raw',
    encoder.encode(secret),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );

  const signature = await crypto.subtle.sign(
    'HMAC',
    key,
    encoder.encode(input)
  );

  return btoa(String.fromCharCode(...new Uint8Array(signature)))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}
