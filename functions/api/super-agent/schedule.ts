interface Env {
    bgdb: D1Database;
}

interface ScheduleRequest {
    group_id: number;
    message_id: number;
    intent_analysis: {
        intent_type: string;
        task_complexity: 'simple' | 'medium' | 'complex';
        required_capabilities: string[];
        priority: number;
        estimated_agents_needed: number;
        task_description: string;
        confidence: number;
    };
    available_agents: Array<{
        id: number;
        name: string;
        display_name: string;
        capabilities: string[];
        avg_rating: number;
        success_rate: number;
    }>;
    user_weights: Array<{
        user_id: number;
        weight: number;
    }>;
}

interface TaskAssignment {
    agent_id: number;
    agent_name: string;
    task_type: string;
    task_description: string;
    priority: number;
    estimated_duration: number;
}

export const onRequestPost: PagesFunction<Env> = async (context) => {
    try {
        const { env, request } = context;
        const body: ScheduleRequest = await request.json();
        
        if (!body.group_id || !body.message_id || !body.intent_analysis) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '缺少必填参数' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const db = env.bgdb;
        const { intent_analysis, available_agents } = body;

        // 根据任务复杂度和意图选择合适的Agent
        const selectedAgents = await selectOptimalAgents(
            available_agents,
            intent_analysis,
            db
        );

        if (selectedAgents.length === 0) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '没有找到合适的Agent来处理此任务' 
                }), 
                { status: 404, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 创建任务分配记录
        const taskAssignments: TaskAssignment[] = [];
        
        for (const assignment of selectedAgents) {
            const result = await db.prepare(`
                INSERT INTO task_assignments (
                    group_id, message_id, assigned_agent_id, task_type, 
                    task_description, status, priority
                ) VALUES (?, ?, ?, ?, ?, 'pending', ?)
            `).bind(
                body.group_id,
                body.message_id,
                assignment.agent_id,
                assignment.task_type,
                assignment.task_description,
                assignment.priority
            ).run();

            taskAssignments.push({
                ...assignment,
                task_assignment_id: result.lastRowId
            });
        }

        // 更新Agent使用统计
        for (const assignment of selectedAgents) {
            await updateAgentUsageStats(db, assignment.agent_id);
        }

        return new Response(
            JSON.stringify({ 
                success: true,
                data: {
                    task_assignments: taskAssignments,
                    scheduling_strategy: getSchedulingStrategy(intent_analysis),
                    estimated_completion_time: calculateEstimatedTime(selectedAgents)
                }
            }), 
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('任务调度失败:', error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误，请稍后重试' 
            }), 
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
};

// 选择最优Agent组合
async function selectOptimalAgents(
    availableAgents: any[],
    intentAnalysis: any,
    db: D1Database
): Promise<TaskAssignment[]> {
    
    const { task_complexity, required_capabilities, estimated_agents_needed, priority } = intentAnalysis;
    
    // 根据能力匹配度对Agent进行评分
    const agentScores = availableAgents.map(agent => {
        let score = 0;
        let matchedCapabilities = 0;
        
        // 能力匹配度评分
        for (const requiredCap of required_capabilities) {
            for (const agentCap of agent.capabilities) {
                if (agentCap.includes(requiredCap) || requiredCap.includes(agentCap)) {
                    matchedCapabilities++;
                    score += 10;
                    break;
                }
            }
        }
        
        // 历史表现评分
        score += agent.avg_rating * 5;
        score += agent.success_rate * 10;
        
        // 能力覆盖率
        const capabilityCoverage = required_capabilities.length > 0 ? 
            matchedCapabilities / required_capabilities.length : 0.5;
        
        return {
            ...agent,
            score,
            capability_coverage: capabilityCoverage,
            matched_capabilities: matchedCapabilities
        };
    });

    // 按评分排序
    agentScores.sort((a, b) => b.score - a.score);

    const assignments: TaskAssignment[] = [];

    if (task_complexity === 'simple' || estimated_agents_needed === 1) {
        // 简单任务：选择最佳单个Agent
        const bestAgent = agentScores[0];
        if (bestAgent && bestAgent.capability_coverage > 0.3) {
            assignments.push({
                agent_id: bestAgent.id,
                agent_name: bestAgent.display_name,
                task_type: intentAnalysis.intent_type,
                task_description: intentAnalysis.task_description,
                priority: priority,
                estimated_duration: 30 // 30秒
            });
        }
    } else if (task_complexity === 'medium') {
        // 中等复杂度：可能需要2个Agent协作
        const topAgents = agentScores.slice(0, Math.min(2, estimated_agents_needed));
        
        if (topAgents.length === 1) {
            // 只有一个合适的Agent
            assignments.push({
                agent_id: topAgents[0].id,
                agent_name: topAgents[0].display_name,
                task_type: intentAnalysis.intent_type,
                task_description: intentAnalysis.task_description,
                priority: priority,
                estimated_duration: 60
            });
        } else if (topAgents.length >= 2) {
            // 分配给两个Agent
            assignments.push({
                agent_id: topAgents[0].id,
                agent_name: topAgents[0].display_name,
                task_type: `${intentAnalysis.intent_type}-主要`,
                task_description: `主要处理：${intentAnalysis.task_description}`,
                priority: priority,
                estimated_duration: 45
            });
            
            assignments.push({
                agent_id: topAgents[1].id,
                agent_name: topAgents[1].display_name,
                task_type: `${intentAnalysis.intent_type}-辅助`,
                task_description: `辅助处理：${intentAnalysis.task_description}`,
                priority: priority - 1,
                estimated_duration: 30
            });
        }
    } else {
        // 复杂任务：需要多个Agent协作
        const selectedCount = Math.min(agentScores.length, estimated_agents_needed, 3);
        const topAgents = agentScores.slice(0, selectedCount);
        
        topAgents.forEach((agent, index) => {
            const roleType = index === 0 ? '主导' : index === 1 ? '协作' : '支持';
            assignments.push({
                agent_id: agent.id,
                agent_name: agent.display_name,
                task_type: `${intentAnalysis.intent_type}-${roleType}`,
                task_description: `${roleType}角色：${intentAnalysis.task_description}`,
                priority: priority - index,
                estimated_duration: 60 + (index * 15)
            });
        });
    }

    return assignments;
}

// 更新Agent使用统计
async function updateAgentUsageStats(db: D1Database, agentId: number) {
    const today = new Date().toISOString().split('T')[0];
    
    // 更新或插入今日统计
    await db.prepare(`
        INSERT INTO agent_performance (agent_id, date, total_tasks)
        VALUES (?, ?, 1)
        ON CONFLICT(agent_id, date) DO UPDATE SET
            total_tasks = total_tasks + 1,
            updated_at = CURRENT_TIMESTAMP
    `).bind(agentId, today).run();
}

// 获取调度策略描述
function getSchedulingStrategy(intentAnalysis: any): string {
    const { task_complexity, estimated_agents_needed } = intentAnalysis;
    
    if (task_complexity === 'simple') {
        return '单Agent处理策略：选择最匹配的Agent独立完成任务';
    } else if (task_complexity === 'medium') {
        return estimated_agents_needed > 1 ? 
            '协作处理策略：主要Agent负责核心任务，辅助Agent提供支持' :
            '增强单Agent策略：使用高能力Agent处理中等复杂任务';
    } else {
        return '多Agent协作策略：任务分解后由多个专业Agent协同完成';
    }
}

// 计算预估完成时间
function calculateEstimatedTime(assignments: TaskAssignment[]): number {
    if (assignments.length === 0) return 0;
    
    // 如果是并行处理，取最长时间；如果是串行处理，累加时间
    const maxDuration = Math.max(...assignments.map(a => a.estimated_duration));
    const totalDuration = assignments.reduce((sum, a) => sum + a.estimated_duration, 0);
    
    // 假设部分并行，部分串行
    return Math.ceil((maxDuration + totalDuration) / 2);
}
