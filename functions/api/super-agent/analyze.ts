import OpenAI from 'openai';
import { modelConfigs } from '../../../src/config/aiCharacters';

interface Env {
    bgdb: D1Database;
    DASHSCOPE_API_KEY: string;
}

interface AnalyzeRequest {
    message: string;
    group_id: number;
    user_id: number;
    history?: Array<{
        role: string;
        content: string;
        sender_type: 'user' | 'agent';
        sender_name: string;
    }>;
}

interface IntentAnalysis {
    intent_type: string;
    task_complexity: 'simple' | 'medium' | 'complex';
    required_capabilities: string[];
    priority: number;
    estimated_agents_needed: number;
    task_description: string;
    confidence: number;
}

export const onRequestPost: PagesFunction<Env> = async (context) => {
    try {
        const { env, request } = context;
        const body: AnalyzeRequest = await request.json();
        
        if (!body.message || !body.group_id || !body.user_id) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '缺少必填参数: message, group_id, user_id' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const db = env.bgdb;

        // 获取群组信息和成员
        const groupInfo = await db.prepare(`
            SELECT g.*, COUNT(gm.id) as member_count
            FROM groups_new g
            LEFT JOIN group_members gm ON g.id = gm.group_id
            WHERE g.id = ?
            GROUP BY g.id
        `).bind(body.group_id).first();

        if (!groupInfo) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '群组不存在' 
                }), 
                { status: 404, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 获取群组中的用户权重信息
        const userWeights = await calculateUserWeights(db, body.group_id);
        
        // 获取可用的Agent及其能力
        const availableAgents = await getAvailableAgents(db, body.group_id);

        // 使用超级智能体分析用户意图
        const intentAnalysis = await analyzeUserIntent(
            env,
            body.message,
            body.history || [],
            availableAgents,
            userWeights,
            body.user_id
        );

        // 记录分析结果
        await db.prepare(`
            INSERT INTO messages (group_id, sender_id, sender_type, content, message_type, metadata)
            VALUES (?, ?, 'user', ?, 'analysis_request', ?)
        `).bind(
            body.group_id,
            body.user_id,
            body.message,
            JSON.stringify({
                intent_analysis: intentAnalysis,
                timestamp: new Date().toISOString()
            })
        ).run();

        return new Response(
            JSON.stringify({ 
                success: true,
                data: {
                    intent_analysis: intentAnalysis,
                    available_agents: availableAgents,
                    user_weights: userWeights
                }
            }), 
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('意图分析失败:', error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误，请稍后重试' 
            }), 
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
};

// 计算用户权重
async function calculateUserWeights(db: D1Database, groupId: number) {
    // 获取最近30天的用户发言统计
    const userStats = await db.prepare(`
        SELECT 
            sender_id,
            COUNT(*) as message_count,
            MAX(created_at) as last_message_time
        FROM messages 
        WHERE group_id = ? 
            AND sender_type = 'user' 
            AND created_at >= datetime('now', '-30 days')
        GROUP BY sender_id
        ORDER BY message_count DESC
    `).bind(groupId).all();

    const totalMessages = userStats.results.reduce((sum, user) => sum + user.message_count, 0);
    
    return userStats.results.map(user => ({
        user_id: user.sender_id,
        message_count: user.message_count,
        weight: totalMessages > 0 ? user.message_count / totalMessages : 1,
        last_active: user.last_message_time
    }));
}

// 获取可用的Agent
async function getAvailableAgents(db: D1Database, groupId: number) {
    const agents = await db.prepare(`
        SELECT 
            a.id,
            a.name,
            a.display_name,
            a.description,
            a.api_type,
            a.status,
            GROUP_CONCAT(ac.capability_name) as capabilities,
            COALESCE(ap.avg_rating, 0) as avg_rating,
            COALESCE(ap.completed_tasks, 0) as completed_tasks,
            COALESCE(ap.total_tasks, 0) as total_tasks
        FROM agents a
        INNER JOIN group_members gm ON a.id = gm.member_id AND gm.member_type = 'agent'
        LEFT JOIN agent_capabilities ac ON a.id = ac.agent_id
        LEFT JOIN (
            SELECT 
                agent_id,
                AVG(avg_rating) as avg_rating,
                SUM(completed_tasks) as completed_tasks,
                SUM(total_tasks) as total_tasks
            FROM agent_performance 
            WHERE date >= date('now', '-7 days')
            GROUP BY agent_id
        ) ap ON a.id = ap.agent_id
        WHERE gm.group_id = ? 
            AND a.status = 1 
            AND gm.is_muted = FALSE
            AND a.is_super_agent = FALSE
        GROUP BY a.id
        ORDER BY ap.avg_rating DESC, ap.completed_tasks DESC
    `).bind(groupId).all();

    return agents.results.map(agent => ({
        ...agent,
        capabilities: agent.capabilities ? agent.capabilities.split(',') : [],
        success_rate: agent.total_tasks > 0 ? agent.completed_tasks / agent.total_tasks : 0
    }));
}

// 使用AI分析用户意图
async function analyzeUserIntent(
    env: Env,
    message: string,
    history: any[],
    availableAgents: any[],
    userWeights: any[],
    currentUserId: number
): Promise<IntentAnalysis> {
    
    // 获取超级智能体配置
    const modelConfig = modelConfigs.find(config => config.model === "qwen-turbo");
    if (!modelConfig) {
        throw new Error('超级智能体模型配置未找到');
    }

    const apiKey = env[modelConfig.apiKey];
    if (!apiKey) {
        throw new Error('超级智能体API密钥未配置');
    }

    const openai = new OpenAI({
        apiKey: apiKey,
        baseURL: modelConfig.baseURL
    });

    // 构建分析提示词
    const systemPrompt = `你是一个超级智能体，负责分析用户意图并规划任务分配。

可用的Agent及其能力：
${availableAgents.map(agent => 
    `- ${agent.display_name} (${agent.name}): ${agent.capabilities.join(', ')} [评分: ${agent.avg_rating.toFixed(1)}, 成功率: ${(agent.success_rate * 100).toFixed(1)}%]`
).join('\n')}

用户权重信息：
${userWeights.map(user => 
    `- 用户${user.user_id}: 权重${(user.weight * 100).toFixed(1)}% (消息数: ${user.message_count})`
).join('\n')}

请分析以下用户消息的意图，并返回JSON格式的分析结果：
{
    "intent_type": "任务类型（如：信息查询、内容生成、数据分析、代码开发等）",
    "task_complexity": "simple|medium|complex",
    "required_capabilities": ["需要的能力列表"],
    "priority": 1-10,
    "estimated_agents_needed": 1-5,
    "task_description": "任务描述",
    "confidence": 0.0-1.0
}`;

    const userPrompt = `当前用户ID: ${currentUserId}
用户消息: ${message}

最近对话历史:
${history.slice(-5).map(h => `${h.sender_name}(${h.sender_type}): ${h.content}`).join('\n')}

请分析用户意图并返回JSON格式结果。`;

    try {
        const response = await openai.chat.completions.create({
            model: modelConfig.model,
            messages: [
                { role: 'system', content: systemPrompt },
                { role: 'user', content: userPrompt }
            ],
            temperature: 0.3,
            max_tokens: 1000
        });

        const content = response.choices[0]?.message?.content || '{}';
        
        // 尝试解析JSON响应
        try {
            const analysis = JSON.parse(content);
            
            // 验证和补充默认值
            return {
                intent_type: analysis.intent_type || '通用对话',
                task_complexity: analysis.task_complexity || 'simple',
                required_capabilities: analysis.required_capabilities || [],
                priority: Math.max(1, Math.min(10, analysis.priority || 5)),
                estimated_agents_needed: Math.max(1, Math.min(5, analysis.estimated_agents_needed || 1)),
                task_description: analysis.task_description || message,
                confidence: Math.max(0, Math.min(1, analysis.confidence || 0.5))
            };
        } catch (parseError) {
            console.error('解析AI响应失败:', parseError, 'Content:', content);
            
            // 返回默认分析结果
            return {
                intent_type: '通用对话',
                task_complexity: 'simple',
                required_capabilities: ['问答对话'],
                priority: 5,
                estimated_agents_needed: 1,
                task_description: message,
                confidence: 0.3
            };
        }
    } catch (apiError) {
        console.error('调用AI分析API失败:', apiError);
        throw new Error('意图分析服务暂时不可用');
    }
}
