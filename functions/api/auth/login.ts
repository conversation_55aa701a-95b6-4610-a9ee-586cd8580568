interface Env {
    bgkv: KVNamespace;
    JWT_SECRET: string;
    bgdb: D1Database;
}

export const onRequestPost: PagesFunction<Env> = async (context) => {
    try {
        const { request, env } = context;
        
        // 获取请求体
        const body = await request.json();
        const { username, password, phone, code, loginType = 'password' } = body;

        if (loginType === 'password') {
            // 用户名密码登录
            return await handlePasswordLogin(username, password, env);
        } else if (loginType === 'sms') {
            // 短信验证码登录
            return await handleSmsLogin(phone, code, env);
        } else {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '不支持的登录方式' 
                }), 
                {
                    status: 400,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                }
            );
        }

    } catch (error) {
        console.error(error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误' 
            }), 
            {
                status: 500,
                headers: {
                    'Content-Type': 'application/json',
                },
            }
        );
    }
};

// 用户名密码登录
async function handlePasswordLogin(username: string, password: string, env: Env) {
    // 验证输入
    if (!username || !password) {
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '用户名和密码不能为空' 
            }), 
            {
                status: 400,
                headers: {
                    'Content-Type': 'application/json',
                },
            }
        );
    }

    const db = env.bgdb;
    
    // 查询用户
    const user = await db.prepare(`
        SELECT id, username, password_hash, email, nickname, avatar_url, role, department_id, balance, status
        FROM users
        WHERE username = ? AND status = 1
    `).bind(username).first();

    if (!user) {
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '用户名或密码错误' 
            }), 
            {
                status: 401,
                headers: {
                    'Content-Type': 'application/json',
                },
            }
        );
    }

    // 验证密码 (这里简化处理，实际应该使用bcrypt等安全的哈希算法)
    const isPasswordValid = await verifyPassword(password, user.password_hash);
    if (!isPasswordValid) {
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '用户名或密码错误' 
            }), 
            {
                status: 401,
                headers: {
                    'Content-Type': 'application/json',
                },
            }
        );
    }

    // 更新最后登录时间
    await db.prepare(`
        UPDATE users 
        SET updated_at = datetime('now')
        WHERE id = ?
    `).bind(user.id).run();

    // 生成 token
    const token = await generateToken(user.id, env);

    // 返回用户信息和token (不包含密码)
    const userInfo = {
        id: user.id,
        username: user.username,
        email: user.email,
        nickname: user.nickname,
        avatar_url: user.avatar_url,
        role: user.role,
        department_id: user.department_id,
        balance: user.balance,
        status: user.status
    };

    return new Response(
        JSON.stringify({ 
            success: true, 
            message: '登录成功',
            data: {
                token,
                user: userInfo
            }
        }), 
        {
            status: 200,
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
}

// 短信验证码登录 (保持原有逻辑)
async function handleSmsLogin(phone: string, code: string, env: Env) {
    // 验证手机号格式
    if (!phone || !/^1[3-9]\d{9}$/.test(phone)) {
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '无效的手机号码' 
            }), 
            {
                status: 400,
                headers: {
                    'Content-Type': 'application/json',
                },
            }
        );
    }

    // 验证码格式检查
    if (!code || !/^\d{6}$/.test(code)) {
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '验证码格式错误' 
            }), 
            {
                status: 400,
                headers: {
                    'Content-Type': 'application/json',
                },
            }
        );
    }

    // 从 KV 中获取存储的验证码
    const storedCode = await env.bgkv.get(`sms:${phone}`);
    if (!storedCode || storedCode !== code) {
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '验证码错误或已过期' 
            }), 
            {
                status: 400,
                headers: {
                    'Content-Type': 'application/json',
                },
            }
        );
    }

    const db = env.bgdb;
    
    // 查询用户是否存在 (通过手机号)
    const existingUser = await db.prepare(`
        SELECT id, username, email, nickname, avatar_url, role, department_id, balance, status
        FROM users 
        WHERE username = ? OR email = ?
    `).bind(phone, phone).first();

    let userId;
    let userInfo;

    if (!existingUser) {
        // 用户不存在，创建新用户
        const newUserId = `user-${Date.now()}`;
        await db.prepare(`
            INSERT INTO users (id, username, email, nickname, role, balance, status, created_at, updated_at)
            VALUES (?, ?, ?, ?, 'user', 0.0, 'active', datetime('now'), datetime('now'))
        `).bind(newUserId, phone, phone, `用户${phone.substring(7)}`).run();
        
        userInfo = {
            id: newUserId,
            username: phone,
            email: phone,
            nickname: `用户${phone.substring(7)}`,
            avatar_url: null,
            role: 'user',
            department_id: null,
            balance: 0.0,
            status: 'active'
        };
        userId = newUserId;
    } else {
        // 用户存在，更新登录时间
        await db.prepare(`
            UPDATE users 
            SET updated_at = datetime('now')
            WHERE id = ?
        `).bind(existingUser.id).run();
        
        userInfo = existingUser;
        userId = existingUser.id;
    }

    // 生成 token
    const token = await generateToken(userId, env);
    
    // 删除验证码
    await env.bgkv.delete(`sms:${phone}`);

    return new Response(
        JSON.stringify({ 
            success: true, 
            message: '登录成功',
            data: {
                token,
                user: userInfo
            }
        }), 
        {
            status: 200,
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
}

// 简化的密码验证 (实际应该使用bcrypt)
async function verifyPassword(password: string, hash: string): Promise<boolean> {
    // 这里简化处理，实际应该使用安全的密码哈希验证
    // 对于admin/admin123这个测试账户，我们直接比较
    if (hash === 'admin123' && password === 'admin123') {
        return true;
    }
    
    // 其他情况可以实现真正的bcrypt验证
    return password === hash;
}

// JWT Token生成函数
async function generateToken(userId: string, env: Env): Promise<string> {
    const header = {
        alg: 'HS256',
        typ: 'JWT'
    };
    
    const payload = {
        userId,
        exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60), // 7天过期
        iat: Math.floor(Date.now() / 1000)
    };
    
    // Base64Url 编码
    const encodeBase64Url = (data: object) => {
        return btoa(JSON.stringify(data))
            .replace(/\+/g, '-')
            .replace(/\//g, '_')
            .replace(/=/g, '');
    };
    
    // 生成签名
    const generateSignature = async (input: string, secret: string) => {
        const encoder = new TextEncoder();
        const key = await crypto.subtle.importKey(
            'raw',
            encoder.encode(secret),
            { name: 'HMAC', hash: 'SHA-256' },
            false,
            ['sign']
        );
        
        const signature = await crypto.subtle.sign(
            'HMAC',
            key,
            encoder.encode(input)
        );
        
        return btoa(String.fromCharCode(...new Uint8Array(signature)))
            .replace(/\+/g, '-')
            .replace(/\//g, '_')
            .replace(/=/g, '');
    };
    
    const headerEncoded = encodeBase64Url(header);
    const payloadEncoded = encodeBase64Url(payload);
    
    const signature = await generateSignature(
        `${headerEncoded}.${payloadEncoded}`,
        env.JWT_SECRET
    );
    
    return `${headerEncoded}.${payloadEncoded}.${signature}`;
}
