interface Env {
  bgkv: KVNamespace;
  JWT_SECRET: string;
  bgdb: D1Database;
}

export const onRequestGet: PagesFunction<Env> = async (context) => {
  try {
    const { request, env } = context;
    
    // 获取认证头
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          message: '未提供认证令牌' 
        }), 
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }

    const token = authHeader.substring(7);
    
    // 验证JWT token
    try {
      const payload = await verifyToken(token, env.JWT_SECRET);
      if (!payload) {
        throw new Error('Invalid token');
      }

      const userId = payload.userId;
      
      const db = env.bgdb;
      
      // 获取用户信息
      const user = await db.prepare(`
        SELECT id, username, email, nickname, role, department_id, balance, status, created_at
        FROM users
        WHERE id = ? AND status = 1
      `).bind(userId).first();

      if (!user) {
        return new Response(
          JSON.stringify({ 
            success: false, 
            message: '用户不存在' 
          }), 
          {
            status: 404,
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );
      }

      // 返回用户信息（不包含敏感信息）
      return new Response(
        JSON.stringify({
          success: true,
          data: {
            id: user.id,
            username: user.username,
            email: user.email,
            nickname: user.nickname,
            role: user.role,
            department_id: user.department_id,
            balance: user.balance,
            created_at: user.created_at
          }
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
      
    } catch (error) {
      console.error('Token verification failed:', error);
      return new Response(
        JSON.stringify({ 
          success: false, 
          message: '认证令牌无效' 
        }), 
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return new Response(
      JSON.stringify({ 
        success: false, 
        message: '服务器内部错误' 
      }), 
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};

// JWT Token验证函数
async function verifyToken(token: string, secret: string): Promise<any> {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    const [headerEncoded, payloadEncoded, signature] = parts;

    // 验证签名
    const expectedSignature = await generateSignature(
      `${headerEncoded}.${payloadEncoded}`,
      secret
    );

    if (signature !== expectedSignature) {
      return null;
    }

    // 解码payload
    const payload = JSON.parse(atob(payloadEncoded.replace(/-/g, '+').replace(/_/g, '/')));

    // 检查过期时间
    if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
      return null;
    }

    return payload;
  } catch (error) {
    return null;
  }
}

// 生成签名
async function generateSignature(input: string, secret: string): Promise<string> {
  const encoder = new TextEncoder();
  const key = await crypto.subtle.importKey(
    'raw',
    encoder.encode(secret),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );

  const signature = await crypto.subtle.sign(
    'HMAC',
    key,
    encoder.encode(input)
  );

  return btoa(String.fromCharCode(...new Uint8Array(signature)))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}
