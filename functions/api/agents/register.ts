interface Env {
    bgdb: D1Database;
}

interface AgentRegistrationRequest {
    name: string;
    display_name: string;
    description?: string;
    api_url: string;
    api_key?: string;
    api_type: 'dify' | 'fastgpt' | 'custom';
    avatar_url?: string;
    capabilities?: string[];
}

export const onRequestPost: PagesFunction<Env> = async (context) => {
    try {
        const { env, data, request } = context;
        const body: AgentRegistrationRequest = await request.json();
        
        // 验证必填字段
        if (!body.name || !body.display_name || !body.api_url) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '缺少必填字段：name, display_name, api_url' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 验证Agent名称唯一性
        const existingAgent = await env.bgdb.prepare(`
            SELECT id FROM agents WHERE name = ?
        `).bind(body.name).first();

        if (existingAgent) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: 'Agent名称已存在，请使用其他名称' 
                }), 
                { status: 409, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 验证API连接（可选，这里先跳过实际验证）
        // TODO: 实现API连接测试

        const db = env.bgdb;
        
        // 插入Agent记录
        const result = await db.prepare(`
            INSERT INTO agents (
                name, display_name, description, api_url, api_key, 
                api_type, avatar_url, created_by, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)
        `).bind(
            body.name,
            body.display_name,
            body.description || '',
            body.api_url,
            body.api_key || '',
            body.api_type,
            body.avatar_url || '',
            data.user?.userId || null
        ).run();

        const agentId = result.lastRowId;

        // 如果提供了能力标签，插入能力记录
        if (body.capabilities && body.capabilities.length > 0) {
            for (const capability of body.capabilities) {
                await db.prepare(`
                    INSERT INTO agent_capabilities (agent_id, capability_name, capability_type, description)
                    VALUES (?, ?, 'manual', ?)
                `).bind(agentId, capability, `${body.display_name}的${capability}能力`).run();
            }
        }

        // 获取完整的Agent信息
        const agentInfo = await db.prepare(`
            SELECT 
                a.*,
                GROUP_CONCAT(ac.capability_name) as capabilities
            FROM agents a
            LEFT JOIN agent_capabilities ac ON a.id = ac.agent_id
            WHERE a.id = ?
            GROUP BY a.id
        `).bind(agentId).first();

        return new Response(
            JSON.stringify({ 
                success: true, 
                message: 'Agent注册成功',
                data: {
                    ...agentInfo,
                    capabilities: agentInfo.capabilities ? agentInfo.capabilities.split(',') : []
                }
            }), 
            {
                status: 201,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('Agent注册失败:', error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误，请稍后重试' 
            }), 
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
};
