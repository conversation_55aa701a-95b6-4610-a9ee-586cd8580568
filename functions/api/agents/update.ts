interface Env {
    bgdb: D1Database;
}

interface AgentUpdateRequest {
    display_name?: string;
    description?: string;
    api_url?: string;
    api_key?: string;
    avatar_url?: string;
    status?: number;
    capabilities?: string[];
}

export const onRequestPut: PagesFunction<Env> = async (context) => {
    try {
        const { env, data, request } = context;
        const url = new URL(request.url);
        const agentId = url.pathname.split('/').pop();
        
        if (!agentId || isNaN(parseInt(agentId))) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '无效的Agent ID' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const body: AgentUpdateRequest = await request.json();
        const db = env.bgdb;

        // 检查Agent是否存在
        const existingAgent = await db.prepare(`
            SELECT id, name, created_by FROM agents WHERE id = ?
        `).bind(agentId).first();

        if (!existingAgent) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: 'Agent不存在' 
                }), 
                { status: 404, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 权限检查：只有创建者或管理员可以修改
        if (data.user && existingAgent.created_by !== data.user.userId) {
            // TODO: 添加管理员权限检查
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '没有权限修改此Agent' 
                }), 
                { status: 403, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 构建更新SQL
        const updateFields = [];
        const updateParams = [];

        if (body.display_name !== undefined) {
            updateFields.push('display_name = ?');
            updateParams.push(body.display_name);
        }

        if (body.description !== undefined) {
            updateFields.push('description = ?');
            updateParams.push(body.description);
        }

        if (body.api_url !== undefined) {
            updateFields.push('api_url = ?');
            updateParams.push(body.api_url);
        }

        if (body.api_key !== undefined) {
            updateFields.push('api_key = ?');
            updateParams.push(body.api_key);
        }

        if (body.avatar_url !== undefined) {
            updateFields.push('avatar_url = ?');
            updateParams.push(body.avatar_url);
        }

        if (body.status !== undefined) {
            updateFields.push('status = ?');
            updateParams.push(body.status);
        }

        // 添加更新时间
        updateFields.push('updated_at = CURRENT_TIMESTAMP');
        updateParams.push(agentId);

        if (updateFields.length > 1) { // 除了updated_at还有其他字段
            const updateSQL = `
                UPDATE agents 
                SET ${updateFields.join(', ')}
                WHERE id = ?
            `;

            await db.prepare(updateSQL).bind(...updateParams).run();
        }

        // 更新能力标签
        if (body.capabilities !== undefined) {
            // 删除现有能力标签
            await db.prepare(`
                DELETE FROM agent_capabilities WHERE agent_id = ? AND capability_type = 'manual'
            `).bind(agentId).run();

            // 插入新的能力标签
            for (const capability of body.capabilities) {
                await db.prepare(`
                    INSERT INTO agent_capabilities (agent_id, capability_name, capability_type, description)
                    VALUES (?, ?, 'manual', ?)
                `).bind(agentId, capability, `更新的${capability}能力`).run();
            }
        }

        // 获取更新后的Agent信息
        const updatedAgent = await db.prepare(`
            SELECT 
                a.*,
                GROUP_CONCAT(ac.capability_name) as capabilities
            FROM agents a
            LEFT JOIN agent_capabilities ac ON a.id = ac.agent_id
            WHERE a.id = ?
            GROUP BY a.id
        `).bind(agentId).first();

        return new Response(
            JSON.stringify({ 
                success: true, 
                message: 'Agent更新成功',
                data: {
                    ...updatedAgent,
                    capabilities: updatedAgent.capabilities ? updatedAgent.capabilities.split(',') : []
                }
            }), 
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('Agent更新失败:', error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误，请稍后重试' 
            }), 
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
};

// 删除Agent
export const onRequestDelete: PagesFunction<Env> = async (context) => {
    try {
        const { env, data, request } = context;
        const url = new URL(request.url);
        const agentId = url.pathname.split('/').pop();
        
        if (!agentId || isNaN(parseInt(agentId))) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '无效的Agent ID' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const db = env.bgdb;

        // 检查Agent是否存在
        const existingAgent = await db.prepare(`
            SELECT id, name, created_by, is_super_agent FROM agents WHERE id = ?
        `).bind(agentId).first();

        if (!existingAgent) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: 'Agent不存在' 
                }), 
                { status: 404, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 不能删除超级智能体
        if (existingAgent.is_super_agent) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '不能删除超级智能体' 
                }), 
                { status: 403, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 权限检查
        if (data.user && existingAgent.created_by !== data.user.userId) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '没有权限删除此Agent' 
                }), 
                { status: 403, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 删除Agent（级联删除相关数据）
        await db.prepare(`DELETE FROM agents WHERE id = ?`).bind(agentId).run();

        return new Response(
            JSON.stringify({ 
                success: true, 
                message: 'Agent删除成功'
            }), 
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('Agent删除失败:', error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误，请稍后重试' 
            }), 
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
};
