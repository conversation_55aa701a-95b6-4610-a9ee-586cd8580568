interface Env {
    bgdb: D1Database;
}

interface CapabilityRequest {
    agent_id: number;
    capabilities: Array<{
        name: string;
        type: 'manual' | 'auto';
        description?: string;
        confidence_score?: number;
    }>;
}

interface CapabilityAnalysisRequest {
    agent_id: number;
    sample_responses: string[];
    force_update?: boolean;
}

// 添加或更新Agent能力
export const onRequestPost: PagesFunction<Env> = async (context) => {
    try {
        const { env, data, request } = context;
        const body: CapabilityRequest = await request.json();
        
        if (!body.agent_id || !body.capabilities || body.capabilities.length === 0) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '缺少必填参数：agent_id, capabilities' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const db = env.bgdb;

        // 验证Agent是否存在
        const agent = await db.prepare(`
            SELECT id, name, display_name FROM agents WHERE id = ?
        `).bind(body.agent_id).first();

        if (!agent) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: 'Agent不存在' 
                }), 
                { status: 404, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 批量添加或更新能力
        const results = [];
        for (const capability of body.capabilities) {
            try {
                // 检查能力是否已存在
                const existing = await db.prepare(`
                    SELECT id FROM agent_capabilities 
                    WHERE agent_id = ? AND capability_name = ?
                `).bind(body.agent_id, capability.name).first();

                if (existing) {
                    // 更新现有能力
                    await db.prepare(`
                        UPDATE agent_capabilities 
                        SET capability_type = ?, description = ?, confidence_score = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE id = ?
                    `).bind(
                        capability.type,
                        capability.description || '',
                        capability.confidence_score || 1.0,
                        existing.id
                    ).run();
                    
                    results.push({ action: 'updated', capability: capability.name });
                } else {
                    // 添加新能力
                    await db.prepare(`
                        INSERT INTO agent_capabilities (agent_id, capability_name, capability_type, description, confidence_score)
                        VALUES (?, ?, ?, ?, ?)
                    `).bind(
                        body.agent_id,
                        capability.name,
                        capability.type,
                        capability.description || '',
                        capability.confidence_score || 1.0
                    ).run();
                    
                    results.push({ action: 'added', capability: capability.name });
                }
            } catch (error) {
                console.error(`处理能力 ${capability.name} 失败:`, error);
                results.push({ action: 'failed', capability: capability.name, error: error.message });
            }
        }

        // 获取更新后的能力列表
        const updatedCapabilities = await getAgentCapabilities(db, body.agent_id);

        return new Response(
            JSON.stringify({ 
                success: true,
                message: '能力更新成功',
                data: {
                    agent_name: agent.display_name,
                    results: results,
                    capabilities: updatedCapabilities
                }
            }), 
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('能力管理失败:', error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误，请稍后重试' 
            }), 
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
};

// 自动分析Agent能力
export const onRequestPut: PagesFunction<Env> = async (context) => {
    try {
        const { env, request } = context;
        const body: CapabilityAnalysisRequest = await request.json();
        
        if (!body.agent_id || !body.sample_responses || body.sample_responses.length === 0) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '缺少必填参数：agent_id, sample_responses' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const db = env.bgdb;

        // 验证Agent是否存在
        const agent = await db.prepare(`
            SELECT * FROM agents WHERE id = ?
        `).bind(body.agent_id).first();

        if (!agent) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: 'Agent不存在' 
                }), 
                { status: 404, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 分析响应内容，检测能力
        const detectedCapabilities = await analyzeCapabilities(body.sample_responses);

        // 如果强制更新，先删除现有的自动检测能力
        if (body.force_update) {
            await db.prepare(`
                DELETE FROM agent_capabilities 
                WHERE agent_id = ? AND capability_type = 'auto'
            `).bind(body.agent_id).run();
        }

        // 添加检测到的能力
        const results = [];
        for (const capability of detectedCapabilities) {
            try {
                // 检查能力是否已存在
                const existing = await db.prepare(`
                    SELECT id FROM agent_capabilities 
                    WHERE agent_id = ? AND capability_name = ?
                `).bind(body.agent_id, capability.name).first();

                if (!existing) {
                    await db.prepare(`
                        INSERT INTO agent_capabilities (agent_id, capability_name, capability_type, description, confidence_score)
                        VALUES (?, ?, 'auto', ?, ?)
                    `).bind(
                        body.agent_id,
                        capability.name,
                        capability.description,
                        capability.confidence
                    ).run();
                    
                    results.push({ action: 'detected', capability: capability.name, confidence: capability.confidence });
                }
            } catch (error) {
                console.error(`添加检测能力 ${capability.name} 失败:`, error);
            }
        }

        // 获取更新后的能力列表
        const updatedCapabilities = await getAgentCapabilities(db, body.agent_id);

        return new Response(
            JSON.stringify({ 
                success: true,
                message: '能力分析完成',
                data: {
                    agent_name: agent.display_name,
                    detected_capabilities: results,
                    capabilities: updatedCapabilities
                }
            }), 
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('能力分析失败:', error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误，请稍后重试' 
            }), 
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
};

// 获取Agent能力列表
export const onRequestGet: PagesFunction<Env> = async (context) => {
    try {
        const { env, request } = context;
        const url = new URL(request.url);
        const agentId = url.searchParams.get('agent_id');
        
        if (!agentId) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '缺少agent_id参数' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const db = env.bgdb;
        const capabilities = await getAgentCapabilities(db, parseInt(agentId));

        return new Response(
            JSON.stringify({ 
                success: true,
                data: capabilities
            }), 
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('获取能力列表失败:', error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误，请稍后重试' 
            }), 
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
};

// 删除Agent能力
export const onRequestDelete: PagesFunction<Env> = async (context) => {
    try {
        const { env, request } = context;
        const url = new URL(request.url);
        const agentId = url.searchParams.get('agent_id');
        const capabilityName = url.searchParams.get('capability_name');
        
        if (!agentId || !capabilityName) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '缺少必填参数：agent_id, capability_name' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const db = env.bgdb;

        // 删除指定能力
        const result = await db.prepare(`
            DELETE FROM agent_capabilities 
            WHERE agent_id = ? AND capability_name = ?
        `).bind(agentId, capabilityName).run();

        if (result.changes === 0) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '能力不存在或已被删除' 
                }), 
                { status: 404, headers: { 'Content-Type': 'application/json' } }
            );
        }

        return new Response(
            JSON.stringify({ 
                success: true,
                message: '能力删除成功'
            }), 
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('删除能力失败:', error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误，请稍后重试' 
            }), 
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
};

// 获取Agent能力列表
async function getAgentCapabilities(db: D1Database, agentId: number) {
    const capabilities = await db.prepare(`
        SELECT * FROM agent_capabilities 
        WHERE agent_id = ? 
        ORDER BY capability_type, capability_name
    `).bind(agentId).all();

    return capabilities.results.map(cap => ({
        id: cap.id,
        name: cap.capability_name,
        type: cap.capability_type,
        description: cap.description,
        confidence_score: cap.confidence_score,
        created_at: cap.created_at,
        updated_at: cap.updated_at
    }));
}

// 分析响应内容，检测能力
async function analyzeCapabilities(responses: string[]) {
    const capabilities = [];
    const combinedText = responses.join('\n');
    
    // 定义能力检测规则
    const capabilityRules = [
        {
            name: '文本生成',
            keywords: ['生成', '创作', '写作', '编写', '撰写'],
            patterns: [/生成.*文本/, /创作.*内容/, /写.*文章/],
            description: '能够生成各种类型的文本内容'
        },
        {
            name: '代码生成',
            keywords: ['代码', 'code', '编程', 'function', 'class', 'def', 'var'],
            patterns: [/```[\s\S]*?```/, /function\s+\w+/, /class\s+\w+/],
            description: '能够生成和编写代码'
        },
        {
            name: '数据分析',
            keywords: ['分析', '统计', '数据', '图表', '趋势'],
            patterns: [/分析.*数据/, /统计.*结果/, /数据.*显示/],
            description: '能够分析和处理数据'
        },
        {
            name: '翻译',
            keywords: ['翻译', 'translate', '英文', '中文', '语言'],
            patterns: [/翻译.*为/, /translate.*to/, /英文.*中文/],
            description: '能够进行多语言翻译'
        },
        {
            name: '总结',
            keywords: ['总结', '概括', '要点', '摘要', '归纳'],
            patterns: [/总结.*内容/, /概括.*要点/, /主要.*观点/],
            description: '能够总结和概括信息'
        },
        {
            name: '问答对话',
            keywords: ['回答', '解答', '问题', '答案', '咨询'],
            patterns: [/回答.*问题/, /解答.*疑问/, /提供.*答案/],
            description: '能够进行智能问答和对话'
        },
        {
            name: '创意设计',
            keywords: ['设计', '创意', '方案', '策划', '构思'],
            patterns: [/设计.*方案/, /创意.*想法/, /策划.*活动/],
            description: '能够提供创意和设计建议'
        },
        {
            name: '逻辑推理',
            keywords: ['推理', '逻辑', '分析', '推断', '判断'],
            patterns: [/逻辑.*推理/, /分析.*原因/, /推断.*结果/],
            description: '能够进行逻辑推理和分析'
        }
    ];

    // 检测每个能力
    for (const rule of capabilityRules) {
        let score = 0;
        let matches = 0;

        // 关键词匹配
        for (const keyword of rule.keywords) {
            const regex = new RegExp(keyword, 'gi');
            const keywordMatches = (combinedText.match(regex) || []).length;
            if (keywordMatches > 0) {
                score += keywordMatches * 0.1;
                matches++;
            }
        }

        // 模式匹配
        for (const pattern of rule.patterns) {
            const patternMatches = (combinedText.match(pattern) || []).length;
            if (patternMatches > 0) {
                score += patternMatches * 0.3;
                matches++;
            }
        }

        // 如果有匹配且置信度足够高，添加到能力列表
        const confidence = Math.min(score / responses.length, 1.0);
        if (matches > 0 && confidence > 0.2) {
            capabilities.push({
                name: rule.name,
                description: rule.description,
                confidence: Math.round(confidence * 100) / 100,
                matches: matches
            });
        }
    }

    // 按置信度排序
    return capabilities.sort((a, b) => b.confidence - a.confidence);
}
