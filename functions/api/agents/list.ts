// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

export const onRequestOptions: PagesFunction = async () => {
  return new Response(null, { headers: corsHeaders });
};

interface AgentListQuery {
    page?: number;
    limit?: number;
    status?: number;
    capability?: string;
    search?: string;
}

// 模拟Agent数据
const mockAgents = [
  {
    id: 'super-agent-001',
    name: '超级智能体',
    display_name: '超级智能体',
    description: '负责任务分配和协调的超级智能体，具有管理员权限，可以调度其他智能体参与群聊',
    api_type: 'openai',
    avatar_url: '',
    status: 1, // 1=启用, 0=禁用, 2=维护中
    capabilities: ['任务分配', '意图理解', '协调管理', '决策制定'],
    avg_rating: 4.8,
    total_tasks: 1250,
    completed_tasks: 1188,
    success_rate: 95,
    created_at: '2024-01-01T00:00:00Z',
    last_used_at: '2024-01-29T10:30:00Z'
  },
  {
    id: 'coding-agent-001',
    name: '编程助手',
    display_name: '编程助手',
    description: '专业的编程和技术问题解答Agent，擅长多种编程语言和框架',
    api_type: 'openai',
    avatar_url: '',
    status: 1,
    capabilities: ['编程', '代码审查', '技术文档', '调试'],
    avg_rating: 4.4,
    total_tasks: 856,
    completed_tasks: 753,
    success_rate: 88,
    created_at: '2024-01-01T00:00:00Z',
    last_used_at: '2024-01-29T09:15:00Z'
  },
  {
    id: 'writing-agent-001',
    name: '写作助手',
    display_name: '写作助手',
    description: '专业的文案写作和内容创作Agent，能够处理各种写作需求',
    api_type: 'claude',
    avatar_url: '',
    status: 1,
    capabilities: ['文案写作', '内容策划', '翻译', '校对'],
    avg_rating: 4.6,
    total_tasks: 642,
    completed_tasks: 591,
    success_rate: 92,
    created_at: '2024-01-01T00:00:00Z',
    last_used_at: '2024-01-29T08:45:00Z'
  },
  {
    id: 'analysis-agent-001',
    name: '数据分析师',
    display_name: '数据分析师',
    description: '专业的数据分析和商业洞察Agent，擅长数据处理和可视化',
    api_type: 'qwen',
    avatar_url: '',
    status: 1,
    capabilities: ['数据分析', '统计建模', '可视化', '报告生成'],
    avg_rating: 4.2,
    total_tasks: 324,
    completed_tasks: 275,
    success_rate: 85,
    created_at: '2024-01-01T00:00:00Z',
    last_used_at: '2024-01-28T16:20:00Z'
  },
  {
    id: 'design-agent-001',
    name: '设计顾问',
    display_name: '设计顾问',
    description: '专业的UI/UX设计和创意指导Agent，提供设计建议和创意方案',
    api_type: 'deepseek',
    avatar_url: '',
    status: 0, // 离线状态
    capabilities: ['UI设计', 'UX设计', '创意策划', '品牌设计'],
    avg_rating: 4.5,
    total_tasks: 198,
    completed_tasks: 178,
    success_rate: 90,
    created_at: '2024-01-01T00:00:00Z',
    last_used_at: '2024-01-27T14:10:00Z'
  }
];

export const onRequestGet: PagesFunction = async (context) => {
    try {
        const { request } = context;
        const url = new URL(request.url);

        // 解析查询参数
        const query: AgentListQuery = {
            page: parseInt(url.searchParams.get('page') || '1'),
            limit: Math.min(parseInt(url.searchParams.get('limit') || '20'), 100), // 最大100条
            status: url.searchParams.get('status') ? parseInt(url.searchParams.get('status')!) : undefined,
            capability: url.searchParams.get('capability') || undefined,
            search: url.searchParams.get('search') || undefined
        };

        // 过滤数据
        let filteredAgents = mockAgents;

        if (query.search) {
            filteredAgents = filteredAgents.filter(agent =>
                agent.name.toLowerCase().includes(query.search!.toLowerCase()) ||
                agent.display_name.toLowerCase().includes(query.search!.toLowerCase()) ||
                agent.description.toLowerCase().includes(query.search!.toLowerCase()) ||
                agent.capabilities.some(cap => cap.toLowerCase().includes(query.search!.toLowerCase()))
            );
        }

        if (query.status !== undefined) {
            filteredAgents = filteredAgents.filter(agent => agent.status === query.status);
        }

        if (query.capability) {
            filteredAgents = filteredAgents.filter(agent =>
                agent.capabilities.some(cap => cap.toLowerCase().includes(query.capability!.toLowerCase()))
            );
        }

        // 分页
        const total = filteredAgents.length;
        const totalPages = Math.ceil(total / query.limit!);
        const offset = (query.page! - 1) * query.limit!;

        const paginatedAgents = filteredAgents.slice(offset, offset + query.limit!);

        const response = {
            success: true,
            data: {
                agents: paginatedAgents,
                pagination: {
                    page: query.page,
                    limit: query.limit,
                    total,
                    pages: totalPages
                }
            }
        };

        return new Response(JSON.stringify(response), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });

    } catch (error) {
        console.error('获取Agent列表失败:', error);
        return new Response(
            JSON.stringify({
                success: false,
                message: '服务器错误，请稍后重试'
            }),
            {
                status: 500,
                headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            }
        );
    }
};
