interface Env {
    bgdb: D1Database;
}

interface RatingRequest {
    agent_id: number;
    rating: number; // 1-5
    task_type?: string;
    feedback?: string;
    task_completed: boolean;
    response_time?: number;
}

export const onRequestPost: PagesFunction<Env> = async (context) => {
    try {
        const { env, data, request } = context;
        const body: RatingRequest = await request.json();
        
        // 验证必填字段
        if (!body.agent_id || !body.rating) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '缺少必填字段：agent_id, rating' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 验证评分范围
        if (body.rating < 1 || body.rating > 5) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '评分必须在1-5之间' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const db = env.bgdb;
        const userId = data.user?.userId;

        if (!userId) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '用户未登录' 
                }), 
                { status: 401, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 检查Agent是否存在
        const agent = await db.prepare(`
            SELECT id, name, display_name FROM agents WHERE id = ?
        `).bind(body.agent_id).first();

        if (!agent) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: 'Agent不存在' 
                }), 
                { status: 404, headers: { 'Content-Type': 'application/json' } }
            );
        }

        // 插入评分记录
        await db.prepare(`
            INSERT INTO agent_ratings (
                agent_id, user_id, task_type, rating, feedback, 
                task_completed, response_time
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        `).bind(
            body.agent_id,
            userId,
            body.task_type || '',
            body.rating,
            body.feedback || '',
            body.task_completed,
            body.response_time || null
        ).run();

        // 更新Agent性能统计
        await updateAgentPerformance(db, body.agent_id, body.rating, body.task_completed, body.response_time);

        // 获取Agent最新统计信息
        const stats = await getAgentStats(db, body.agent_id);

        return new Response(
            JSON.stringify({ 
                success: true,
                message: '评分提交成功',
                data: {
                    agent_name: agent.display_name,
                    new_rating: body.rating,
                    current_stats: stats
                }
            }), 
            {
                status: 200,
                headers: { 'Content-Type': 'application/json' }
            }
        );

    } catch (error) {
        console.error('Agent评分失败:', error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误，请稍后重试' 
            }), 
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
};

// 更新Agent性能统计
async function updateAgentPerformance(
    db: D1Database, 
    agentId: number, 
    rating: number, 
    taskCompleted: boolean, 
    responseTime?: number
) {
    const today = new Date().toISOString().split('T')[0];
    
    // 获取今日现有统计
    const existing = await db.prepare(`
        SELECT * FROM agent_performance WHERE agent_id = ? AND date = ?
    `).bind(agentId, today).first();

    if (existing) {
        // 更新现有记录
        const newTotalTasks = existing.total_tasks + 1;
        const newCompletedTasks = existing.completed_tasks + (taskCompleted ? 1 : 0);
        const newTotalRatings = existing.total_ratings + 1;
        const newAvgRating = (existing.avg_rating * existing.total_ratings + rating) / newTotalRatings;
        
        let newAvgResponseTime = existing.avg_response_time;
        if (responseTime && responseTime > 0) {
            const totalResponseTime = existing.avg_response_time * existing.total_tasks;
            newAvgResponseTime = (totalResponseTime + responseTime) / newTotalTasks;
        }

        await db.prepare(`
            UPDATE agent_performance SET
                total_tasks = ?,
                completed_tasks = ?,
                failed_tasks = ?,
                avg_response_time = ?,
                avg_rating = ?,
                total_ratings = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE agent_id = ? AND date = ?
        `).bind(
            newTotalTasks,
            newCompletedTasks,
            newTotalTasks - newCompletedTasks,
            newAvgResponseTime,
            newAvgRating,
            newTotalRatings,
            agentId,
            today
        ).run();
    } else {
        // 创建新记录
        await db.prepare(`
            INSERT INTO agent_performance (
                agent_id, date, total_tasks, completed_tasks, failed_tasks,
                avg_response_time, avg_rating, total_ratings
            ) VALUES (?, ?, 1, ?, ?, ?, ?, 1)
        `).bind(
            agentId,
            today,
            taskCompleted ? 1 : 0,
            taskCompleted ? 0 : 1,
            responseTime || 0,
            rating
        ).run();
    }
}

// 获取Agent统计信息
async function getAgentStats(db: D1Database, agentId: number) {
    // 获取最近30天的统计
    const stats = await db.prepare(`
        SELECT 
            SUM(total_tasks) as total_tasks,
            SUM(completed_tasks) as completed_tasks,
            SUM(failed_tasks) as failed_tasks,
            AVG(avg_rating) as avg_rating,
            AVG(avg_response_time) as avg_response_time,
            SUM(total_ratings) as total_ratings
        FROM agent_performance 
        WHERE agent_id = ? AND date >= date('now', '-30 days')
    `).bind(agentId).first();

    // 获取最近的评分分布
    const ratingDistribution = await db.prepare(`
        SELECT 
            rating,
            COUNT(*) as count
        FROM agent_ratings 
        WHERE agent_id = ? AND created_at >= datetime('now', '-30 days')
        GROUP BY rating
        ORDER BY rating
    `).bind(agentId).all();

    return {
        total_tasks: stats?.total_tasks || 0,
        completed_tasks: stats?.completed_tasks || 0,
        failed_tasks: stats?.failed_tasks || 0,
        success_rate: stats?.total_tasks > 0 ? 
            Math.round((stats.completed_tasks / stats.total_tasks) * 100) : 0,
        avg_rating: stats?.avg_rating ? Math.round(stats.avg_rating * 10) / 10 : 0,
        avg_response_time: stats?.avg_response_time ? Math.round(stats.avg_response_time) : 0,
        total_ratings: stats?.total_ratings || 0,
        rating_distribution: ratingDistribution.results.reduce((acc, item) => {
            acc[`rating_${item.rating}`] = item.count;
            return acc;
        }, {})
    };
}
