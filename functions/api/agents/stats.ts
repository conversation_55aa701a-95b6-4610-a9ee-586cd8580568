interface Env {
    bgdb: D1Database;
}

interface StatsQuery {
    agent_id?: number;
    days?: number;
    group_by?: 'day' | 'week' | 'month';
}

export const onRequestGet: PagesFunction<Env> = async (context) => {
    try {
        const { env, request } = context;
        const url = new URL(request.url);
        
        const query: StatsQuery = {
            agent_id: url.searchParams.get('agent_id') ? parseInt(url.searchParams.get('agent_id')!) : undefined,
            days: parseInt(url.searchParams.get('days') || '30'),
            group_by: (url.searchParams.get('group_by') as 'day' | 'week' | 'month') || 'day'
        };

        const db = env.bgdb;

        if (query.agent_id) {
            // 获取特定Agent的详细统计
            const agentStats = await getDetailedAgentStats(db, query.agent_id, query.days!);
            return new Response(
                JSON.stringify({ 
                    success: true,
                    data: agentStats
                }), 
                {
                    status: 200,
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        } else {
            // 获取所有Agent的概览统计
            const overviewStats = await getAgentsOverviewStats(db, query.days!, query.group_by!);
            return new Response(
                JSON.stringify({ 
                    success: true,
                    data: overviewStats
                }), 
                {
                    status: 200,
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        }

    } catch (error) {
        console.error('获取Agent统计失败:', error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误，请稍后重试' 
            }), 
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
};

// 获取特定Agent的详细统计
async function getDetailedAgentStats(db: D1Database, agentId: number, days: number) {
    // 基本信息
    const agentInfo = await db.prepare(`
        SELECT 
            a.*,
            GROUP_CONCAT(ac.capability_name) as capabilities
        FROM agents a
        LEFT JOIN agent_capabilities ac ON a.id = ac.agent_id
        WHERE a.id = ?
        GROUP BY a.id
    `).bind(agentId).first();

    if (!agentInfo) {
        throw new Error('Agent不存在');
    }

    // 性能统计
    const performanceStats = await db.prepare(`
        SELECT 
            date,
            total_tasks,
            completed_tasks,
            failed_tasks,
            avg_rating,
            avg_response_time,
            total_ratings
        FROM agent_performance 
        WHERE agent_id = ? AND date >= date('now', '-${days} days')
        ORDER BY date DESC
    `).bind(agentId).all();

    // 任务类型分布
    const taskTypeStats = await db.prepare(`
        SELECT 
            task_type,
            COUNT(*) as count,
            AVG(CASE WHEN ar.rating IS NOT NULL THEN ar.rating END) as avg_rating
        FROM task_assignments ta
        LEFT JOIN agent_ratings ar ON ta.assigned_agent_id = ar.agent_id
        WHERE ta.assigned_agent_id = ? 
            AND ta.assigned_at >= datetime('now', '-${days} days')
        GROUP BY task_type
        ORDER BY count DESC
    `).bind(agentId).all();

    // 最近评分
    const recentRatings = await db.prepare(`
        SELECT 
            ar.*,
            u.nickname as user_name
        FROM agent_ratings ar
        LEFT JOIN users u ON ar.user_id = u.id
        WHERE ar.agent_id = ? 
            AND ar.created_at >= datetime('now', '-${days} days')
        ORDER BY ar.created_at DESC
        LIMIT 20
    `).bind(agentId).all();

    // 计算总体统计
    const totalStats = performanceStats.results.reduce((acc, day) => ({
        total_tasks: acc.total_tasks + day.total_tasks,
        completed_tasks: acc.completed_tasks + day.completed_tasks,
        failed_tasks: acc.failed_tasks + day.failed_tasks,
        total_ratings: acc.total_ratings + day.total_ratings,
        total_response_time: acc.total_response_time + (day.avg_response_time * day.total_tasks)
    }), { total_tasks: 0, completed_tasks: 0, failed_tasks: 0, total_ratings: 0, total_response_time: 0 });

    return {
        agent_info: {
            ...agentInfo,
            capabilities: agentInfo.capabilities ? agentInfo.capabilities.split(',') : []
        },
        summary: {
            total_tasks: totalStats.total_tasks,
            completed_tasks: totalStats.completed_tasks,
            failed_tasks: totalStats.failed_tasks,
            success_rate: totalStats.total_tasks > 0 ? 
                Math.round((totalStats.completed_tasks / totalStats.total_tasks) * 100) : 0,
            avg_response_time: totalStats.total_tasks > 0 ? 
                Math.round(totalStats.total_response_time / totalStats.total_tasks) : 0,
            total_ratings: totalStats.total_ratings,
            avg_rating: totalStats.total_ratings > 0 ? 
                Math.round((recentRatings.results.reduce((sum, r) => sum + r.rating, 0) / recentRatings.results.length) * 10) / 10 : 0
        },
        daily_performance: performanceStats.results,
        task_type_distribution: taskTypeStats.results,
        recent_ratings: recentRatings.results
    };
}

// 获取所有Agent的概览统计
async function getAgentsOverviewStats(db: D1Database, days: number, groupBy: string) {
    // 获取所有活跃Agent
    const agents = await db.prepare(`
        SELECT 
            a.id,
            a.name,
            a.display_name,
            a.status,
            a.created_at
        FROM agents a
        WHERE a.status = 1 AND a.is_super_agent = FALSE
        ORDER BY a.created_at DESC
    `).all();

    // 获取Agent排行榜
    const topPerformers = await db.prepare(`
        SELECT 
            a.id,
            a.name,
            a.display_name,
            SUM(ap.total_tasks) as total_tasks,
            SUM(ap.completed_tasks) as completed_tasks,
            AVG(ap.avg_rating) as avg_rating,
            AVG(ap.avg_response_time) as avg_response_time
        FROM agents a
        INNER JOIN agent_performance ap ON a.id = ap.agent_id
        WHERE ap.date >= date('now', '-${days} days')
            AND a.status = 1 AND a.is_super_agent = FALSE
        GROUP BY a.id
        HAVING total_tasks > 0
        ORDER BY avg_rating DESC, completed_tasks DESC
        LIMIT 10
    `).all();

    // 获取系统总体统计
    const systemStats = await db.prepare(`
        SELECT 
            COUNT(DISTINCT a.id) as total_agents,
            SUM(ap.total_tasks) as total_tasks,
            SUM(ap.completed_tasks) as completed_tasks,
            AVG(ap.avg_rating) as avg_rating,
            AVG(ap.avg_response_time) as avg_response_time
        FROM agents a
        LEFT JOIN agent_performance ap ON a.id = ap.agent_id 
            AND ap.date >= date('now', '-${days} days')
        WHERE a.status = 1 AND a.is_super_agent = FALSE
    `).first();

    // 获取时间序列数据
    let dateFormat = '%Y-%m-%d';
    let dateInterval = '1 day';
    
    if (groupBy === 'week') {
        dateFormat = '%Y-W%W';
        dateInterval = '7 days';
    } else if (groupBy === 'month') {
        dateFormat = '%Y-%m';
        dateInterval = '1 month';
    }

    const timeSeriesData = await db.prepare(`
        SELECT 
            strftime('${dateFormat}', date) as period,
            SUM(total_tasks) as total_tasks,
            SUM(completed_tasks) as completed_tasks,
            AVG(avg_rating) as avg_rating,
            COUNT(DISTINCT agent_id) as active_agents
        FROM agent_performance 
        WHERE date >= date('now', '-${days} days')
        GROUP BY strftime('${dateFormat}', date)
        ORDER BY period
    `).all();

    return {
        system_overview: {
            total_agents: agents.results.length,
            active_agents: systemStats?.total_agents || 0,
            total_tasks: systemStats?.total_tasks || 0,
            completed_tasks: systemStats?.completed_tasks || 0,
            success_rate: systemStats?.total_tasks > 0 ? 
                Math.round((systemStats.completed_tasks / systemStats.total_tasks) * 100) : 0,
            avg_rating: systemStats?.avg_rating ? Math.round(systemStats.avg_rating * 10) / 10 : 0,
            avg_response_time: systemStats?.avg_response_time ? Math.round(systemStats.avg_response_time) : 0
        },
        top_performers: topPerformers.results.map(agent => ({
            ...agent,
            success_rate: agent.total_tasks > 0 ? 
                Math.round((agent.completed_tasks / agent.total_tasks) * 100) : 0,
            avg_rating: agent.avg_rating ? Math.round(agent.avg_rating * 10) / 10 : 0,
            avg_response_time: agent.avg_response_time ? Math.round(agent.avg_response_time) : 0
        })),
        time_series: timeSeriesData.results,
        all_agents: agents.results
    };
}
