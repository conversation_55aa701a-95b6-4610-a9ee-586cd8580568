interface Env {
    bgdb: D1Database;
}

interface AgentTestRequest {
    message: string;
    test_type?: 'capability' | 'connection' | 'performance';
}

interface AgentTestResponse {
    success: boolean;
    response_time: number;
    response_content?: string;
    error_message?: string;
    capabilities_detected?: string[];
}

// 测试Agent连接和能力
export const onRequestPost: PagesFunction<Env> = async (context) => {
    try {
        const { env, request } = context;
        const url = new URL(request.url);
        const agentId = url.pathname.split('/').slice(-2)[0]; // 从 /agents/{id}/test 中提取ID
        
        if (!agentId || isNaN(parseInt(agentId))) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '无效的Agent ID' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const body: AgentTestRequest = await request.json();
        
        if (!body.message) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '测试消息不能为空' 
                }), 
                { status: 400, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const db = env.bgdb;

        // 获取Agent信息
        const agent = await db.prepare(`
            SELECT * FROM agents WHERE id = ? AND status = 1
        `).bind(agentId).first();

        if (!agent) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: 'Agent不存在或已禁用' 
                }), 
                { status: 404, headers: { 'Content-Type': 'application/json' } }
            );
        }

        const startTime = Date.now();
        let testResult: AgentTestResponse;

        try {
            // 根据API类型调用不同的测试方法
            switch (agent.api_type) {
                case 'dify':
                    testResult = await testDifyAgent(agent, body.message);
                    break;
                case 'fastgpt':
                    testResult = await testFastGPTAgent(agent, body.message);
                    break;
                case 'custom':
                    testResult = await testCustomAgent(agent, body.message);
                    break;
                default:
                    throw new Error(`不支持的API类型: ${agent.api_type}`);
            }

            testResult.response_time = Date.now() - startTime;

            // 如果是能力测试，尝试检测Agent能力
            if (body.test_type === 'capability' && testResult.success) {
                testResult.capabilities_detected = await detectCapabilities(testResult.response_content || '');
                
                // 自动更新Agent能力标签
                if (testResult.capabilities_detected.length > 0) {
                    await updateAgentCapabilities(db, parseInt(agentId), testResult.capabilities_detected);
                }
            }

            // 更新Agent最后使用时间
            await db.prepare(`
                UPDATE agents SET last_used_at = CURRENT_TIMESTAMP WHERE id = ?
            `).bind(agentId).run();

            return new Response(
                JSON.stringify({ 
                    success: true,
                    data: testResult
                }), 
                {
                    status: 200,
                    headers: { 'Content-Type': 'application/json' }
                }
            );

        } catch (error) {
            console.error(`Agent测试失败 (ID: ${agentId}):`, error);
            
            return new Response(
                JSON.stringify({ 
                    success: false,
                    data: {
                        success: false,
                        response_time: Date.now() - startTime,
                        error_message: error.message
                    }
                }), 
                {
                    status: 200, // 返回200但标记测试失败
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        }

    } catch (error) {
        console.error('Agent测试API错误:', error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误，请稍后重试' 
            }), 
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
};

// 测试Dify类型的Agent
async function testDifyAgent(agent: any, message: string): Promise<AgentTestResponse> {
    const response = await fetch(`${agent.api_url}/chat-messages`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${agent.api_key}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            inputs: {},
            query: message,
            response_mode: 'blocking',
            user: 'test-user'
        })
    });

    if (!response.ok) {
        throw new Error(`Dify API错误: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    return {
        success: true,
        response_time: 0, // 将在调用处设置
        response_content: data.answer || data.message || '无响应内容'
    };
}

// 测试FastGPT类型的Agent
async function testFastGPTAgent(agent: any, message: string): Promise<AgentTestResponse> {
    const response = await fetch(`${agent.api_url}/api/v1/chat/completions`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${agent.api_key}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            chatId: 'test-chat',
            stream: false,
            detail: false,
            messages: [
                {
                    content: message,
                    role: 'user'
                }
            ]
        })
    });

    if (!response.ok) {
        throw new Error(`FastGPT API错误: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    return {
        success: true,
        response_time: 0,
        response_content: data.choices?.[0]?.message?.content || '无响应内容'
    };
}

// 测试自定义API类型的Agent
async function testCustomAgent(agent: any, message: string): Promise<AgentTestResponse> {
    const response = await fetch(agent.api_url, {
        method: 'POST',
        headers: {
            'Authorization': agent.api_key ? `Bearer ${agent.api_key}` : undefined,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            message: message,
            user: 'test-user'
        })
    });

    if (!response.ok) {
        throw new Error(`自定义API错误: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    return {
        success: true,
        response_time: 0,
        response_content: data.response || data.message || data.content || '无响应内容'
    };
}

// 基于响应内容检测Agent能力
async function detectCapabilities(responseContent: string): Promise<string[]> {
    const capabilities = [];
    
    // 简单的关键词匹配来检测能力
    const capabilityKeywords = {
        '文本生成': ['生成', '创作', '写作', '编写'],
        '代码生成': ['代码', 'code', '编程', 'function', 'class'],
        '数据分析': ['分析', '统计', '数据', '图表'],
        '翻译': ['翻译', 'translate', '英文', '中文'],
        '总结': ['总结', '概括', '要点', '摘要'],
        '问答': ['回答', '解答', '问题', '答案']
    };

    for (const [capability, keywords] of Object.entries(capabilityKeywords)) {
        if (keywords.some(keyword => responseContent.includes(keyword))) {
            capabilities.push(capability);
        }
    }

    return capabilities;
}

// 更新Agent能力标签
async function updateAgentCapabilities(db: D1Database, agentId: number, capabilities: string[]) {
    for (const capability of capabilities) {
        // 检查能力是否已存在
        const existing = await db.prepare(`
            SELECT id FROM agent_capabilities 
            WHERE agent_id = ? AND capability_name = ?
        `).bind(agentId, capability).first();

        if (!existing) {
            await db.prepare(`
                INSERT INTO agent_capabilities (agent_id, capability_name, capability_type, description)
                VALUES (?, ?, 'auto', ?)
            `).bind(agentId, capability, `自动检测到的${capability}能力`).run();
        }
    }
}
