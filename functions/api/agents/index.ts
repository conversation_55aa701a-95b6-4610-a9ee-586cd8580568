/**
 * AgentGroup - Agent管理API
 * 处理Agent的注册、查询、更新和删除操作
 */

interface Env {
  DB: D1Database;
  KV: KVNamespace;
}

interface Agent {
  id: string;
  name: string;
  description: string;
  api_type: string;
  api_key: string;
  model_name: string;
  capabilities: string[];
  performance_score: number;
  status: 'active' | 'inactive' | 'maintenance';
  created_at: string;
  updated_at: string;
}

interface AgentCapability {
  id: string;
  agent_id: string;
  capability_name: string;
  confidence_score: number;
  is_primary: boolean;
}

export async function onRequest(context: any): Promise<Response> {
  const { request, env } = context;
  const url = new URL(request.url);
  const method = request.method;

  // CORS headers
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    switch (method) {
      case 'GET':
        return await handleGet(url, env, corsHeaders);
      case 'POST':
        return await handlePost(request, env, corsHeaders);
      case 'PUT':
        return await handlePut(request, env, corsHeaders);
      case 'DELETE':
        return await handleDelete(url, env, corsHeaders);
      default:
        return new Response('Method not allowed', { 
          status: 405, 
          headers: corsHeaders 
        });
    }
  } catch (error) {
    console.error('Agent API Error:', error);
    return new Response(JSON.stringify({ 
      success: false, 
      error: 'Internal server error' 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

// 获取Agent列表或单个Agent
async function handleGet(url: URL, env: Env, corsHeaders: any): Promise<Response> {
  const agentId = url.searchParams.get('id');
  
  if (agentId) {
    // 获取单个Agent详情
    const agent = await env.DB.prepare(
      'SELECT * FROM agents WHERE id = ?'
    ).bind(agentId).first();

    if (!agent) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Agent not found'
      }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 获取Agent能力
    const capabilities = await env.DB.prepare(
      'SELECT * FROM agent_capabilities WHERE agent_id = ?'
    ).bind(agentId).all();

    return new Response(JSON.stringify({
      success: true,
      data: {
        ...agent,
        capabilities: capabilities.results
      }
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } else {
    // 获取Agent列表
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const status = url.searchParams.get('status');
    const offset = (page - 1) * limit;

    let query = 'SELECT * FROM agents';
    let countQuery = 'SELECT COUNT(*) as total FROM agents';
    const params: any[] = [];

    if (status) {
      query += ' WHERE status = ?';
      countQuery += ' WHERE status = ?';
      params.push(status);
    }

    query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
    params.push(limit, offset);

    const [agents, count] = await Promise.all([
      env.DB.prepare(query).bind(...params).all(),
      env.DB.prepare(countQuery).bind(...(status ? [status] : [])).first()
    ]);

    return new Response(JSON.stringify({
      success: true,
      data: {
        agents: agents.results,
        pagination: {
          page,
          limit,
          total: count?.total || 0,
          totalPages: Math.ceil((count?.total || 0) / limit)
        }
      }
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

// 创建新Agent
async function handlePost(request: Request, env: Env, corsHeaders: any): Promise<Response> {
  const data = await request.json();
  
  // 验证必需字段
  const requiredFields = ['name', 'description', 'api_type', 'api_key', 'model_name'];
  for (const field of requiredFields) {
    if (!data[field]) {
      return new Response(JSON.stringify({
        success: false,
        error: `Missing required field: ${field}`
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }
  }

  const agentId = crypto.randomUUID();
  const now = new Date().toISOString();

  // 插入Agent
  await env.DB.prepare(`
    INSERT INTO agents (id, name, description, api_type, api_key, model_name, 
                       performance_score, status, created_at, updated_at)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `).bind(
    agentId,
    data.name,
    data.description,
    data.api_type,
    data.api_key,
    data.model_name,
    0, // 初始性能分数
    'active',
    now,
    now
  ).run();

  // 插入能力标签
  if (data.capabilities && Array.isArray(data.capabilities)) {
    for (const capability of data.capabilities) {
      await env.DB.prepare(`
        INSERT INTO agent_capabilities (id, agent_id, capability_name, confidence_score, is_primary)
        VALUES (?, ?, ?, ?, ?)
      `).bind(
        crypto.randomUUID(),
        agentId,
        capability,
        0.8, // 默认置信度
        false
      ).run();
    }
  }

  return new Response(JSON.stringify({
    success: true,
    data: { id: agentId }
  }), {
    status: 201,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

// 更新Agent
async function handlePut(request: Request, env: Env, corsHeaders: any): Promise<Response> {
  const data = await request.json();
  const agentId = data.id;

  if (!agentId) {
    return new Response(JSON.stringify({
      success: false,
      error: 'Agent ID is required'
    }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  const now = new Date().toISOString();
  
  // 更新Agent基本信息
  await env.DB.prepare(`
    UPDATE agents 
    SET name = ?, description = ?, api_type = ?, api_key = ?, 
        model_name = ?, status = ?, updated_at = ?
    WHERE id = ?
  `).bind(
    data.name,
    data.description,
    data.api_type,
    data.api_key,
    data.model_name,
    data.status || 'active',
    now,
    agentId
  ).run();

  return new Response(JSON.stringify({
    success: true,
    data: { id: agentId }
  }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

// 删除Agent
async function handleDelete(url: URL, env: Env, corsHeaders: any): Promise<Response> {
  const agentId = url.searchParams.get('id');

  if (!agentId) {
    return new Response(JSON.stringify({
      success: false,
      error: 'Agent ID is required'
    }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  // 删除Agent能力
  await env.DB.prepare('DELETE FROM agent_capabilities WHERE agent_id = ?')
    .bind(agentId).run();

  // 删除Agent
  await env.DB.prepare('DELETE FROM agents WHERE id = ?')
    .bind(agentId).run();

  return new Response(JSON.stringify({
    success: true,
    data: { id: agentId }
  }), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}
