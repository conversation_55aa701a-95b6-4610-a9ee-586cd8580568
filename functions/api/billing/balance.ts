interface Env {
  bgkv: KVNamespace;
  JWT_SECRET: string;
  bgdb: D1Database;
}

// 获取用户余额和交易记录
export const onRequestGet: PagesFunction<Env> = async (context) => {
    try {
        const { request, env } = context;
        
        // 获取用户信息
        const authHeader = request.headers.get('Authorization');
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return new Response(
            JSON.stringify({
              success: false,
              message: '未提供认证令牌'
            }),
            {
              status: 401,
              headers: {
                'Content-Type': 'application/json',
              },
            }
          );
        }

        const token = authHeader.substring(7);

        // 验证JWT token
        const payload = await verifyToken(token, env.JWT_SECRET);
        if (!payload) {
          return new Response(
            JSON.stringify({
              success: false,
              message: '认证令牌无效'
            }),
            {
              status: 401,
              headers: {
                'Content-Type': 'application/json',
              },
            }
          );
        }

        const userId = payload.userId;
        const url = new URL(request.url);
        const includeTransactions = url.searchParams.get('include_transactions') === 'true';
        const page = parseInt(url.searchParams.get('page') || '1');
        const limit = parseInt(url.searchParams.get('limit') || '10');

        const db = env.bgdb;

        // 获取用户基本信息和余额
        const user = await db.prepare(`
            SELECT id, username, nickname, balance, role, department_id, account_expires_at
            FROM users
            WHERE id = ?
        `).bind(userId).first();

        if (!user) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '用户不存在' 
                }), 
                {
                    status: 404,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                }
            );
        }

        const result: any = {
            balance: user.balance,
            account_expires_at: user.account_expires_at,
            user: {
                id: user.id,
                username: user.username,
                nickname: user.nickname,
                role: user.role,
                department_id: user.department_id
            }
        };

        // 如果需要包含交易记录
        if (includeTransactions) {
            const offset = (page - 1) * limit;

            // 获取交易记录
            const transactions = await db.prepare(`
                SELECT
                    id, type as transaction_type, amount, description, created_at
                FROM billing_transactions
                WHERE user_id = ?
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            `).bind(userId, limit, offset).all();

            // 获取交易记录总数
            const countResult = await db.prepare(`
                SELECT COUNT(*) as total
                FROM billing_transactions
                WHERE user_id = ?
            `).bind(userId).first();

            result.transactions = {
                records: transactions.results || [],
                pagination: {
                    page,
                    limit,
                    total: countResult?.total || 0,
                    totalPages: Math.ceil((countResult?.total || 0) / limit)
                }
            };
        }

        // 获取统计信息
        const stats = await db.prepare(`
            SELECT
                SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) as total_income,
                SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as total_expense,
                COUNT(*) as transaction_count
            FROM billing_transactions
            WHERE user_id = ?
        `).bind(userId).first();

        result.stats = {
            total_income: stats?.total_income || 0,
            total_expense: stats?.total_expense || 0,
            transaction_count: stats?.transaction_count || 0
        };

        return new Response(
            JSON.stringify({ 
                success: true, 
                data: result
            }), 
            {
                status: 200,
                headers: {
                    'Content-Type': 'application/json',
                },
            }
        );

    } catch (error) {
        console.error(error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误' 
            }), 
            {
                status: 500,
                headers: {
                    'Content-Type': 'application/json',
                },
            }
        );
    }
};

// JWT Token验证函数
async function verifyToken(token: string, secret: string): Promise<any> {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    const [headerEncoded, payloadEncoded, signature] = parts;

    // 验证签名
    const expectedSignature = await generateSignature(
      `${headerEncoded}.${payloadEncoded}`,
      secret
    );

    if (signature !== expectedSignature) {
      return null;
    }

    // 解码payload
    const payload = JSON.parse(atob(payloadEncoded.replace(/-/g, '+').replace(/_/g, '/')));

    // 检查过期时间
    if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
      return null;
    }

    return payload;
  } catch (error) {
    return null;
  }
}

// 生成签名
async function generateSignature(input: string, secret: string): Promise<string> {
  const encoder = new TextEncoder();
  const key = await crypto.subtle.importKey(
    'raw',
    encoder.encode(secret),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );

  const signature = await crypto.subtle.sign(
    'HMAC',
    key,
    encoder.encode(input)
  );

  return btoa(String.fromCharCode(...new Uint8Array(signature)))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}
