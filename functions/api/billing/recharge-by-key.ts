interface Env {
  bgdb: D1Database;
  bgkv: KVNamespace;
  JWT_SECRET: string;
  AUTH_ACCESS: string;
}

// 验证用户身份
async function verifyAuth(request: Request, env: Env): Promise<{success: boolean, userId?: string, message?: string}> {
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { success: false, message: '缺少认证信息' };
  }

  const token = authHeader.substring(7);

  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      return { success: false, message: '无效的token格式' };
    }

    const payload = JSON.parse(atob(parts[1].replace(/-/g, '+').replace(/_/g, '/')));

    if (payload.exp < Math.floor(Date.now() / 1000)) {
      return { success: false, message: 'token已过期' };
    }

    return { success: true, userId: payload.userId };
  } catch (error) {
    return { success: false, message: '无效的token' };
  }
}

interface RechargeKey {
  id: number;
  key_code: string;
  amount: number;
  extend_days: number;
  status: number; // 0: 未使用, 1: 已使用
  used_by?: number;
  used_at?: string;
  created_at: string;
  expires_at?: string;
}

export async function onRequestPost(context: { request: Request; env: Env }) {
  const { request, env } = context;

  try {
    // 验证用户身份
    const authResult = await verifyAuth(request, env);
    if (!authResult.success) {
      return new Response(JSON.stringify({
        success: false,
        message: authResult.message
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    const userId = authResult.userId;

    const { recharge_key } = await request.json();

    if (!recharge_key) {
      return new Response(JSON.stringify({
        success: false,
        message: '充值密钥不能为空'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 查找充值密钥
    const keyRecord = await env.bgdb.prepare(`
      SELECT * FROM recharge_keys 
      WHERE key_code = ? AND status = 0
    `).bind(recharge_key).first() as RechargeKey | null;

    if (!keyRecord) {
      return new Response(JSON.stringify({
        success: false,
        message: '充值密钥无效或已被使用'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 检查密钥是否过期
    if (keyRecord.expires_at && new Date(keyRecord.expires_at) < new Date()) {
      return new Response(JSON.stringify({
        success: false,
        message: '充值密钥已过期'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 获取用户当前信息
    const user = await env.bgdb.prepare(`
      SELECT balance, account_expires_at FROM users WHERE id = ?
    `).bind(userId).first();

    if (!user) {
      return new Response(JSON.stringify({
        success: false,
        message: '用户不存在'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 计算新的余额和有效期
    const newBalance = (user.balance || 0) + keyRecord.amount;
    
    // 计算新的账户有效期
    let newExpiresAt: string;
    const currentExpiresAt = user.account_expires_at ? new Date(user.account_expires_at) : new Date();
    const baseDate = currentExpiresAt > new Date() ? currentExpiresAt : new Date();
    const newExpiresDate = new Date(baseDate.getTime() + keyRecord.extend_days * 24 * 60 * 60 * 1000);
    newExpiresAt = newExpiresDate.toISOString();

    // 开始事务
    try {
      // 更新用户余额和有效期
      await env.bgdb.prepare(`
        UPDATE users 
        SET balance = ?, account_expires_at = ?, updated_at = datetime('now')
        WHERE id = ?
      `).bind(newBalance, newExpiresAt, userId).run();

      // 标记充值密钥为已使用
      await env.bgdb.prepare(`
        UPDATE recharge_keys 
        SET status = 1, used_by = ?, used_at = datetime('now')
        WHERE id = ?
      `).bind(userId, keyRecord.id).run();

      // 记录交易记录
      await env.bgdb.prepare(`
        INSERT INTO billing_transactions (user_id, type, amount, description, created_at)
        VALUES (?, 'income', ?, ?, datetime('now'))
      `).bind(userId, keyRecord.amount, `充值密钥充值 - ${recharge_key}`).run();

      return new Response(JSON.stringify({
        success: true,
        message: '充值成功',
        data: {
          amount: keyRecord.amount,
          new_balance: newBalance,
          expires_at: newExpiresAt,
          extend_days: keyRecord.extend_days
        }
      }), {
        headers: { 'Content-Type': 'application/json' }
      });

    } catch (error) {
      console.error('充值事务失败:', error);
      return new Response(JSON.stringify({
        success: false,
        message: '充值失败，请重试'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

  } catch (error) {
    console.error('充值密钥验证失败:', error);
    return new Response(JSON.stringify({
      success: false,
      message: error instanceof Error ? error.message : '充值失败'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
