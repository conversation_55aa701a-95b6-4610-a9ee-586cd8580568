interface Env {
    bgkv: KVNamespace;
    JWT_SECRET: string;
    bgdb: D1Database;
}

// 获取计费记录
export const onRequestGet: PagesFunction<Env> = async (context) => {
    try {
        const { request, env } = context;
        
        // 验证用户身份
        const authResult = await verifyAuth(request, env);
        if (!authResult.success) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: authResult.message 
                }), 
                {
                    status: 401,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                }
            );
        }

        const userId = authResult.userId;
        const url = new URL(request.url);
        const page = parseInt(url.searchParams.get('page') || '1');
        const limit = parseInt(url.searchParams.get('limit') || '20');
        const billing_type = url.searchParams.get('billing_type');
        const offset = (page - 1) * limit;

        const db = env.bgdb;

        // 构建查询条件
        let whereClause = 'WHERE br.user_id = ?';
        let params = [userId];

        if (billing_type) {
            whereClause += ' AND br.billing_type = ?';
            params.push(billing_type);
        }

        // 查询计费记录
        const records = await db.prepare(`
            SELECT 
                br.*,
                u.nickname as creator_name,
                a.name as agent_name
            FROM billing_records br
            LEFT JOIN users u ON br.creator_id = u.id
            LEFT JOIN agents a ON br.resource_id = a.id AND br.resource_type = 'agent'
            ${whereClause}
            ORDER BY br.created_at DESC
            LIMIT ? OFFSET ?
        `).bind(...params, limit, offset).all();

        // 查询总数
        const countResult = await db.prepare(`
            SELECT COUNT(*) as total
            FROM billing_records br
            ${whereClause}
        `).bind(...params).first();

        const total = countResult?.total || 0;

        return new Response(
            JSON.stringify({ 
                success: true, 
                data: {
                    records: records.results || [],
                    pagination: {
                        page,
                        limit,
                        total,
                        totalPages: Math.ceil(total / limit)
                    }
                }
            }), 
            {
                status: 200,
                headers: {
                    'Content-Type': 'application/json',
                },
            }
        );

    } catch (error) {
        console.error(error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误' 
            }), 
            {
                status: 500,
                headers: {
                    'Content-Type': 'application/json',
                },
            }
        );
    }
};

// 创建计费记录
export const onRequestPost: PagesFunction<Env> = async (context) => {
    try {
        const { request, env } = context;
        
        // 验证用户身份
        const authResult = await verifyAuth(request, env);
        if (!authResult.success) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: authResult.message 
                }), 
                {
                    status: 401,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                }
            );
        }

        const userId = authResult.userId;
        const body = await request.json();
        const { 
            billing_type, 
            resource_id, 
            resource_type, 
            quantity, 
            conversation_id, 
            message_id,
            metadata 
        } = body;

        // 验证必填字段
        if (!billing_type || !resource_type || !quantity) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '缺少必填字段' 
                }), 
                {
                    status: 400,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                }
            );
        }

        const db = env.bgdb;

        // 获取定价信息
        let unitPrice = 0;
        let creatorId = null;
        let creatorShare = 0;
        let platformShare = 0;

        if (resource_type === 'agent' && resource_id) {
            // Agent调用计费
            const agentPricing = await db.prepare(`
                SELECT ap.price, ap.creator_share, ap.platform_share, a.id as agent_id
                FROM agent_pricing ap
                JOIN agents a ON ap.agent_id = a.id
                WHERE a.id = ? AND ap.status = 'active'
            `).bind(resource_id).first();

            if (agentPricing) {
                unitPrice = agentPricing.price;
                creatorShare = agentPricing.creator_share;
                platformShare = agentPricing.platform_share;

                // 获取Agent创作者
                const agentPermission = await db.prepare(`
                    SELECT owner_id FROM agent_permissions 
                    WHERE agent_id = ? AND owner_type = 'user'
                `).bind(resource_id).first();
                
                creatorId = agentPermission?.owner_id;
            }
        } else {
            // 算力资源计费
            const computePricing = await db.prepare(`
                SELECT price_per_unit FROM compute_pricing 
                WHERE resource_type = ? AND status = 'active'
                ${resource_id ? 'AND model_name = ?' : ''}
            `).bind(resource_type, ...(resource_id ? [resource_id] : [])).first();

            if (computePricing) {
                unitPrice = computePricing.price_per_unit;
            }
        }

        const totalAmount = quantity * unitPrice;
        const creatorAmount = totalAmount * creatorShare;
        const platformAmount = totalAmount * platformShare;

        // 检查用户余额
        const user = await db.prepare(`
            SELECT balance FROM users WHERE id = ?
        `).bind(userId).first();

        if (!user || user.balance < totalAmount) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '余额不足' 
                }), 
                {
                    status: 400,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                }
            );
        }

        // 创建计费记录
        const recordId = `bill-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        
        await db.prepare(`
            INSERT INTO billing_records (
                id, user_id, billing_type, resource_id, resource_type, 
                quantity, unit_price, total_amount, creator_id, creator_amount, 
                platform_amount, conversation_id, message_id, metadata, 
                status, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'completed', datetime('now'))
        `).bind(
            recordId, userId, billing_type, resource_id, resource_type,
            quantity, unitPrice, totalAmount, creatorId, creatorAmount,
            platformAmount, conversation_id, message_id, JSON.stringify(metadata)
        ).run();

        // 扣除用户余额
        const newBalance = user.balance - totalAmount;
        await db.prepare(`
            UPDATE users SET balance = ?, updated_at = datetime('now') WHERE id = ?
        `).bind(newBalance, userId).run();

        // 记录交易
        const transactionId = `tx-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        await db.prepare(`
            INSERT INTO currency_transactions (
                id, user_id, transaction_type, amount, balance_after, 
                description, reference_id, reference_type, created_at
            ) VALUES (?, ?, 'consume', ?, ?, ?, ?, 'billing_record', datetime('now'))
        `).bind(
            transactionId, userId, -totalAmount, newBalance,
            `${resource_type}消费 ${quantity}单位`, recordId
        ).run();

        // 如果有创作者，给创作者分成
        if (creatorId && creatorAmount > 0) {
            const creator = await db.prepare(`
                SELECT balance FROM users WHERE id = ?
            `).bind(creatorId).first();

            if (creator) {
                const creatorNewBalance = creator.balance + creatorAmount;
                await db.prepare(`
                    UPDATE users SET balance = ?, updated_at = datetime('now') WHERE id = ?
                `).bind(creatorNewBalance, creatorId).run();

                // 记录创作者收入
                const creatorTxId = `tx-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
                await db.prepare(`
                    INSERT INTO currency_transactions (
                        id, user_id, transaction_type, amount, balance_after, 
                        description, reference_id, reference_type, created_at
                    ) VALUES (?, ?, 'reward', ?, ?, ?, ?, 'billing_record', datetime('now'))
                `).bind(
                    creatorTxId, creatorId, creatorAmount, creatorNewBalance,
                    `Agent调用分成收入`, recordId
                ).run();
            }
        }

        return new Response(
            JSON.stringify({ 
                success: true, 
                message: '计费成功',
                data: {
                    recordId,
                    totalAmount,
                    newBalance
                }
            }), 
            {
                status: 200,
                headers: {
                    'Content-Type': 'application/json',
                },
            }
        );

    } catch (error) {
        console.error(error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误' 
            }), 
            {
                status: 500,
                headers: {
                    'Content-Type': 'application/json',
                },
            }
        );
    }
};

// 验证用户身份
async function verifyAuth(request: Request, env: Env): Promise<{success: boolean, userId?: string, message?: string}> {
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return { success: false, message: '缺少认证信息' };
    }

    const token = authHeader.substring(7);
    
    try {
        const parts = token.split('.');
        if (parts.length !== 3) {
            return { success: false, message: '无效的token格式' };
        }

        const payload = JSON.parse(atob(parts[1].replace(/-/g, '+').replace(/_/g, '/')));
        
        if (payload.exp < Math.floor(Date.now() / 1000)) {
            return { success: false, message: 'token已过期' };
        }

        return { success: true, userId: payload.userId };
    } catch (error) {
        return { success: false, message: '无效的token' };
    }
}
