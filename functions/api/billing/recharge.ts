interface Env {
    bgkv: KVNamespace;
    JWT_SECRET: string;
    bgdb: D1Database;
}

export const onRequestPost: PagesFunction<Env> = async (context) => {
    try {
        const { request, env } = context;
        
        // 验证用户身份
        const authResult = await verifyAuth(request, env);
        if (!authResult.success) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: authResult.message 
                }), 
                {
                    status: 401,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                }
            );
        }

        const userId = authResult.userId;
        const body = await request.json();
        const { amount, payment_method = 'internal' } = body;

        // 验证充值金额
        if (!amount || amount <= 0) {
            return new Response(
                JSON.stringify({ 
                    success: false, 
                    message: '充值金额必须大于0' 
                }), 
                {
                    status: 400,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                }
            );
        }

        const db = env.bgdb;
        const orderId = `recharge-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

        // 创建充值订单
        await db.prepare(`
            INSERT INTO recharge_orders (id, user_id, amount, payment_method, payment_status, created_at, updated_at)
            VALUES (?, ?, ?, ?, 'pending', datetime('now'), datetime('now'))
        `).bind(orderId, userId, amount, payment_method).run();

        // 如果是内部充值(管理员直接充值)，立即完成
        if (payment_method === 'internal') {
            // 检查是否为管理员
            const user = await db.prepare(`
                SELECT role FROM users WHERE id = ?
            `).bind(userId).first();

            if (user?.role === 'admin') {
                // 管理员可以直接充值
                await completeRecharge(orderId, userId, amount, userId, db);
                
                return new Response(
                    JSON.stringify({ 
                        success: true, 
                        message: '充值成功',
                        data: {
                            orderId,
                            amount,
                            status: 'completed'
                        }
                    }), 
                    {
                        status: 200,
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }
                );
            } else {
                return new Response(
                    JSON.stringify({ 
                        success: false, 
                        message: '只有管理员可以进行内部充值' 
                    }), 
                    {
                        status: 403,
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }
                );
            }
        }

        // 其他支付方式返回订单信息，等待支付
        return new Response(
            JSON.stringify({ 
                success: true, 
                message: '充值订单创建成功',
                data: {
                    orderId,
                    amount,
                    payment_method,
                    status: 'pending'
                }
            }), 
            {
                status: 200,
                headers: {
                    'Content-Type': 'application/json',
                },
            }
        );

    } catch (error) {
        console.error(error);
        return new Response(
            JSON.stringify({ 
                success: false, 
                message: '服务器错误' 
            }), 
            {
                status: 500,
                headers: {
                    'Content-Type': 'application/json',
                },
            }
        );
    }
};

// 完成充值
async function completeRecharge(orderId: string, userId: string, amount: number, approvedBy: string, db: D1Database) {
    // 开始事务处理
    try {
        // 更新订单状态
        await db.prepare(`
            UPDATE recharge_orders 
            SET payment_status = 'paid', admin_approved_by = ?, approved_at = datetime('now'), updated_at = datetime('now')
            WHERE id = ?
        `).bind(approvedBy, orderId).run();

        // 获取用户当前余额
        const user = await db.prepare(`
            SELECT balance FROM users WHERE id = ?
        `).bind(userId).first();

        const currentBalance = user?.balance || 0;
        const newBalance = currentBalance + amount;

        // 更新用户余额
        await db.prepare(`
            UPDATE users 
            SET balance = ?, updated_at = datetime('now')
            WHERE id = ?
        `).bind(newBalance, userId).run();

        // 记录交易
        const transactionId = `tx-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        await db.prepare(`
            INSERT INTO currency_transactions (id, user_id, transaction_type, amount, balance_after, description, reference_id, reference_type, created_at)
            VALUES (?, ?, 'recharge', ?, ?, ?, ?, 'recharge_order', datetime('now'))
        `).bind(transactionId, userId, amount, newBalance, `充值 ${amount} 字节币`, orderId).run();

    } catch (error) {
        console.error('充值处理失败:', error);
        throw error;
    }
}

// 验证用户身份
async function verifyAuth(request: Request, env: Env): Promise<{success: boolean, userId?: string, message?: string}> {
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return { success: false, message: '缺少认证信息' };
    }

    const token = authHeader.substring(7);
    
    try {
        // 简化的JWT验证 (实际应该使用完整的JWT库)
        const parts = token.split('.');
        if (parts.length !== 3) {
            return { success: false, message: '无效的token格式' };
        }

        const payload = JSON.parse(atob(parts[1].replace(/-/g, '+').replace(/_/g, '/')));
        
        // 检查过期时间
        if (payload.exp < Math.floor(Date.now() / 1000)) {
            return { success: false, message: 'token已过期' };
        }

        return { success: true, userId: payload.userId };
    } catch (error) {
        return { success: false, message: '无效的token' };
    }
}
