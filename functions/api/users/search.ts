interface Env {
  bgkv: KVNamespace;
  JWT_SECRET: string;
  bgdb: D1Database;
}

// 验证用户认证
async function verifyAuth(request: Request, env: Env) {
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('未提供认证令牌');
  }

  const token = authHeader.substring(7);
  
  try {
    const payload = await verifyToken(token, env.JWT_SECRET);
    if (!payload) {
      throw new Error('认证令牌无效');
    }

    const userId = payload.userId;
    
    const db = env.bgdb;
    const user = await db.prepare(`
      SELECT id, username, role
      FROM users
      WHERE id = ? AND status = 1
    `).bind(userId).first();

    if (!user) {
      throw new Error('用户不存在或已被禁用');
    }
    
    return user;
  } catch (error) {
    throw new Error('认证失败');
  }
}

// 搜索用户
export const onRequestGet: PagesFunction<Env> = async (context) => {
  try {
    const { request, env } = context;
    
    // 验证用户认证
    const currentUser = await verifyAuth(request, env);
    
    const url = new URL(request.url);
    const query = url.searchParams.get('q');
    
    if (!query || query.trim().length < 2) {
      return new Response(
        JSON.stringify({
          success: true,
          data: { users: [] }
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    
    const db = env.bgdb;
    
    // 搜索用户（排除当前用户）
    const users = await db.prepare(`
      SELECT id, username, email, nickname as display_name, status
      FROM users
      WHERE (username LIKE ? OR email LIKE ? OR nickname LIKE ?)
        AND status = 1
        AND id != ?
      ORDER BY username
      LIMIT 20
    `).bind(
      `%${query}%`,
      `%${query}%`, 
      `%${query}%`,
      currentUser.id
    ).all();

    return new Response(
      JSON.stringify({
        success: true,
        data: { users: users.results }
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
    
  } catch (error) {
    console.error('搜索用户失败:', error);
    return new Response(
      JSON.stringify({ 
        success: false, 
        message: error.message || '服务器内部错误' 
      }), 
      {
        status: error.message === '认证失败' ? 401 : 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
};

// JWT Token验证函数
async function verifyToken(token: string, secret: string): Promise<any> {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    const [headerEncoded, payloadEncoded, signature] = parts;

    // 验证签名
    const expectedSignature = await generateSignature(
      `${headerEncoded}.${payloadEncoded}`,
      secret
    );

    if (signature !== expectedSignature) {
      return null;
    }

    // 解码payload
    const payload = JSON.parse(atob(payloadEncoded.replace(/-/g, '+').replace(/_/g, '/')));

    // 检查过期时间
    if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
      return null;
    }

    return payload;
  } catch (error) {
    return null;
  }
}

// 生成签名
async function generateSignature(input: string, secret: string): Promise<string> {
  const encoder = new TextEncoder();
  const key = await crypto.subtle.importKey(
    'raw',
    encoder.encode(secret),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );

  const signature = await crypto.subtle.sign(
    'HMAC',
    key,
    encoder.encode(input)
  );

  return btoa(String.fromCharCode(...new Uint8Array(signature)))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}
