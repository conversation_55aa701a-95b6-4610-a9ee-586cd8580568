import OpenAI from 'openai';
import { modelConfigs } from '../../../src/config/aiCharacters';

// CORS headers
const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

export const onRequestOptions: PagesFunction = async () => {
    return new Response(null, { headers: corsHeaders });
};

interface Env {
    bgdb: D1Database;
    DASHSCOPE_API_KEY: string;
    HUNYUAN_API_KEY: string;
    ARK_API_KEY: string;
    GLM_API_KEY: string;
    DEEPSEEK_API_KEY: string;
    KIMI_API_KEY: string;
    BAIDU_API_KEY: string;
}

interface EnhancedChatRequest {
    group_id: number;
    message: string;
    sender_type: 'user' | 'agent';
    sender_id: number;
    reply_to?: number;
}

interface ChatResponse {
    success: boolean;
    data?: {
        message_id: number;
        agent_responses: Array<{
            agent_id: number;
            agent_name: string;
            response: string;
            response_time: number;
            task_type: string;
        }>;
        intent_analysis?: any;
        task_assignments?: any[];
    };
    message?: string;
}

export const onRequestPost: PagesFunction<Env> = async (context) => {
    try {
        const { env, data, request } = context;
        const body: EnhancedChatRequest = await request.json();
        
        if (!body.group_id || !body.message || !body.sender_type || !body.sender_id) {
            return new Response(
                JSON.stringify({
                    success: false,
                    message: '缺少必填参数'
                }),
                { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
            );
        }

        const db = env.bgdb;

        // 1. 保存用户消息到数据库
        const messageResult = await db.prepare(`
            INSERT INTO messages (group_id, sender_id, sender_type, content, reply_to)
            VALUES (?, ?, ?, ?, ?)
        `).bind(
            body.group_id,
            body.sender_id,
            body.sender_type,
            body.message,
            body.reply_to || null
        ).run();

        const messageId = messageResult.lastRowId;

        // 2. 如果是用户消息，触发超级智能体调度
        if (body.sender_type === 'user') {
            const agentResponses = await processUserMessage(env, body, messageId);
            
            return new Response(
                JSON.stringify({
                    success: true,
                    data: {
                        message_id: messageId,
                        agent_responses: agentResponses.responses,
                        intent_analysis: agentResponses.intent_analysis,
                        task_assignments: agentResponses.task_assignments
                    }
                }),
                {
                    status: 200,
                    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
                }
            );
        } else {
            // Agent消息，直接返回
            return new Response(
                JSON.stringify({
                    success: true,
                    data: {
                        message_id: messageId,
                        agent_responses: []
                    }
                }),
                {
                    status: 200,
                    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
                }
            );
        }

    } catch (error) {
        console.error('增强聊天API错误:', error);
        return new Response(
            JSON.stringify({
                success: false,
                message: '服务器错误，请稍后重试'
            }),
            {
                status: 500,
                headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            }
        );
    }
};

// 处理用户消息并触发Agent响应
async function processUserMessage(env: Env, request: EnhancedChatRequest, messageId: number) {
    const db = env.bgdb;

    // 获取群组信息和历史消息
    const groupInfo = await db.prepare(`
        SELECT * FROM groups_new WHERE id = ?
    `).bind(request.group_id).first();

    if (!groupInfo) {
        throw new Error('群组不存在');
    }

    // 获取最近的消息历史
    const history = await db.prepare(`
        SELECT 
            m.*,
            CASE 
                WHEN m.sender_type = 'user' THEN u.nickname
                WHEN m.sender_type = 'agent' THEN a.display_name
            END as sender_name
        FROM messages m
        LEFT JOIN users u ON m.sender_type = 'user' AND m.sender_id = u.id
        LEFT JOIN agents a ON m.sender_type = 'agent' AND m.sender_id = a.id
        WHERE m.group_id = ?
        ORDER BY m.created_at DESC
        LIMIT 10
    `).bind(request.group_id).all();

    // 调用超级智能体分析意图
    const intentResponse = await fetch('internal://super-agent/analyze', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            message: request.message,
            group_id: request.group_id,
            user_id: request.sender_id,
            history: history.results.reverse().map(h => ({
                role: h.sender_type === 'user' ? 'user' : 'assistant',
                content: h.content,
                sender_type: h.sender_type,
                sender_name: h.sender_name
            }))
        })
    });

    let intentAnalysis = null;
    let taskAssignments = [];

    if (intentResponse.ok) {
        const intentData = await intentResponse.json();
        if (intentData.success) {
            intentAnalysis = intentData.data.intent_analysis;

            // 调用任务调度
            const scheduleResponse = await fetch('internal://super-agent/schedule', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    group_id: request.group_id,
                    message_id: messageId,
                    intent_analysis: intentAnalysis,
                    available_agents: intentData.data.available_agents,
                    user_weights: intentData.data.user_weights
                })
            });

            if (scheduleResponse.ok) {
                const scheduleData = await scheduleResponse.json();
                if (scheduleData.success) {
                    taskAssignments = scheduleData.data.task_assignments;
                }
            }
        }
    }

    // 执行Agent响应
    const agentResponses = [];
    
    for (const assignment of taskAssignments) {
        try {
            const agentResponse = await executeAgentTask(env, assignment, request, history.results);
            agentResponses.push(agentResponse);

            // 保存Agent响应到数据库
            await db.prepare(`
                INSERT INTO messages (group_id, sender_id, sender_type, content, metadata)
                VALUES (?, ?, 'agent', ?, ?)
            `).bind(
                request.group_id,
                assignment.agent_id,
                agentResponse.response,
                JSON.stringify({
                    task_type: assignment.task_type,
                    response_time: agentResponse.response_time,
                    task_assignment_id: assignment.task_assignment_id
                })
            ).run();

            // 更新任务状态
            await db.prepare(`
                UPDATE task_assignments 
                SET status = 'completed', completed_at = CURRENT_TIMESTAMP, result = ?
                WHERE id = ?
            `).bind(agentResponse.response, assignment.task_assignment_id).run();

        } catch (error) {
            console.error(`Agent ${assignment.agent_id} 执行失败:`, error);
            
            // 更新任务状态为失败
            await db.prepare(`
                UPDATE task_assignments 
                SET status = 'failed', completed_at = CURRENT_TIMESTAMP, result = ?
                WHERE id = ?
            `).bind(error.message, assignment.task_assignment_id).run();
        }
    }

    return {
        responses: agentResponses,
        intent_analysis: intentAnalysis,
        task_assignments: taskAssignments
    };
}

// 执行单个Agent任务
async function executeAgentTask(env: Env, assignment: any, request: EnhancedChatRequest, history: any[]) {
    const db = env.bgdb;
    const startTime = Date.now();

    // 获取Agent信息
    const agent = await db.prepare(`
        SELECT * FROM agents WHERE id = ?
    `).bind(assignment.agent_id).first();

    if (!agent) {
        throw new Error(`Agent ${assignment.agent_id} 不存在`);
    }

    // 构建对话上下文
    const messages = [
        {
            role: 'system',
            content: `你是${agent.display_name}，${agent.description}。当前任务类型：${assignment.task_type}。任务描述：${assignment.task_description}`
        },
        ...history.slice(-5).map(h => ({
            role: h.sender_type === 'user' ? 'user' : 'assistant',
            content: `${h.sender_name}: ${h.content}`
        })),
        {
            role: 'user',
            content: request.message
        }
    ];

    // 根据Agent类型调用相应的API
    let response;
    
    if (agent.api_type === 'internal' || agent.is_super_agent) {
        // 内部Agent，使用配置的模型
        response = await callInternalAgent(env, agent, messages);
    } else {
        // 外部Agent，调用其API
        response = await callExternalAgent(agent, messages);
    }

    const responseTime = Date.now() - startTime;

    return {
        agent_id: agent.id,
        agent_name: agent.display_name,
        response: response,
        response_time: responseTime,
        task_type: assignment.task_type
    };
}

// 调用内部Agent
async function callInternalAgent(env: Env, agent: any, messages: any[]) {
    // 使用现有的chat API逻辑
    const modelConfig = modelConfigs.find(config => config.model === "qwen-plus");
    if (!modelConfig) {
        throw new Error('模型配置未找到');
    }

    const apiKey = env[modelConfig.apiKey];
    if (!apiKey) {
        throw new Error('API密钥未配置');
    }

    const openai = new OpenAI({
        apiKey: apiKey,
        baseURL: modelConfig.baseURL
    });

    const response = await openai.chat.completions.create({
        model: modelConfig.model,
        messages: messages,
        temperature: 0.7,
        max_tokens: 1000
    });

    return response.choices[0]?.message?.content || '抱歉，我无法生成回复。';
}

// 调用外部Agent
async function callExternalAgent(agent: any, messages: any[]) {
    const userMessage = messages[messages.length - 1].content;
    
    // 根据API类型调用不同的接口
    switch (agent.api_type) {
        case 'dify':
            return await callDifyAgent(agent, userMessage);
        case 'fastgpt':
            return await callFastGPTAgent(agent, userMessage);
        case 'custom':
            return await callCustomAgent(agent, userMessage);
        default:
            throw new Error(`不支持的API类型: ${agent.api_type}`);
    }
}

// 调用Dify Agent
async function callDifyAgent(agent: any, message: string) {
    const response = await fetch(`${agent.api_url}/chat-messages`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${agent.api_key}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            inputs: {},
            query: message,
            response_mode: 'blocking',
            user: 'agentgroup-user'
        })
    });

    if (!response.ok) {
        throw new Error(`Dify API错误: ${response.status}`);
    }

    const data = await response.json();
    return data.answer || data.message || '无响应内容';
}

// 调用FastGPT Agent
async function callFastGPTAgent(agent: any, message: string) {
    const response = await fetch(`${agent.api_url}/api/v1/chat/completions`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${agent.api_key}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            chatId: `agentgroup-${Date.now()}`,
            stream: false,
            detail: false,
            messages: [{ content: message, role: 'user' }]
        })
    });

    if (!response.ok) {
        throw new Error(`FastGPT API错误: ${response.status}`);
    }

    const data = await response.json();
    return data.choices?.[0]?.message?.content || '无响应内容';
}

// 调用自定义Agent
async function callCustomAgent(agent: any, message: string) {
    const response = await fetch(agent.api_url, {
        method: 'POST',
        headers: {
            'Authorization': agent.api_key ? `Bearer ${agent.api_key}` : undefined,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            message: message,
            user: 'agentgroup-user'
        })
    });

    if (!response.ok) {
        throw new Error(`自定义API错误: ${response.status}`);
    }

    const data = await response.json();
    return data.response || data.message || data.content || '无响应内容';
}
