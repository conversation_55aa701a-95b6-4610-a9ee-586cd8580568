# AgentGroup 环境配置文件示例
# 复制此文件为 .env 并填入实际配置值

# 应用基础配置
NODE_ENV=development
PORT=3000
APP_URL=http://localhost:3000

# 数据库配置
DATABASE_URL=your_database_url_here

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-here-change-in-production
JWT_EXPIRES_IN=7d

# 超级智能体配置
SUPER_AGENT_PROVIDER=openai
SUPER_AGENT_API_KEY=your_openai_api_key_here
SUPER_AGENT_MODEL=gpt-4
SUPER_AGENT_TEMPERATURE=0.7
SUPER_AGENT_MAX_TOKENS=2000
SUPER_AGENT_ENABLED=true

# 超级智能体系统提示词
SUPER_AGENT_SYSTEM_PROMPT="你是AgentGroup平台的超级智能体，负责协调和管理其他专业智能体。

你的主要职责：
1. 理解用户需求并分析任务复杂度
2. 选择合适的专业智能体来处理特定任务
3. 协调多个智能体之间的协作
4. 整合各智能体的输出，提供统一的解决方案
5. 在多用户场景下，根据用户参与度权重理解综合意图

工作原则：
- 始终以用户需求为中心
- 选择最适合的智能体处理任务
- 确保输出的准确性和实用性
- 保持友好、专业的沟通风格
- 在不确定时主动询问澄清"

# 其他AI服务配置（可选）
CLAUDE_API_KEY=your_claude_api_key_here
QWEN_API_KEY=your_qwen_api_key_here
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# 邮件服务配置（用于邀请功能）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password
SMTP_FROM=AgentGroup <<EMAIL>>

# 短信服务配置（用于邀请功能）
SMS_PROVIDER=aliyun
SMS_ACCESS_KEY=your_sms_access_key
SMS_SECRET_KEY=your_sms_secret_key
SMS_SIGN_NAME=AgentGroup
SMS_TEMPLATE_CODE=SMS_123456789

# 文件存储配置
STORAGE_PROVIDER=local
# 如果使用云存储
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=agentgroup-files

# Redis配置（用于缓存和会话）
REDIS_URL=redis://localhost:6379

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# 安全配置
CORS_ORIGIN=http://localhost:3000
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100

# 内容审核配置
CONTENT_MODERATION_ENABLED=true
CONTENT_MODERATION_PROVIDER=openai
CONTENT_MODERATION_API_KEY=your_moderation_api_key

# 监控配置
SENTRY_DSN=your_sentry_dsn_here
ANALYTICS_ENABLED=true

# 计费系统配置
BILLING_ENABLED=true
DEFAULT_USER_BALANCE=100.0
INFRASTRUCTURE_COST_PER_MESSAGE=0.01
DEPARTMENT_AGENT_COST_PER_MESSAGE=0.05
PERSONAL_AGENT_COST_PER_MESSAGE=0.1

# 权限系统配置
DEFAULT_USER_ROLE=user
ADMIN_EMAILS=<EMAIL>,<EMAIL>

# LDAP/RADIUS配置（企业版）
LDAP_ENABLED=false
LDAP_URL=ldap://your-ldap-server:389
LDAP_BIND_DN=cn=admin,dc=company,dc=com
LDAP_BIND_PASSWORD=your_ldap_password
LDAP_SEARCH_BASE=ou=users,dc=company,dc=com
LDAP_SEARCH_FILTER=(uid={{username}})

RADIUS_ENABLED=false
RADIUS_HOST=your-radius-server
RADIUS_PORT=1812
RADIUS_SECRET=your_radius_secret

# 默认管理员账户
DEFAULT_ADMIN_USERNAME=admin
DEFAULT_ADMIN_PASSWORD=admin123
DEFAULT_ADMIN_EMAIL=<EMAIL>

# 功能开关
FEATURE_MULTI_USER_CHAT=true
FEATURE_AGENT_MARKETPLACE=true
FEATURE_BILLING_SYSTEM=true
FEATURE_ENTERPRISE_SSO=false

# 性能配置
MAX_CONCURRENT_REQUESTS=100
REQUEST_TIMEOUT=30000
MAX_FILE_SIZE=10485760
MAX_MESSAGE_LENGTH=4000

# 开发配置
DEBUG=true
MOCK_AI_RESPONSES=false
ENABLE_API_DOCS=true
