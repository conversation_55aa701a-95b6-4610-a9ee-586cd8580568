首先理解项目整体的架构和代码逻辑，以及各个组件之间的关联关系，我需要你参考这个项目的架构和UI设计重新帮我形成新的工程，需要实现以下几个功能：
1、群聊的面相对象是真人用户，群聊的其他角色可以是真人用户也可以是智能体Agent，所以这里面涉及到用户概念，Agent的加入需要由超级智能体来规划和拉进群；
2、超级智能体需要理解真人用户的意图，在Agent store里选择适合完成真人用户任务的一个或者多个智能体来完成，在Agent完成用户任务的时候，可以由超级智能体拆分任务（可以参考Sequential Thinking这个mcp-server的拆分任务的能力）分发给单一Agent完成后再进行合并，也可以由单一Agent独立完成，如果没有@到的Agent（即没有分配到任务的Agent）就不要随意发言以免干扰整体任务；
3、在Agent store里可以自动/手动添加Agent进来，需要宣告这些Agent的能力以便超级智能体知道他们的能力边界，这点可以参考mcp-server的方式，需要将mcp-tools标签化，当然也可以通过超级智能体去询问Agent具备什么样的能力，通过Agent的返回内容（自然语言/结构化语言类似json）实时打标签实现，另外最好每个Agent只允许唯一名字，这样不容易拉错不同的Agent进来。
4、超级智能体需要给Agent store里面的Agent建立实时打分机制，通过与真人用户对话的过程中了解或者判断到底这个Agent是否能完成它所宣称的能力和功能，如果持续无法完成或者真人用户持续抱怨或反馈任务没有很好完成，需要通过打分的方式建立拉该Agent进群的权重，以保证优秀的Agent可以被持续使用，而不好的Agent会被回收，这样从后台就可以看到哪些Agent被调用的多，哪些Agent被调用的少或者没有被调用。
5、Agent的接入都以API的方式接入进来，可以参考dify的接入方式https://docs.dify.ai/zh-hans/guides/application-publishing/developing-with-apis，包括hiagent、fastgpt等也是以这种方式接入，相关的url和key等变量可以留待客户填写，也可以提供agent的能力填写窗口，方便用户去填写该agent的能力边界用以后续超级智能体给Agent打标签。
7、整体UI和用户交互要以现代WEB的标准去设计，整体清爽干净，交互逻辑简单明了，后续Agent如果具备类似生成word文档/PPT材料等能力的话，也要参考微信群里可以由用户上传文件的方式将Agent生成的附件进行上传并提供真人用户下载。
8、群里只有1个真人用户的时候，超级智能体理解用户意图100%以该真人用户为准，当群里有超过1个及以上的真人用户之后，超级智能体理解用户意图的比例按照真人用户数量做加权平均，权重为真人用户发言的多少为准，即发言多的权重越高（参与度高）但是每个真人用户的发言都必须重视，作为Agent执行任务的上下文，当然涉黄涉暴涉恐的等内容要严格筛选。
9、提供超级智能体的配置，大模型配置参数、Prompt提示词配置、MCP-tools配置等，当然你也可以参考类似genspark/manus等成熟的超级智能体给出相关的配置建议和菜单。
10、最后你再整体理解一下本次项目工程的内容，并根据上述需求看是否还有补充和完善的地方，最后形成产品PRD文档供我确认，待我确认之后进行整体项目开发，你需要再确认一下是在botgroup.chat这个开源工程上继续开发还是新起一个工程来开发。


——————
1、增加计费功能，大致分三层：第一层是算力计费，分为GPU驱动的大模型等能力输出以tokens方式计费，以CPU驱动的Agent虚拟机、sandbox等以调用时间/包月的方式输出，一般由集团信息中心作为收费部门向其他部门计费；第二层是各部门自建的Agent（部门），按照调用次数/协同次数计费，意在驱动各部门建设更好的Agent服务其他部门，其中需要用到的算力资源由信息中心提供并收费；第三层是各位员工按照自己的理解或者工作任务的完成过程中建立的Agent（个人），其他部门或者个人在调用过程中认为很好的完成工作任务，也需要向创作者付费。
2、计费单位：建议以某种数字货币的单位基准，可以在集团内部发数字货币，比如字节币等等，该数字货币可以用于在字节商城（内部员工福利商城）购买相关产品（衣服文创等），也可以作为部门结算的相关依据，可以考虑使用区块链的方式进行可信认证。
3、agent store要有角色和权限划分，可以参考apple的app store/安卓手机的应用商店的方式，至少有三层权限和角色：最大权限是管理员admin，可以上架智能体，下架智能体，编辑智能体等，具体参考app store/安卓手机的应用商店；其次是部门管理员，可以上架部门智能体，下架部门智能体，编辑部门智能体，但是不可以修改和上下架由管理员上架的智能体，也不可以修改其他横向部门上架的智能体；最后是个人开发者，可以上架个人智能体，下架个人智能体，编辑个人智能体，但是不可以修改和上下架由管理员上架的智能体，也不可以修改部门上架的智能体。个人开发者和员工使用者的角色和权限应该是一致的，因为我们要鼓励全员使用AI。最后，在计费功能里面也要有相对应的体现和计费逻辑，这一点你自己合并设计。
4、admin账户进入之后可以创建用户（部门管理员/普通用户两种角色），也要支持SSO接入，比如ldap/radius等一般公司和企业使用的用户认证系统。当前先创建用户名：admin/密码：admin123这个账户给我，我自行进去创建其他两种角色的用户来测试。
5、普通用户的群聊应该限制在5个以内，超过了就需要用户删除以前的群聊才能再次新建。
我用admin/admin123登录报错，你检查一下什么问题，然后不要跳过权限验证，现在不用登录也可以访问到智能体页面。解决这个问题后，进入的功能要做一下调整，第一个页面应该是超级智能体，可以参考genspark的风格https://www.genspark.ai





你可以自己调用mcp访问浏览器做UI复刻，把整体UI和配色等风格按照genspark直接复刻就行，现在的UI完全不行，全部重构了。第一个功能是超级智能体，让用户可以直接跟超级智能体对话，第二个功能就是建立群聊,当建立好群聊之后，群聊就上升为第二个tab标签，然后建立群聊的功能自动下发，直到用户建立了5个群聊tab，群聊标签和超级智能体的标签可以放在同一级，然后侧边啦下面的功能按钮等才是新建各种群聊，普通用户的群聊应该限制在5个以内，超过了就需要用户删除以前的群聊才能再次新建。

首先有两个大问题需要调整，1、群聊窗口的用户交互逻辑：“我的群聊”下面每个群聊菜单是不可以被删除的，除非在群聊管理里面被删除了，而且还要提示用户，删除之后数据不可恢复，顶部的群聊页面后面的叉号退出按钮只是关闭当前页面，不能影响左侧菜单栏的群聊菜单，现在的交互逻辑是一起删除了，但是实际又没有删除，刷新一下又出来了。2、新建群聊这个交互逻辑也有问题，现在是弹出式页面，如果agent有很多，就会变的很长的内容让用户可读性很差，所以要改成类似https://www.genspark.ai/agents一样的交互方式，把agent平铺在整个页面上，然后让用户选择，并且还要增加搜索/筛选按钮，可以方便用户快速找需要的agent，而且最好还要有agent分类，这个可以跟智能体商店里面的agent做分类，意味着智能体商店里面也对应的有agent分类，最后的问题是你先整体理解项目的代码和架构，删除跟当前项目和功能不相关的代码和文件，并使用mcp-tools仔细检查每一个页面上的所有按钮，务必都需要做到有响应和反馈才行，现在好多按钮都点不动。

1、浅色主题下智能体商店里面的内容有好多都是白色字体，这在浅色主题下完全看不清楚，你全部统一调整
2、用户管理页面里面没有手动新增用户按钮了，需要维持这个功能，让用户可以新增，另外admin系统管理员可以删除手动新增的用户，也可以帮助所有用户重置密码，SSO接入进来的用户要带他的组织架构信息
3、个人中心里面的个人设置修改密码和绑定邮箱的外轮廓都太长了，太丑了，需要做UI调整美观大方，而且这两个功能都没有响应，点击修改密码和绑定邮箱的字样都没有任何弹出界面可以提供功能实现，修复这个问题。
4、左侧栏目的“我的群聊”是不能超过5个，但是可以被其他用户拉进群，就再多增加一个栏目叫公共群聊，就是被其他用户拉进群的群聊信息和列表，每个用户最多可以被拉进10个群，如果超过10个群还被其他用户拉群，就弹出提示不允许了。
5、整体系统没有一个通知栏，可以参考一些现代网站的copilot放在整体页面的右上角提供通知，类似ios里面的app旁边的红色数字的样式，用户点击通知的圆形按钮就可以弹出对应的通知消息，能看到被谁@了，新消息通知


我们开始一个功能一个功能的开始开发了，首先你需要根据下面的需求形成PRD文档，我确认之后进行产品开发，记住所有的功能开发都要基于当前的框架和UI风格来进行，不要跳出现有的基础框架和风格。需求是：
1、完全复刻https://www.genspark.ai/agents?type=super_agent的能力，具体超级智能体superagent有哪些功能可以搜一下，我认为肯定有MCP-tools的功能，任务规划能力Plan，附件上传等等，开源组件可以参考https://github.com/bytedance/deer-flow和https://github.com/dtyq/magic，另外应该在本地内置几个mcp-tools：Fetch/Sequential Thinking/EdgeOne Pages/arXiv/Context7，通过开关按钮开启或者关闭，其中需要在系统安装的时候把这些mcp-server安装到本地，当然也支持自定义mcp-tools，通过SSE或者StreamableHttp进行链接，要素有：名称，类型（StreamableHttp/SSE），服务器 URL，等等
2、超级智能体应该还具备类似genspark和manus等著名超级智能体的所有功能，你可以搜索相关材料学习一下然后最后形成完整的PRD文档。


