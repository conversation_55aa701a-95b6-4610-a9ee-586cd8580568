{"name": "agentgroup", "version": "1.0.0", "private": true, "scripts": {"dev": "wrangler pages dev dist --compatibility-date=2023-05-18 --port=3000", "dev:vite": "vite", "build": "vite build", "preview": "vite preview", "dev:full": "npm run build && npm run dev"}, "dependencies": {"@alicloud/credentials": "^2.4.2", "@alicloud/dysmsapi20170525": "^3.1.1", "@fontsource/audiowide": "^5.1.1", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.1.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dom-to-image": "^2.6.0", "dom-to-image-more": "^3.5.0", "html2canvas": "^1.4.1", "katex": "^0.16.21", "lucide-react": "^0.263.1", "openai": "^4.83.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-github-btn": "^1.4.0", "react-markdown": "^9.0.3", "react-router-dom": "^7.4.0", "rehype-katex": "^7.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sonner": "^2.0.0", "tailwind-merge": "^2.6.0", "tailwind-scrollbar-hide": "^2.0.0", "tailwindcss-animate": "^1.0.7", "wrangler": "^3.112.0", "zustand": "^5.0.3"}, "devDependencies": {"@shadcn/ui": "^0.0.4", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.13.1", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.20", "postcss": "^8.5.2", "rollup-plugin-copy": "^3.5.0", "tailwindcss": "^3.4.17", "typescript": "^5.0.2", "vite": "^5.0.0"}}