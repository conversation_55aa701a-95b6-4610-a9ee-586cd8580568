#!/usr/bin/env node

// 生成测试数据脚本
const BASE_URL = 'http://localhost:3001'

async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, options)
    return {
      ok: response.ok,
      status: response.status,
      data: response.ok ? await response.json() : await response.text()
    }
  } catch (error) {
    return {
      ok: false,
      status: 0,
      error: error.message
    }
  }
}

async function getAuthToken() {
  const loginResponse = await makeRequest(`${BASE_URL}/api/auth/login`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username: 'admin', password: 'admin123' })
  })
  
  if (loginResponse.ok && loginResponse.data.success) {
    return loginResponse.data.data.token
  }
  return null
}

async function createTestUsers(authToken) {
  console.log('\n👥 创建测试用户...')
  
  const testUsers = [
    { username: 'alice', password: 'alice123', nickname: '爱丽丝', phone: '13800138001' },
    { username: 'bob', password: 'bob123', nickname: '鲍勃', phone: '13800138002' },
    { username: 'charlie', password: 'charlie123', nickname: '查理', phone: '13800138003' },
    { username: 'diana', password: 'diana123', nickname: '戴安娜', phone: '13800138004' },
    { username: 'eve', password: 'eve123', nickname: '夏娃', phone: '13800138005' },
    { username: 'frank', password: 'frank123', nickname: '弗兰克', phone: '13800138006' },
    { username: 'grace', password: 'grace123', nickname: '格蕾丝', phone: '13800138007' },
    { username: 'henry', password: 'henry123', nickname: '亨利', phone: '13800138008' }
  ]
  
  const createdUsers = []
  
  for (const user of testUsers) {
    // 直接插入数据库（通过API）
    const response = await makeRequest(`${BASE_URL}/api/test-db`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        action: 'insert_user',
        data: user
      })
    })
    
    if (response.ok) {
      console.log(`✅ 创建用户: ${user.nickname} (${user.username})`)
      createdUsers.push(user)
    } else {
      console.log(`⚠️ 创建用户失败: ${user.nickname} - ${response.error || response.data}`)
    }
  }
  
  return createdUsers
}

async function createTestGroups(authToken) {
  console.log('\n🏠 创建测试群聊...')
  
  const testGroups = [
    { name: '技术讨论群', description: '讨论技术问题和分享经验', group_type: 'mixed' },
    { name: '产品设计团队', description: '产品设计相关讨论', group_type: 'mixed' },
    { name: '市场营销部', description: '市场营销策略讨论', group_type: 'human_only' },
    { name: '开源项目协作', description: '开源项目开发协作', group_type: 'mixed' },
    { name: 'AI研究小组', description: '人工智能技术研究', group_type: 'ai_only' }
  ]
  
  const createdGroups = []
  
  for (const group of testGroups) {
    const response = await makeRequest(`${BASE_URL}/api/groups/create`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(group)
    })
    
    if (response.ok && response.data.success) {
      console.log(`✅ 创建群聊: ${group.name} (ID: ${response.data.data.id})`)
      createdGroups.push({
        ...group,
        id: response.data.data.id
      })
    } else {
      console.log(`⚠️ 创建群聊失败: ${group.name} - ${response.data?.message || response.error}`)
    }
  }
  
  return createdGroups
}

async function addMembersToGroups(authToken, groups) {
  console.log('\n👥 添加成员到群聊...')
  
  // 模拟用户ID（假设从2开始，因为1是admin）
  const userIds = ['2', '3', '4', '5', '6', '7', '8', '9']
  
  for (const group of groups) {
    // 随机选择3-6个用户加入群聊
    const memberCount = Math.floor(Math.random() * 4) + 3
    const selectedUsers = userIds.slice(0, memberCount)
    
    for (const userId of selectedUsers) {
      const response = await makeRequest(`${BASE_URL}/api/test-db`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'add_group_member',
          data: {
            group_id: group.id,
            member_id: userId,
            member_type: 'user',
            role: 'member'
          }
        })
      })
      
      if (response.ok) {
        console.log(`✅ 用户 ${userId} 加入群聊: ${group.name}`)
      }
    }
  }
}

async function generateMessages(authToken, groups) {
  console.log('\n💬 生成测试消息...')
  
  const sampleMessages = [
    '大家好！很高兴加入这个群聊。',
    '今天的会议讨论得很有意思。',
    '有人能帮我看看这个问题吗？',
    '分享一个有用的工具给大家。',
    '周末愉快！',
    '这个方案我觉得可行性很高。',
    '需要更多的数据来支持这个决策。',
    '感谢大家的积极参与！',
    '明天的deadline大家准备好了吗？',
    '这个bug已经修复了。',
    '新功能上线了，大家可以试试。',
    '会议纪要我稍后发给大家。',
    '有什么问题随时联系我。',
    '项目进展顺利，按计划进行。',
    '需要协调一下资源分配。'
  ]
  
  for (const group of groups) {
    // 每个群聊生成10-20条消息
    const messageCount = Math.floor(Math.random() * 11) + 10
    
    for (let i = 0; i < messageCount; i++) {
      const senderId = Math.floor(Math.random() * 8) + 2 // 用户ID 2-9
      const message = sampleMessages[Math.floor(Math.random() * sampleMessages.length)]
      
      const response = await makeRequest(`${BASE_URL}/api/test-db`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'add_message',
          data: {
            group_id: group.id,
            sender_id: senderId,
            sender_type: 'user',
            content: message,
            message_type: 'text'
          }
        })
      })
      
      if (response.ok) {
        console.log(`✅ 群聊 ${group.name} 新增消息`)
      }
    }
  }
}

async function createTestAgents(authToken) {
  console.log('\n🤖 创建测试智能体...')
  
  const testAgents = [
    {
      name: 'CodeReviewer',
      display_name: '代码审查助手',
      description: '专业的代码审查和建议',
      api_url: 'http://localhost:3000/api/agents/code-reviewer',
      api_key: 'test-key-001',
      api_type: 'dify',
      avatar_url: null
    },
    {
      name: 'DataAnalyst',
      display_name: '数据分析师',
      description: '数据分析和可视化专家',
      api_url: 'http://localhost:3000/api/agents/data-analyst',
      api_key: 'test-key-002',
      api_type: 'dify',
      avatar_url: null
    },
    {
      name: 'ProjectManager',
      display_name: '项目管理助手',
      description: '项目规划和进度跟踪',
      api_url: 'http://localhost:3000/api/agents/project-manager',
      api_key: 'test-key-003',
      api_type: 'dify',
      avatar_url: null
    }
  ]
  
  const createdAgents = []
  
  for (const agent of testAgents) {
    const response = await makeRequest(`${BASE_URL}/api/test-db`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        action: 'insert_agent',
        data: agent
      })
    })
    
    if (response.ok) {
      console.log(`✅ 创建智能体: ${agent.display_name}`)
      createdAgents.push(agent)
    } else {
      console.log(`⚠️ 创建智能体失败: ${agent.display_name}`)
    }
  }
  
  return createdAgents
}

async function main() {
  console.log('🚀 开始生成测试数据...')
  
  // 获取认证令牌
  const authToken = await getAuthToken()
  if (!authToken) {
    console.error('❌ 无法获取认证令牌')
    return
  }
  
  console.log('✅ 获取认证令牌成功')
  
  // 创建测试用户
  const users = await createTestUsers(authToken)
  
  // 创建测试群聊
  const groups = await createTestGroups(authToken)
  
  // 添加成员到群聊
  if (groups.length > 0) {
    await addMembersToGroups(authToken, groups)
    
    // 生成测试消息
    await generateMessages(authToken, groups)
  }
  
  // 创建测试智能体
  const agents = await createTestAgents(authToken)
  
  console.log('\n🎉 测试数据生成完成！')
  console.log(`📊 统计信息:`)
  console.log(`   - 用户: ${users.length}个`)
  console.log(`   - 群聊: ${groups.length}个`)
  console.log(`   - 智能体: ${agents.length}个`)
}

// 运行脚本
main().catch(console.error)
