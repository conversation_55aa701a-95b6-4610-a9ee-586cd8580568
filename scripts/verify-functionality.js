#!/usr/bin/env node

// 功能验证脚本 - 直接测试API端点
const BASE_URL = 'http://localhost:3001'

async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, options)
    return {
      ok: response.ok,
      status: response.status,
      data: response.ok ? await response.json() : await response.text()
    }
  } catch (error) {
    return {
      ok: false,
      status: 0,
      error: error.message
    }
  }
}

async function testAuthentication() {
  console.log('\n🔐 测试认证功能...')
  
  // 测试正确登录
  const loginResponse = await makeRequest(`${BASE_URL}/api/auth/login`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username: 'admin', password: 'admin123' })
  })
  
  if (loginResponse.ok && loginResponse.data.success) {
    console.log('✅ 管理员登录成功')
    console.log(`   用户: ${loginResponse.data.data.user.username}`)
    console.log(`   角色: ${loginResponse.data.data.user.role}`)
    console.log(`   余额: ${loginResponse.data.data.user.balance}`)
    return loginResponse.data.data.token
  } else {
    console.log('❌ 管理员登录失败')
    console.log(`   错误: ${loginResponse.error || loginResponse.data}`)
    return null
  }
}

async function testDatabase() {
  console.log('\n📊 测试数据库功能...')
  
  const dbResponse = await makeRequest(`${BASE_URL}/api/init-db`)
  
  if (dbResponse.ok && dbResponse.data.success) {
    console.log('✅ 数据库连接正常')
    console.log(`   状态: ${dbResponse.data.message}`)
  } else {
    console.log('❌ 数据库连接失败')
    console.log(`   错误: ${dbResponse.error || dbResponse.data}`)
  }
}

async function testGroupFunctionality(authToken) {
  console.log('\n👥 测试群聊功能...')
  
  if (!authToken) {
    console.log('⚠️ 跳过群聊测试：无认证令牌')
    return
  }
  
  // 测试群聊列表
  console.log(`   使用认证令牌: ${authToken ? authToken.substring(0, 20) + '...' : 'null'}`)
  const groupsResponse = await makeRequest(`${BASE_URL}/api/groups/list`, {
    headers: { 'Authorization': `Bearer ${authToken}` }
  })
  
  if (groupsResponse.ok && groupsResponse.data.success) {
    console.log('✅ 群聊列表获取成功')
    console.log(`   我的群聊: ${groupsResponse.data.data.my_groups.length}个`)
    console.log(`   公共群聊: ${groupsResponse.data.data.public_groups.length}个`)
    
    // 测试群聊创建
    const testGroupName = `测试群聊_${Date.now()}`
    const createResponse = await makeRequest(`${BASE_URL}/api/groups/create`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: testGroupName,
        description: '自动化测试群聊',
        type: 'private'
      })
    })
    
    if (createResponse.ok && createResponse.data.success) {
      console.log('✅ 群聊创建成功')
      console.log(`   群聊ID: ${createResponse.data.data.id}`)
      console.log(`   群聊名称: ${createResponse.data.data.name}`)
      return createResponse.data.data.id
    } else {
      console.log('⚠️ 群聊创建失败（可能是数量限制）')
      console.log(`   原因: ${createResponse.data.message || createResponse.error}`)
    }
  } else {
    console.log('❌ 群聊列表获取失败')
    console.log(`   错误: ${groupsResponse.error || groupsResponse.data}`)
  }
  
  return null
}

async function testUserSearch(authToken) {
  console.log('\n🔍 测试用户搜索功能...')
  
  if (!authToken) {
    console.log('⚠️ 跳过用户搜索测试：无认证令牌')
    return
  }
  
  const searchResponse = await makeRequest(`${BASE_URL}/api/users/search?q=admin`, {
    headers: { 'Authorization': `Bearer ${authToken}` }
  })
  
  if (searchResponse.ok && searchResponse.data.success) {
    console.log('✅ 用户搜索成功')
    console.log(`   找到用户: ${searchResponse.data.data.length}个`)
    if (searchResponse.data.data.length > 0) {
      console.log(`   第一个用户: ${searchResponse.data.data[0].username}`)
    }
  } else {
    console.log('❌ 用户搜索失败')
    console.log(`   错误: ${searchResponse.error || searchResponse.data}`)
  }
}

async function testWeightAnalysis(authToken, groupId) {
  console.log('\n📈 测试权重分析功能...')
  
  if (!authToken) {
    console.log('⚠️ 跳过权重分析测试：无认证令牌')
    return
  }
  
  if (!groupId) {
    console.log('⚠️ 跳过权重分析测试：无群聊ID')
    return
  }
  
  const weightsResponse = await makeRequest(`${BASE_URL}/api/groups/${groupId}/weights`, {
    headers: { 'Authorization': `Bearer ${authToken}` }
  })
  
  if (weightsResponse.ok && weightsResponse.data.success) {
    console.log('✅ 权重分析成功')
    console.log(`   群聊ID: ${weightsResponse.data.data.group_id}`)
    console.log(`   用户数量: ${weightsResponse.data.data.total_users}`)
    console.log(`   建议数量: ${weightsResponse.data.data.recommendations.length}`)
  } else {
    console.log('❌ 权重分析失败')
    console.log(`   错误: ${weightsResponse.error || weightsResponse.data}`)
  }
}

async function testAPIEndpoints() {
  console.log('\n🔗 测试API端点存在性...')
  
  const endpoints = [
    '/api/init-db',
    '/api/auth/login',
    '/api/groups/list',
    '/api/groups/create',
    '/api/users/search'
  ]
  
  for (const endpoint of endpoints) {
    const response = await makeRequest(`${BASE_URL}${endpoint}`)
    if (response.status !== 404) {
      console.log(`✅ ${endpoint} (状态: ${response.status})`)
    } else {
      console.log(`❌ ${endpoint} (404 - 不存在)`)
    }
  }
}

async function main() {
  console.log('🚀 开始功能验证测试...')
  console.log(`📍 测试服务器: ${BASE_URL}`)
  
  // 测试数据库
  await testDatabase()
  
  // 测试认证
  const authToken = await testAuthentication()
  
  // 测试群聊功能
  const groupId = await testGroupFunctionality(authToken)
  
  // 测试用户搜索
  await testUserSearch(authToken)
  
  // 测试权重分析
  await testWeightAnalysis(authToken, groupId)
  
  // 测试API端点
  await testAPIEndpoints()
  
  console.log('\n🎉 功能验证测试完成！')
}

// 运行测试
main().catch(console.error)
