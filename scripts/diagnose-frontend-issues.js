#!/usr/bin/env node

/**
 * 前端问题诊断脚本
 * 用于检测和诊断前端页面跳转、点击响应和数据保存问题
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 开始前端问题诊断...\n');

// 诊断结果收集
const diagnosticResults = {
  clickIssues: [],
  navigationIssues: [],
  apiIssues: [],
  databaseIssues: [],
  recommendations: []
};

// 1. 检查前端构建状态
function checkBuildStatus() {
  console.log('📦 检查前端构建状态...');
  try {
    execSync('npm run build', { stdio: 'pipe' });
    console.log('✅ 前端构建成功');
    return true;
  } catch (error) {
    console.log('❌ 前端构建失败');
    console.log('   错误:', error.message);
    diagnosticResults.recommendations.push('修复前端构建错误');
    return false;
  }
}

// 2. 运行诊断测试
function runDiagnosticTests() {
  console.log('\n🧪 运行诊断测试...');
  try {
    const testOutput = execSync('npm run test -- src/test/diagnosis/click-and-save-diagnosis.test.tsx', { 
      encoding: 'utf8',
      stdio: 'pipe'
    });
    console.log('✅ 诊断测试通过');
    console.log('   测试输出:', testOutput.substring(0, 500) + '...');
    return true;
  } catch (error) {
    console.log('❌ 诊断测试失败');
    console.log('   错误:', error.message);
    diagnosticResults.clickIssues.push('测试执行失败');
    return false;
  }
}

// 3. 检查路由配置
function checkRouteConfiguration() {
  console.log('\n🛣️  检查路由配置...');
  const routesPath = path.join(__dirname, '../src/routes.tsx');
  
  if (!fs.existsSync(routesPath)) {
    console.log('❌ 路由配置文件不存在');
    diagnosticResults.navigationIssues.push('路由配置文件缺失');
    return false;
  }

  const routesContent = fs.readFileSync(routesPath, 'utf8');
  
  // 检查关键路由
  const requiredRoutes = [
    '/group-chat',
    '/group/:groupId',
    '/agents',
    '/settings'
  ];

  const missingRoutes = requiredRoutes.filter(route => 
    !routesContent.includes(route)
  );

  if (missingRoutes.length > 0) {
    console.log('❌ 缺少路由配置:', missingRoutes.join(', '));
    diagnosticResults.navigationIssues.push(`缺少路由: ${missingRoutes.join(', ')}`);
    return false;
  }

  console.log('✅ 路由配置正常');
  return true;
}

// 4. 检查API端点
function checkApiEndpoints() {
  console.log('\n🌐 检查API端点...');
  const apiPath = path.join(__dirname, '../functions/api');
  
  if (!fs.existsSync(apiPath)) {
    console.log('❌ API目录不存在');
    diagnosticResults.apiIssues.push('API目录缺失');
    return false;
  }

  // 检查关键API文件
  const requiredApis = [
    'groups/create.ts',
    'groups/list.ts',
    'groups/[id].ts',
    'init.ts'
  ];

  const missingApis = requiredApis.filter(api => 
    !fs.existsSync(path.join(apiPath, api))
  );

  if (missingApis.length > 0) {
    console.log('❌ 缺少API文件:', missingApis.join(', '));
    diagnosticResults.apiIssues.push(`缺少API: ${missingApis.join(', ')}`);
    return false;
  }

  console.log('✅ API端点配置正常');
  return true;
}

// 5. 检查数据库配置
function checkDatabaseConfiguration() {
  console.log('\n🗄️  检查数据库配置...');
  const schemaPath = path.join(__dirname, '../database/schema.sql');
  
  if (!fs.existsSync(schemaPath)) {
    console.log('❌ 数据库schema文件不存在');
    diagnosticResults.databaseIssues.push('数据库schema缺失');
    return false;
  }

  const schemaContent = fs.readFileSync(schemaPath, 'utf8');
  
  // 检查关键表
  const requiredTables = [
    'groups_new',
    'group_members',
    'messages',
    'users'
  ];

  const missingTables = requiredTables.filter(table => 
    !schemaContent.includes(`CREATE TABLE ${table}`) && 
    !schemaContent.includes(`CREATE TABLE IF NOT EXISTS ${table}`)
  );

  if (missingTables.length > 0) {
    console.log('❌ 缺少数据库表:', missingTables.join(', '));
    diagnosticResults.databaseIssues.push(`缺少数据库表: ${missingTables.join(', ')}`);
    return false;
  }

  console.log('✅ 数据库配置正常');
  return true;
}

// 6. 检查React组件渲染循环问题
function checkReactRenderingIssues() {
  console.log('\n⚛️  检查React渲染问题...');
  
  // 检查常见的渲染循环问题
  const componentsToCheck = [
    '../src/pages/group-chat/index.tsx',
    '../src/layouts/BasicLayout.tsx',
    '../src/contexts/GroupChatContext.tsx'
  ];

  let hasIssues = false;

  componentsToCheck.forEach(componentPath => {
    const fullPath = path.join(__dirname, componentPath);
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');
      
      // 检查可能导致渲染循环的模式
      const problematicPatterns = [
        /useEffect\(\s*\(\)\s*=>\s*{[\s\S]*?}\s*,\s*\[[\s\S]*?\]\s*\)/g,
        /useState\(\s*\(\)\s*=>\s*{[\s\S]*?}\s*\)/g
      ];

      problematicPatterns.forEach(pattern => {
        const matches = content.match(pattern);
        if (matches && matches.length > 5) {
          console.log(`⚠️  ${componentPath} 可能存在渲染循环问题`);
          hasIssues = true;
        }
      });
    }
  });

  if (!hasIssues) {
    console.log('✅ 未发现明显的React渲染问题');
  }

  return !hasIssues;
}

// 7. 生成诊断报告
function generateDiagnosticReport() {
  console.log('\n📋 生成诊断报告...');
  
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalIssues: diagnosticResults.clickIssues.length + 
                  diagnosticResults.navigationIssues.length + 
                  diagnosticResults.apiIssues.length + 
                  diagnosticResults.databaseIssues.length,
      clickIssues: diagnosticResults.clickIssues.length,
      navigationIssues: diagnosticResults.navigationIssues.length,
      apiIssues: diagnosticResults.apiIssues.length,
      databaseIssues: diagnosticResults.databaseIssues.length
    },
    details: diagnosticResults,
    recommendations: [
      ...diagnosticResults.recommendations,
      '检查控制台错误日志',
      '验证API响应格式',
      '确认数据库连接状态',
      '检查React组件依赖项'
    ]
  };

  const reportPath = path.join(__dirname, '../diagnostic-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  console.log(`📄 诊断报告已保存到: ${reportPath}`);
  return report;
}

// 主函数
async function main() {
  console.log('🚀 开始前端问题诊断流程...\n');

  const checks = [
    { name: '构建状态', fn: checkBuildStatus },
    { name: '路由配置', fn: checkRouteConfiguration },
    { name: 'API端点', fn: checkApiEndpoints },
    { name: '数据库配置', fn: checkDatabaseConfiguration },
    { name: 'React渲染', fn: checkReactRenderingIssues },
    { name: '诊断测试', fn: runDiagnosticTests }
  ];

  let passedChecks = 0;
  
  for (const check of checks) {
    try {
      const result = check.fn();
      if (result) {
        passedChecks++;
      }
    } catch (error) {
      console.log(`❌ ${check.name}检查失败:`, error.message);
    }
  }

  console.log(`\n📊 诊断完成: ${passedChecks}/${checks.length} 项检查通过`);
  
  const report = generateDiagnosticReport();
  
  if (report.summary.totalIssues > 0) {
    console.log('\n🔧 发现问题，建议修复:');
    report.recommendations.forEach((rec, index) => {
      console.log(`   ${index + 1}. ${rec}`);
    });
  } else {
    console.log('\n✅ 未发现明显问题，建议进行手动测试验证');
  }
}

// 运行诊断
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 诊断过程中发生错误:', error);
    process.exit(1);
  });
}

module.exports = {
  checkBuildStatus,
  checkRouteConfiguration,
  checkApiEndpoints,
  checkDatabaseConfiguration,
  checkReactRenderingIssues,
  generateDiagnosticReport
};
