#!/usr/bin/env node

/**
 * 验证前端架构修复效果的脚本
 * 检查是否成功统一为单一前端架构
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 验证前端架构修复效果...\n');

const verificationResults = {
  passed: [],
  failed: [],
  warnings: []
};

// 1. 检查package.json脚本配置
function checkPackageScripts() {
  console.log('📦 检查package.json脚本配置...');
  
  const packagePath = path.join(__dirname, '../package.json');
  const packageContent = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  
  // 检查dev脚本是否指向vite
  if (packageContent.scripts.dev === 'vite') {
    verificationResults.passed.push('✅ dev脚本正确指向vite');
    console.log('✅ dev脚本配置正确');
  } else {
    verificationResults.failed.push('❌ dev脚本未指向vite');
    console.log('❌ dev脚本配置错误');
  }
  
  // 检查是否有dev:pages脚本用于Cloudflare Pages测试
  if (packageContent.scripts['dev:pages']) {
    verificationResults.passed.push('✅ dev:pages脚本存在，用于Cloudflare Pages测试');
    console.log('✅ dev:pages脚本配置正确');
  } else {
    verificationResults.warnings.push('⚠️ 缺少dev:pages脚本');
  }
}

// 2. 检查路由配置统一性
function checkRouteUnification() {
  console.log('\n🛣️  检查路由配置统一性...');
  
  const routesPath = path.join(__dirname, '../src/routes.tsx');
  
  if (!fs.existsSync(routesPath)) {
    verificationResults.failed.push('❌ 路由配置文件不存在');
    return;
  }
  
  const routesContent = fs.readFileSync(routesPath, 'utf8');
  
  // 检查是否使用createBrowserRouter
  if (routesContent.includes('createBrowserRouter')) {
    verificationResults.passed.push('✅ 使用React Router的createBrowserRouter');
    console.log('✅ 路由配置使用React Router');
  } else {
    verificationResults.failed.push('❌ 未使用React Router');
  }
  
  // 检查是否有BasicLayout
  if (routesContent.includes('BasicLayout')) {
    verificationResults.passed.push('✅ 使用统一的BasicLayout布局');
    console.log('✅ 布局系统统一');
  } else {
    verificationResults.failed.push('❌ 未使用统一布局');
  }
  
  // 检查关键路由是否存在
  const requiredRoutes = ['/login', '/', '/agents', '/group-chat', '/settings'];
  const missingRoutes = requiredRoutes.filter(route => 
    !routesContent.includes(`path: '${route.replace('/', '')}'`) && 
    !routesContent.includes(`path: '${route}'`)
  );
  
  if (missingRoutes.length === 0) {
    verificationResults.passed.push('✅ 所有关键路由都已配置');
    console.log('✅ 路由配置完整');
  } else {
    verificationResults.failed.push(`❌ 缺少路由: ${missingRoutes.join(', ')}`);
  }
}

// 3. 检查functions目录是否只包含API
function checkFunctionsDirectory() {
  console.log('\n🔧 检查functions目录...');
  
  const functionsPath = path.join(__dirname, '../functions');
  
  if (!fs.existsSync(functionsPath)) {
    verificationResults.warnings.push('⚠️ functions目录不存在');
    return;
  }
  
  // 递归检查functions目录中是否有前端文件
  function checkForFrontendFiles(dir) {
    const files = fs.readdirSync(dir);
    const frontendExtensions = ['.jsx', '.tsx', '.vue', '.html'];
    const frontendFiles = [];
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        frontendFiles.push(...checkForFrontendFiles(filePath));
      } else {
        const ext = path.extname(file);
        if (frontendExtensions.includes(ext)) {
          frontendFiles.push(filePath);
        }
      }
    });
    
    return frontendFiles;
  }
  
  const frontendFiles = checkForFrontendFiles(functionsPath);
  
  if (frontendFiles.length === 0) {
    verificationResults.passed.push('✅ functions目录只包含API文件');
    console.log('✅ functions目录清理完成');
  } else {
    verificationResults.failed.push(`❌ functions目录包含前端文件: ${frontendFiles.join(', ')}`);
  }
}

// 4. 检查构建产物
function checkBuildOutput() {
  console.log('\n🏗️  检查构建产物...');
  
  const distPath = path.join(__dirname, '../dist');
  
  if (!fs.existsSync(distPath)) {
    verificationResults.failed.push('❌ dist目录不存在');
    return;
  }
  
  const indexHtmlPath = path.join(distPath, 'index.html');
  
  if (fs.existsSync(indexHtmlPath)) {
    const indexContent = fs.readFileSync(indexHtmlPath, 'utf8');
    
    // 检查是否包含正确的标题
    if (indexContent.includes('AgentGroup - 智能体群聊平台')) {
      verificationResults.passed.push('✅ 构建产物包含正确的标题');
      console.log('✅ 构建产物正确');
    } else {
      verificationResults.failed.push('❌ 构建产物标题不正确');
    }
    
    // 检查是否包含React应用的根元素
    if (indexContent.includes('<div id="root">')) {
      verificationResults.passed.push('✅ 构建产物包含React根元素');
    } else {
      verificationResults.failed.push('❌ 构建产物缺少React根元素');
    }
  } else {
    verificationResults.failed.push('❌ 构建产物index.html不存在');
  }
}

// 5. 测试开发服务器启动
function testDevServer() {
  console.log('\n🚀 测试开发服务器启动...');
  
  try {
    // 尝试启动开发服务器（不等待，只检查是否能启动）
    const child = execSync('timeout 5s npm run dev || true', { 
      stdio: 'pipe',
      timeout: 6000
    });
    
    verificationResults.passed.push('✅ 开发服务器可以启动');
    console.log('✅ 开发服务器启动测试通过');
  } catch (error) {
    if (error.message.includes('timeout')) {
      verificationResults.passed.push('✅ 开发服务器启动正常（超时退出）');
      console.log('✅ 开发服务器启动正常');
    } else {
      verificationResults.failed.push('❌ 开发服务器启动失败');
      console.log('❌ 开发服务器启动失败:', error.message);
    }
  }
}

// 生成验证报告
function generateVerificationReport() {
  console.log('\n📊 验证报告');
  console.log('='.repeat(50));
  
  const totalChecks = verificationResults.passed.length + verificationResults.failed.length + verificationResults.warnings.length;
  const passedChecks = verificationResults.passed.length;
  const failedChecks = verificationResults.failed.length;
  const warningChecks = verificationResults.warnings.length;
  
  console.log(`总检查项: ${totalChecks}`);
  console.log(`通过: ${passedChecks}`);
  console.log(`失败: ${failedChecks}`);
  console.log(`警告: ${warningChecks}`);
  console.log(`成功率: ${Math.round((passedChecks / (passedChecks + failedChecks)) * 100)}%`);
  
  if (verificationResults.passed.length > 0) {
    console.log('\n✅ 通过的检查:');
    verificationResults.passed.forEach(item => console.log(`   ${item}`));
  }
  
  if (verificationResults.failed.length > 0) {
    console.log('\n❌ 失败的检查:');
    verificationResults.failed.forEach(item => console.log(`   ${item}`));
  }
  
  if (verificationResults.warnings.length > 0) {
    console.log('\n⚠️ 警告:');
    verificationResults.warnings.forEach(item => console.log(`   ${item}`));
  }
  
  // 保存报告
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      total: totalChecks,
      passed: passedChecks,
      failed: failedChecks,
      warnings: warningChecks,
      successRate: Math.round((passedChecks / (passedChecks + failedChecks)) * 100)
    },
    results: verificationResults
  };
  
  const reportPath = path.join(__dirname, '../frontend-fix-verification-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  console.log(`\n📄 详细报告已保存到: ${reportPath}`);
  
  return failedChecks === 0;
}

// 主函数
async function main() {
  console.log('🚀 开始验证前端架构修复效果...\n');
  
  const checks = [
    { name: 'package.json脚本', fn: checkPackageScripts },
    { name: '路由配置统一性', fn: checkRouteUnification },
    { name: 'functions目录清理', fn: checkFunctionsDirectory },
    { name: '构建产物检查', fn: checkBuildOutput },
    { name: '开发服务器测试', fn: testDevServer }
  ];
  
  for (const check of checks) {
    try {
      check.fn();
    } catch (error) {
      console.log(`❌ ${check.name}检查失败:`, error.message);
      verificationResults.failed.push(`${check.name}: ${error.message}`);
    }
  }
  
  const success = generateVerificationReport();
  
  if (success) {
    console.log('\n🎉 前端架构修复验证通过！');
    console.log('💡 现在可以使用 npm run dev 启动统一的Vite开发环境');
    console.log('💡 使用 npm run dev:pages 测试Cloudflare Pages环境');
  } else {
    console.log('\n⚠️ 验证发现问题，请查看上述报告进行修复');
    process.exit(1);
  }
}

// 运行验证
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 验证过程中发生错误:', error);
    process.exit(1);
  });
}

module.exports = {
  checkPackageScripts,
  checkRouteUnification,
  checkFunctionsDirectory,
  checkBuildOutput,
  generateVerificationReport
};
