#!/usr/bin/env node

/**
 * 前端测试运行脚本
 * 提供详细的测试报告和问题分析
 */

const { spawn } = require('child_process')
const fs = require('fs')
const path = require('path')

// 测试配置
const TEST_CONFIG = {
  // 测试文件模式
  patterns: {
    all: 'src/test/**/*.test.{ts,tsx}',
    pages: 'src/test/pages/**/*.test.{ts,tsx}',
    components: 'src/test/components/**/*.test.{ts,tsx}',
    layouts: 'src/test/layouts/**/*.test.{ts,tsx}',
    utils: 'src/test/utils/**/*.test.{ts,tsx}'
  },
  
  // 测试环境配置
  environment: {
    NODE_ENV: 'test',
    VITE_API_BASE_URL: 'http://localhost:3001',
    VITE_AUTH_ACCESS: '0'
  },
  
  // 覆盖率配置
  coverage: {
    threshold: {
      global: {
        branches: 80,
        functions: 80,
        lines: 80,
        statements: 80
      }
    },
    exclude: [
      'src/test/**',
      'src/**/*.test.{ts,tsx}',
      'src/**/*.d.ts',
      'dist/**',
      'node_modules/**'
    ]
  }
}

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`
}

// 日志函数
function log(message, color = 'reset') {
  console.log(colorize(message, color))
}

function logSection(title) {
  console.log('\n' + colorize('='.repeat(60), 'cyan'))
  console.log(colorize(`  ${title}`, 'cyan'))
  console.log(colorize('='.repeat(60), 'cyan') + '\n')
}

function logSubSection(title) {
  console.log('\n' + colorize(`📋 ${title}`, 'blue'))
  console.log(colorize('-'.repeat(40), 'blue'))
}

// 检查测试文件
function checkTestFiles() {
  logSubSection('检查测试文件')
  
  const testFiles = [
    'src/test/setup.ts',
    'src/test/utils.tsx',
    'src/test/pages/login.test.tsx',
    'src/test/layouts/BasicLayout.test.tsx',
    'src/test/pages/super-agent.test.tsx',
    'src/test/pages/group-chat.test.tsx',
    'src/test/pages/agents.test.tsx',
    'src/test/pages/settings.test.tsx'
  ]
  
  const missingFiles = []
  const existingFiles = []
  
  testFiles.forEach(file => {
    if (fs.existsSync(file)) {
      existingFiles.push(file)
      log(`✅ ${file}`, 'green')
    } else {
      missingFiles.push(file)
      log(`❌ ${file}`, 'red')
    }
  })
  
  log(`\n📊 测试文件统计:`, 'bright')
  log(`   存在: ${existingFiles.length}个`, 'green')
  log(`   缺失: ${missingFiles.length}个`, 'red')
  
  if (missingFiles.length > 0) {
    log('\n⚠️  请先创建缺失的测试文件', 'yellow')
    return false
  }
  
  return true
}

// 运行测试
function runTests(pattern = 'all', options = {}) {
  return new Promise((resolve, reject) => {
    const testPattern = TEST_CONFIG.patterns[pattern] || pattern
    
    const vitestArgs = [
      'vitest',
      'run',
      testPattern,
      '--reporter=verbose',
      '--reporter=json',
      '--outputFile=test-results.json'
    ]
    
    if (options.coverage) {
      vitestArgs.push('--coverage')
    }
    
    if (options.watch) {
      vitestArgs[1] = 'watch'
    }
    
    if (options.ui) {
      vitestArgs.push('--ui')
    }
    
    log(`🚀 运行测试: ${testPattern}`, 'blue')
    log(`📝 命令: npx ${vitestArgs.join(' ')}`, 'cyan')
    
    const testProcess = spawn('npx', vitestArgs, {
      stdio: 'pipe',
      env: { ...process.env, ...TEST_CONFIG.environment }
    })
    
    let output = ''
    let errorOutput = ''
    
    testProcess.stdout.on('data', (data) => {
      const text = data.toString()
      output += text
      if (!options.silent) {
        process.stdout.write(text)
      }
    })
    
    testProcess.stderr.on('data', (data) => {
      const text = data.toString()
      errorOutput += text
      if (!options.silent) {
        process.stderr.write(text)
      }
    })
    
    testProcess.on('close', (code) => {
      resolve({
        code,
        output,
        errorOutput,
        success: code === 0
      })
    })
    
    testProcess.on('error', (error) => {
      reject(error)
    })
  })
}

// 分析测试结果
function analyzeTestResults() {
  logSubSection('分析测试结果')
  
  try {
    if (!fs.existsSync('test-results.json')) {
      log('❌ 测试结果文件不存在', 'red')
      return null
    }
    
    const results = JSON.parse(fs.readFileSync('test-results.json', 'utf8'))
    
    log(`📊 测试统计:`, 'bright')
    log(`   总测试数: ${results.numTotalTests}`, 'cyan')
    log(`   通过: ${results.numPassedTests}`, 'green')
    log(`   失败: ${results.numFailedTests}`, 'red')
    log(`   跳过: ${results.numPendingTests}`, 'yellow')
    log(`   执行时间: ${results.testResults?.[0]?.perfStats?.runtime || 'N/A'}ms`, 'cyan')
    
    if (results.numFailedTests > 0) {
      logSubSection('失败的测试')
      results.testResults?.forEach(testFile => {
        if (testFile.status === 'failed') {
          log(`❌ ${testFile.name}`, 'red')
          testFile.assertionResults?.forEach(test => {
            if (test.status === 'failed') {
              log(`   - ${test.title}`, 'red')
              if (test.failureMessages?.length > 0) {
                test.failureMessages.forEach(msg => {
                  log(`     ${msg.split('\n')[0]}`, 'red')
                })
              }
            }
          })
        }
      })
    }
    
    return results
  } catch (error) {
    log(`❌ 解析测试结果失败: ${error.message}`, 'red')
    return null
  }
}

// 生成测试报告
function generateTestReport(results) {
  logSubSection('生成测试报告')
  
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      total: results.numTotalTests,
      passed: results.numPassedTests,
      failed: results.numFailedTests,
      skipped: results.numPendingTests,
      success_rate: ((results.numPassedTests / results.numTotalTests) * 100).toFixed(2)
    },
    test_files: results.testResults?.map(testFile => ({
      file: testFile.name,
      status: testFile.status,
      duration: testFile.perfStats?.runtime,
      tests: testFile.assertionResults?.map(test => ({
        name: test.title,
        status: test.status,
        duration: test.duration
      }))
    })),
    recommendations: generateRecommendations(results)
  }
  
  fs.writeFileSync('test-report.json', JSON.stringify(report, null, 2))
  log('✅ 测试报告已生成: test-report.json', 'green')
  
  return report
}

// 生成改进建议
function generateRecommendations(results) {
  const recommendations = []
  
  if (results.numFailedTests > 0) {
    recommendations.push({
      type: 'error',
      message: '存在失败的测试，需要修复',
      priority: 'high'
    })
  }
  
  const successRate = (results.numPassedTests / results.numTotalTests) * 100
  if (successRate < 90) {
    recommendations.push({
      type: 'warning',
      message: `测试通过率 ${successRate.toFixed(2)}% 低于建议的90%`,
      priority: 'medium'
    })
  }
  
  if (results.numTotalTests < 50) {
    recommendations.push({
      type: 'info',
      message: '建议增加更多测试用例以提高覆盖率',
      priority: 'low'
    })
  }
  
  return recommendations
}

// 主函数
async function main() {
  const args = process.argv.slice(2)
  const command = args[0] || 'all'
  
  logSection('AgentGroup 前端测试套件')
  
  // 解析命令行参数
  const options = {
    coverage: args.includes('--coverage'),
    watch: args.includes('--watch'),
    ui: args.includes('--ui'),
    silent: args.includes('--silent')
  }
  
  log(`🎯 测试模式: ${command}`, 'bright')
  log(`⚙️  选项: ${JSON.stringify(options)}`, 'cyan')
  
  // 检查测试文件
  if (!checkTestFiles()) {
    process.exit(1)
  }
  
  try {
    logSection('执行测试')
    
    // 运行测试
    const result = await runTests(command, options)
    
    if (!result.success) {
      log(`\n❌ 测试执行失败 (退出码: ${result.code})`, 'red')
    } else {
      log(`\n✅ 测试执行成功`, 'green')
    }
    
    // 分析结果
    logSection('测试结果分析')
    const testResults = analyzeTestResults()
    
    if (testResults) {
      // 生成报告
      const report = generateTestReport(testResults)
      
      // 显示建议
      if (report.recommendations.length > 0) {
        logSubSection('改进建议')
        report.recommendations.forEach(rec => {
          const icon = rec.type === 'error' ? '❌' : rec.type === 'warning' ? '⚠️' : 'ℹ️'
          const color = rec.type === 'error' ? 'red' : rec.type === 'warning' ? 'yellow' : 'blue'
          log(`${icon} ${rec.message}`, color)
        })
      }
    }
    
    logSection('测试完成')
    log(`📄 详细报告: test-report.json`, 'cyan')
    log(`📊 JSON结果: test-results.json`, 'cyan')
    
    if (options.coverage) {
      log(`📈 覆盖率报告: coverage/index.html`, 'cyan')
    }
    
    process.exit(result.success ? 0 : 1)
    
  } catch (error) {
    log(`\n💥 测试运行出错: ${error.message}`, 'red')
    console.error(error)
    process.exit(1)
  }
}

// 显示帮助信息
function showHelp() {
  console.log(`
${colorize('AgentGroup 前端测试运行器', 'bright')}

${colorize('用法:', 'blue')}
  node scripts/run-frontend-tests.js [模式] [选项]

${colorize('测试模式:', 'blue')}
  all         运行所有测试 (默认)
  pages       只运行页面测试
  components  只运行组件测试
  layouts     只运行布局测试
  utils       只运行工具函数测试

${colorize('选项:', 'blue')}
  --coverage  生成覆盖率报告
  --watch     监视模式
  --ui        启动测试UI界面
  --silent    静默模式
  --help      显示帮助信息

${colorize('示例:', 'blue')}
  node scripts/run-frontend-tests.js all --coverage
  node scripts/run-frontend-tests.js pages --watch
  node scripts/run-frontend-tests.js --ui
`)
}

// 检查是否需要显示帮助
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  showHelp()
  process.exit(0)
}

// 运行主函数
main().catch(console.error)
