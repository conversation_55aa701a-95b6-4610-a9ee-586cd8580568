#!/usr/bin/env node

/**
 * 测试前端修复效果的脚本
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 测试前端修复效果...\n');

// 测试结果
const testResults = {
  passed: [],
  failed: [],
  warnings: []
};

// 1. 检查React渲染循环修复
function testReactRenderingFixes() {
  console.log('⚛️  测试React渲染循环修复...');
  
  const chatUIPath = path.join(__dirname, '../src/pages/chat/components/ChatUI.tsx');
  const basicLayoutPath = path.join(__dirname, '../src/layouts/BasicLayout.tsx');
  
  // 检查ChatUI.tsx的useEffect修复
  if (fs.existsSync(chatUIPath)) {
    const content = fs.readFileSync(chatUIPath, 'utf8');
    
    // 检查是否移除了userStore.userInfo依赖
    if (content.includes('}, []); // 移除userStore.userInfo依赖，避免无限循环')) {
      testResults.passed.push('ChatUI.tsx: useEffect依赖项已修复');
    } else if (content.includes('}, [userStore.userInfo]);')) {
      testResults.failed.push('ChatUI.tsx: useEffect依赖项未修复，仍可能导致无限循环');
    } else {
      testResults.warnings.push('ChatUI.tsx: useEffect依赖项状态不明确');
    }
  }
  
  // 检查BasicLayout.tsx的useEffect修复
  if (fs.existsSync(basicLayoutPath)) {
    const content = fs.readFileSync(basicLayoutPath, 'utf8');
    
    if (content.includes('if (!userInfo) {') && content.includes('fetchUserInfo();')) {
      testResults.passed.push('BasicLayout.tsx: fetchUserInfo条件调用已修复');
    } else {
      testResults.failed.push('BasicLayout.tsx: fetchUserInfo可能仍会重复调用');
    }
  }
}

// 2. 检查导航跳转修复
function testNavigationFixes() {
  console.log('🛣️  测试导航跳转修复...');
  
  const groupChatPath = path.join(__dirname, '../src/pages/group-chat/index.tsx');
  
  if (fs.existsSync(groupChatPath)) {
    const content = fs.readFileSync(groupChatPath, 'utf8');
    
    // 检查群聊创建后的跳转逻辑
    if (content.includes('if (response.data?.id) {') && content.includes('console.log(\'群聊创建成功，跳转到:\'')) {
      testResults.passed.push('群聊页面: 导航跳转逻辑已增强');
    } else {
      testResults.failed.push('群聊页面: 导航跳转逻辑未修复');
    }
    
    // 检查点击事件调试信息
    if (content.includes('console.log(\'打开智能体选择页面 - 按钮被点击\')')) {
      testResults.passed.push('群聊页面: 点击事件调试信息已添加');
    } else {
      testResults.failed.push('群聊页面: 点击事件调试信息未添加');
    }
  }
}

// 3. 检查路由配置
function testRouteConfiguration() {
  console.log('🛣️  测试路由配置...');
  
  const routesPath = path.join(__dirname, '../src/routes.tsx');
  
  if (fs.existsSync(routesPath)) {
    const content = fs.readFileSync(routesPath, 'utf8');
    
    const requiredRoutes = [
      '/group-chat',
      '/group/:groupId',
      '/agents',
      '/settings'
    ];
    
    const missingRoutes = requiredRoutes.filter(route => !content.includes(route));
    
    if (missingRoutes.length === 0) {
      testResults.passed.push('路由配置: 所有必要路由都存在');
    } else {
      testResults.failed.push(`路由配置: 缺少路由 ${missingRoutes.join(', ')}`);
    }
  } else {
    testResults.failed.push('路由配置: routes.tsx文件不存在');
  }
}

// 4. 检查API文件
function testApiFiles() {
  console.log('🌐 测试API文件...');
  
  const apiFiles = [
    'functions/api/groups/create.ts',
    'functions/api/groups/list.ts',
    'functions/api/init.ts'
  ];
  
  apiFiles.forEach(apiFile => {
    const filePath = path.join(__dirname, '..', apiFile);
    
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      
      if (content.includes('JSON.stringify') && content.includes('success')) {
        testResults.passed.push(`API文件: ${apiFile} 格式正确`);
      } else {
        testResults.warnings.push(`API文件: ${apiFile} 响应格式可能有问题`);
      }
    } else {
      testResults.failed.push(`API文件: ${apiFile} 不存在`);
    }
  });
}

// 5. 生成测试报告
function generateTestReport() {
  console.log('\n📋 生成测试报告...');
  
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalTests: testResults.passed.length + testResults.failed.length + testResults.warnings.length,
      passed: testResults.passed.length,
      failed: testResults.failed.length,
      warnings: testResults.warnings.length
    },
    results: {
      passed: testResults.passed,
      failed: testResults.failed,
      warnings: testResults.warnings
    },
    recommendations: []
  };
  
  // 生成建议
  if (testResults.failed.length > 0) {
    report.recommendations.push('修复失败的测试项');
  }
  
  if (testResults.warnings.length > 0) {
    report.recommendations.push('检查警告项并进行优化');
  }
  
  if (testResults.passed.length === report.summary.totalTests) {
    report.recommendations.push('所有测试通过，可以进行手动测试验证');
  }
  
  const reportPath = path.join(__dirname, '../test-fixes-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  console.log(`📄 测试报告已保存到: ${reportPath}`);
  return report;
}

// 主函数
function main() {
  console.log('🚀 开始测试前端修复效果...\n');
  
  const tests = [
    { name: 'React渲染循环修复', fn: testReactRenderingFixes },
    { name: '导航跳转修复', fn: testNavigationFixes },
    { name: '路由配置', fn: testRouteConfiguration },
    { name: 'API文件', fn: testApiFiles }
  ];
  
  tests.forEach(test => {
    try {
      test.fn();
    } catch (error) {
      console.log(`❌ ${test.name}测试失败:`, error.message);
      testResults.failed.push(`${test.name}: ${error.message}`);
    }
  });
  
  const report = generateTestReport();
  
  console.log(`\n📊 测试完成:`);
  console.log(`   ✅ 通过: ${report.summary.passed}`);
  console.log(`   ❌ 失败: ${report.summary.failed}`);
  console.log(`   ⚠️  警告: ${report.summary.warnings}`);
  
  if (report.summary.failed > 0) {
    console.log('\n❌ 失败的测试:');
    report.results.failed.forEach((failure, index) => {
      console.log(`   ${index + 1}. ${failure}`);
    });
  }
  
  if (report.summary.warnings > 0) {
    console.log('\n⚠️  警告:');
    report.results.warnings.forEach((warning, index) => {
      console.log(`   ${index + 1}. ${warning}`);
    });
  }
  
  if (report.summary.passed > 0) {
    console.log('\n✅ 通过的测试:');
    report.results.passed.forEach((success, index) => {
      console.log(`   ${index + 1}. ${success}`);
    });
  }
  
  console.log('\n📝 建议:');
  report.recommendations.forEach((rec, index) => {
    console.log(`   ${index + 1}. ${rec}`);
  });
}

// 运行测试
if (require.main === module) {
  main();
}

module.exports = {
  testReactRenderingFixes,
  testNavigationFixes,
  testRouteConfiguration,
  testApiFiles,
  generateTestReport
};
