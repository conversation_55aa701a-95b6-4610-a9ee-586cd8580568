# AgentGroup 数据库架构文档

## 概述
AgentGroup 使用 Cloudflare D1 (SQLite) 数据库，支持智能体管理、群聊功能、用户管理、计费系统等核心功能。

## 数据库配置
- **数据库类型**: Cloudflare D1 (基于 SQLite)
- **数据库名称**: `bgdb`
- **字符编码**: UTF-8
- **时区**: UTC

## 核心表结构

### 1. 用户管理表 (users)
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    phone VARCHAR(11),                    -- 手机号
    nickname VARCHAR(50),                 -- 昵称
    avatar_url TEXT,                      -- 头像URL
    status INTEGER DEFAULT 1,             -- 状态: 1=启用, 0=禁用
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    username TEXT,                        -- 用户名
    password_hash TEXT,                   -- 密码哈希
    email TEXT,                          -- 邮箱
    role TEXT DEFAULT 'user',            -- 角色: admin/department_admin/user
    department_id TEXT,                  -- 部门ID
    balance REAL DEFAULT 0.0,            -- 字节币余额
    source TEXT DEFAULT 'local',         -- 用户来源: local/sso
    account_expires_at TIMESTAMP         -- 账户过期时间
);
```

**索引**:
- `username` (UNIQUE)
- `email` (UNIQUE)
- `phone` (UNIQUE)

### 2. 智能体管理表 (agents)
```sql
CREATE TABLE agents (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,   -- 智能体名称
    display_name VARCHAR(100) NOT NULL,  -- 显示名称
    description TEXT,                    -- 描述
    api_url TEXT NOT NULL,               -- API地址
    api_key TEXT,                        -- API密钥
    api_type VARCHAR(50) DEFAULT 'dify', -- API类型: dify/openai/claude等
    avatar_url TEXT,                     -- 头像URL
    status INTEGER DEFAULT 1,            -- 状态: 1=启用, 0=禁用, 2=维护中
    is_super_agent BOOLEAN DEFAULT FALSE, -- 是否为超级智能体
    created_by INTEGER,                  -- 创建者ID
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used_at TIMESTAMP,              -- 最后使用时间
    FOREIGN KEY (created_by) REFERENCES users(id)
);
```

**索引**:
- `name` (UNIQUE)
- `api_type`
- `status`
- `is_super_agent`

### 3. 群聊管理表 (groups_new)
```sql
CREATE TABLE groups_new (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(200) NOT NULL,          -- 群聊名称
    description TEXT,                    -- 群聊描述
    group_type VARCHAR(50) DEFAULT 'mixed', -- 群聊类型: mixed/ai_only/human_only
    max_members INTEGER DEFAULT 50,      -- 最大成员数
    created_by INTEGER,                  -- 创建者ID
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);
```

### 4. 群聊成员表 (group_members)
```sql
CREATE TABLE group_members (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    group_id INTEGER NOT NULL,           -- 群聊ID
    member_id INTEGER NOT NULL,          -- 成员ID
    member_type VARCHAR(20) NOT NULL,    -- 成员类型: user/agent
    role VARCHAR(20) DEFAULT 'member',   -- 角色: admin/member
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (group_id) REFERENCES groups_new(id) ON DELETE CASCADE
);
```

**索引**:
- `(group_id, member_id, member_type)` (UNIQUE)

### 5. 消息表 (messages)
```sql
CREATE TABLE messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    group_id INTEGER NOT NULL,           -- 群聊ID
    sender_id INTEGER NOT NULL,          -- 发送者ID
    sender_type VARCHAR(20) NOT NULL DEFAULT 'user', -- 发送者类型: user/agent/system
    content TEXT NOT NULL,               -- 消息内容
    message_type VARCHAR(20) DEFAULT 'text', -- 消息类型: text/image/file/system
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (group_id) REFERENCES groups_new(id) ON DELETE CASCADE
);
```

**索引**:
- `group_id`
- `created_at`
- `sender_type`

### 6. 通知表 (notifications)
```sql
CREATE TABLE notifications (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,            -- 接收用户ID
    type VARCHAR(50) NOT NULL,           -- 通知类型: mention/message/group_invite/system
    title VARCHAR(200) NOT NULL,         -- 通知标题
    content TEXT NOT NULL,               -- 通知内容
    sender_id INTEGER,                   -- 发送者ID
    group_id INTEGER,                    -- 相关群聊ID
    group_name VARCHAR(200),             -- 群聊名称
    action_url TEXT,                     -- 操作链接
    read_status INTEGER DEFAULT 0,       -- 阅读状态: 0=未读, 1=已读
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (sender_id) REFERENCES users(id),
    FOREIGN KEY (group_id) REFERENCES groups_new(id)
);
```

**索引**:
- `user_id`
- `read_status`
- `created_at`

## 计费系统表

### 7. 充值密钥表 (recharge_keys)
```sql
CREATE TABLE recharge_keys (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    key_code TEXT UNIQUE NOT NULL,       -- 充值密钥
    amount REAL NOT NULL,                -- 充值金额
    extend_days INTEGER DEFAULT 30,      -- 延长天数
    status INTEGER DEFAULT 0,            -- 状态: 0=未使用, 1=已使用, 2=已过期
    used_by INTEGER,                     -- 使用者ID
    used_at TIMESTAMP,                   -- 使用时间
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,                -- 过期时间
    FOREIGN KEY (used_by) REFERENCES users(id)
);
```

**索引**:
- `key_code` (UNIQUE)
- `status`

### 8. 交易记录表 (billing_transactions)
```sql
CREATE TABLE billing_transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,            -- 用户ID
    type TEXT NOT NULL,                  -- 交易类型: income/expense
    amount REAL NOT NULL,                -- 交易金额
    description TEXT,                    -- 交易描述
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

**索引**:
- `user_id`
- `type`
- `created_at`

## 扩展表结构

### 9. 部门表 (departments)
```sql
CREATE TABLE departments (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,                  -- 部门名称
    description TEXT,                    -- 部门描述
    parent_id TEXT,                      -- 父部门ID
    budget REAL DEFAULT 0.0,             -- 部门预算
    status TEXT DEFAULT 'active',        -- 状态: active/inactive
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    FOREIGN KEY (parent_id) REFERENCES departments(id)
);
```

### 10. 智能体能力表 (agent_capabilities)
```sql
CREATE TABLE agent_capabilities (
    id TEXT PRIMARY KEY,
    agent_id TEXT NOT NULL,              -- 智能体ID
    capability_name TEXT NOT NULL,       -- 能力名称
    confidence_score REAL DEFAULT 0.0,   -- 置信度分数
    is_primary BOOLEAN DEFAULT FALSE,    -- 是否为主要能力
    created_at TEXT NOT NULL,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE
);
```

### 11. 系统配置表 (system_config)
```sql
CREATE TABLE system_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    key TEXT UNIQUE NOT NULL,            -- 配置键
    value TEXT NOT NULL,                 -- 配置值
    description TEXT,                    -- 配置描述
    updated_at TEXT NOT NULL
);
```

## 数据关系图

```
users (用户)
├── groups_new (创建的群聊)
├── group_members (参与的群聊)
├── messages (发送的消息)
├── notifications (接收的通知)
├── billing_transactions (交易记录)
└── recharge_keys (使用的充值密钥)

agents (智能体)
├── group_members (参与的群聊)
├── messages (发送的消息)
└── agent_capabilities (智能体能力)

groups_new (群聊)
├── group_members (群聊成员)
├── messages (群聊消息)
└── notifications (群聊通知)
```

## 默认数据

### 默认管理员账户
- **用户名**: `admin`
- **密码**: `admin123`
- **角色**: `admin`
- **余额**: `10000.0`

### 默认智能体
1. **超级智能体** (`super-agent`)
   - 负责任务调度和意图理解
   - `is_super_agent = TRUE`

2. **Dify助手** (`dify-assistant`)
   - 通用AI助手

3. **代码助手** (`code-helper`)
   - 专业编程助手

### 默认充值密钥
- `RECHARGE100`: 100元，30天
- `RECHARGE500`: 500元，90天
- `RECHARGE1000`: 1000元，180天
- `TESTKEY2024`: 200元，60天

## 数据库初始化

数据库通过 `/api/init-db` 接口自动初始化，包括：
1. 创建所有表结构
2. 插入默认数据
3. 设置索引和外键约束

## 性能优化建议

1. **索引优化**
   - 为频繁查询的字段添加索引
   - 复合索引用于多字段查询

2. **数据清理**
   - 定期清理过期的通知
   - 归档历史消息数据

3. **查询优化**
   - 使用 LIMIT 限制查询结果
   - 避免 SELECT * 查询
   - 使用适当的 JOIN 类型

## 备份策略

1. **定期备份**: 每日自动备份
2. **增量备份**: 实时同步重要数据
3. **灾难恢复**: 多地域备份存储
