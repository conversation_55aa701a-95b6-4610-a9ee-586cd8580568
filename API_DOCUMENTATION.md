# AgentGroup API 接口文档

## 概述
AgentGroup 是一个智能体群聊平台，提供完整的 RESTful API 接口，支持用户认证、智能体管理、群聊功能、计费系统等核心功能。

## 基础信息
- **Base URL**: `http://localhost:3000/api` (开发环境)
- **认证方式**: <PERSON><PERSON> (JWT)
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式
```json
{
  "success": boolean,
  "message": string,
  "data": object | array
}
```

## 1. 认证接口 (Authentication)

### 1.1 用户登录
- **接口**: `POST /api/auth/login`
- **描述**: 支持用户名密码登录和短信验证码登录
- **请求体**:
```json
{
  "loginType": "password" | "sms",
  "username": "string",     // 用户名密码登录时必填
  "password": "string",     // 用户名密码登录时必填
  "phone": "string",        // 短信登录时必填
  "code": "string"          // 短信登录时必填
}
```
- **响应**:
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "token": "jwt_token",
    "user": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "nickname": "管理员",
      "role": "admin",
      "balance": 10000
    }
  }
}
```

### 1.2 获取当前用户信息
- **接口**: `GET /api/auth/me`
- **描述**: 获取当前登录用户的详细信息
- **认证**: 需要 Bearer Token
- **响应**: 返回用户详细信息

### 1.3 发送短信验证码
- **接口**: `POST /api/sendCode`
- **描述**: 发送短信验证码用于登录
- **请求体**:
```json
{
  "phone": "13800138000"
}
```

## 2. 用户管理接口 (User Management)

### 2.1 获取用户信息
- **接口**: `GET /api/user/info`
- **描述**: 获取当前用户的详细信息
- **认证**: 需要 Bearer Token

### 2.2 更新用户信息
- **接口**: `POST /api/user/update`
- **描述**: 更新用户昵称和头像
- **请求体**:
```json
{
  "nickname": "新昵称",
  "avatar_url": "头像URL"
}
```

### 2.3 头像上传
- **接口**: `POST /api/user/upload`
- **描述**: 获取头像上传的直接上传URL
- **认证**: 需要 Bearer Token

### 2.4 搜索用户
- **接口**: `GET /api/users/search?query=关键词`
- **描述**: 搜索用户（用于群聊邀请）
- **认证**: 需要 Bearer Token

## 3. 智能体管理接口 (Agent Management)

### 3.1 获取智能体列表
- **接口**: `GET /api/agents/list`
- **描述**: 获取所有可用的智能体列表
- **响应**:
```json
{
  "success": true,
  "data": {
    "agents": [
      {
        "id": "super-agent-001",
        "name": "超级智能体",
        "description": "负责任务分配和协调",
        "capabilities": ["任务分配", "意图理解"],
        "status": 1,
        "avg_rating": 4.8,
        "is_super_agent": true
      }
    ]
  }
}
```

### 3.2 智能体注册
- **接口**: `POST /api/agents/register`
- **描述**: 注册新的智能体
- **认证**: 需要管理员权限

### 3.3 智能体能力管理
- **接口**: `GET /api/agents/capabilities?agent_id=xxx`
- **描述**: 获取智能体的能力列表

### 3.4 智能体统计
- **接口**: `GET /api/agents/stats?agent_id=xxx&days=30`
- **描述**: 获取智能体的使用统计数据

### 3.5 智能体评分
- **接口**: `POST /api/agents/rate`
- **描述**: 对智能体进行评分

## 4. 群聊管理接口 (Group Chat)

### 4.1 获取群聊列表
- **接口**: `GET /api/groups/list`
- **描述**: 获取用户的群聊列表
- **认证**: 需要 Bearer Token

### 4.2 创建群聊
- **接口**: `POST /api/groups/create`
- **描述**: 创建新的群聊
- **请求体**:
```json
{
  "name": "群聊名称",
  "description": "群聊描述",
  "group_type": "mixed",
  "agent_ids": ["agent1", "agent2"],
  "user_ids": ["user1", "user2"]
}
```

### 4.3 获取群聊详情
- **接口**: `GET /api/groups/[id]`
- **描述**: 获取指定群聊的详细信息

## 5. 聊天接口 (Chat)

### 5.1 发送消息
- **接口**: `POST /api/chat`
- **描述**: 发送聊天消息
- **请求体**:
```json
{
  "message": "消息内容",
  "group_id": "群聊ID",
  "message_type": "text"
}
```

### 5.2 增强聊天
- **接口**: `POST /api/chat/enhanced`
- **描述**: 支持流式响应的增强聊天接口

## 6. 超级智能体接口 (Super Agent)

### 6.1 意图分析
- **接口**: `POST /api/super-agent/analyze`
- **描述**: 分析用户消息意图

### 6.2 任务调度
- **接口**: `POST /api/super-agent/schedule`
- **描述**: 智能调度合适的智能体

## 7. 计费系统接口 (Billing)

### 7.1 获取余额
- **接口**: `GET /api/billing/balance?include_transactions=true`
- **描述**: 获取用户账户余额和交易记录
- **认证**: 需要 Bearer Token

### 7.2 密钥充值
- **接口**: `POST /api/billing/recharge-by-key`
- **描述**: 使用充值密钥进行充值
- **请求体**:
```json
{
  "recharge_key": "充值密钥"
}
```

### 7.3 在线充值
- **接口**: `POST /api/billing/recharge`
- **描述**: 在线充值接口

### 7.4 交易记录
- **接口**: `GET /api/billing/records`
- **描述**: 获取交易记录

## 8. 管理员接口 (Admin)

### 8.1 用户管理
- **接口**: `GET /api/admin/users`
- **描述**: 获取所有用户列表（管理员专用）
- **认证**: 需要管理员权限

### 8.2 生成充值密钥
- **接口**: `POST /api/admin/recharge-keys/generate`
- **描述**: 生成充值密钥（管理员专用）

### 8.3 SSO配置
- **接口**: `GET/POST /api/admin/sso-config`
- **描述**: SSO单点登录配置管理

### 8.4 SSO同步
- **接口**: `POST /api/admin/sso-sync`
- **描述**: 同步SSO用户数据

## 9. 通知接口 (Notifications)

### 9.1 获取通知列表
- **接口**: `GET /api/notifications`
- **描述**: 获取用户的通知列表
- **认证**: 需要 Bearer Token

## 10. 任务管理接口 (Tasks)

### 10.1 获取任务列表
- **接口**: `GET /api/tasks/list`
- **描述**: 获取任务列表

### 10.2 更新任务状态
- **接口**: `POST /api/tasks/update`
- **描述**: 更新任务状态

## 11. 系统接口 (System)

### 11.1 初始化数据库
- **接口**: `GET /api/init-db`
- **描述**: 初始化数据库表结构

### 11.2 数据库测试
- **接口**: `GET /api/test-db`
- **描述**: 测试数据库连接

### 11.3 系统初始化
- **接口**: `GET /api/init`
- **描述**: 获取系统初始化数据

## 错误码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权/认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 认证说明

大部分接口需要在请求头中携带认证令牌：
```
Authorization: Bearer <jwt_token>
```

## 开发环境配置

默认管理员账户：
- 用户名: `admin`
- 密码: `admin123`
