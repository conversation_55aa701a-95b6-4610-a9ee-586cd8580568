import { useCallback } from 'react';
import { request } from '@/lib/request';

interface ActivityUpdate {
  user_id: string;
  activity_type: 'message' | 'chars' | 'interaction';
  value?: number;
}

export const useUserActivity = () => {
  // 更新用户活跃度
  const updateUserActivity = useCallback(async (
    groupId: number,
    updates: ActivityUpdate[]
  ) => {
    try {
      const promises = updates.map(update =>
        request(`/api/groups/${groupId}/weights`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(update),
        })
      );

      await Promise.all(promises);
    } catch (error) {
      console.error('更新用户活跃度失败:', error);
      // 静默失败，不影响用户体验
    }
  }, []);

  // 记录消息发送
  const recordMessage = useCallback(async (
    groupId: number,
    userId: string,
    messageContent: string
  ) => {
    const charCount = messageContent.length;
    
    await updateUserActivity(groupId, [
      { user_id: userId, activity_type: 'message', value: 1 },
      { user_id: userId, activity_type: 'chars', value: charCount }
    ]);
  }, [updateUserActivity]);

  // 记录互动行为（点赞、回复等）
  const recordInteraction = useCallback(async (
    groupId: number,
    userId: string,
    interactionScore: number = 1
  ) => {
    await updateUserActivity(groupId, [
      { user_id: userId, activity_type: 'interaction', value: interactionScore }
    ]);
  }, [updateUserActivity]);

  // 批量记录用户活跃度
  const recordBatchActivity = useCallback(async (
    groupId: number,
    activities: Array<{
      userId: string;
      messageCount?: number;
      charCount?: number;
      interactionScore?: number;
    }>
  ) => {
    const updates: ActivityUpdate[] = [];
    
    activities.forEach(activity => {
      if (activity.messageCount) {
        updates.push({
          user_id: activity.userId,
          activity_type: 'message',
          value: activity.messageCount
        });
      }
      
      if (activity.charCount) {
        updates.push({
          user_id: activity.userId,
          activity_type: 'chars',
          value: activity.charCount
        });
      }
      
      if (activity.interactionScore) {
        updates.push({
          user_id: activity.userId,
          activity_type: 'interaction',
          value: activity.interactionScore
        });
      }
    });

    if (updates.length > 0) {
      await updateUserActivity(groupId, updates);
    }
  }, [updateUserActivity]);

  return {
    updateUserActivity,
    recordMessage,
    recordInteraction,
    recordBatchActivity
  };
};
