/* 主题变量 - 统一的主题系统 */
:root {
  /* 浅色主题 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-quaternary: #e2e8f0;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  --border-primary: #e5e7eb;
  --border-secondary: #d1d5db;
  --accent-primary: #3b82f6;
  --accent-hover: #2563eb;
  --shadow-primary: rgba(0, 0, 0, 0.1);
  --shadow-secondary: rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] {
  /* 深色主题 */
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-quaternary: #475569;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;
  --border-primary: #334155;
  --border-secondary: #475569;
  --accent-primary: #3b82f6;
  --accent-hover: #2563eb;
  --shadow-primary: rgba(0, 0, 0, 0.3);
  --shadow-secondary: rgba(0, 0, 0, 0.2);
}

/* 全局样式 */
* {
  box-sizing: border-box;
}

html, body, #root {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 确保根元素也应用主题 */
#root {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  min-height: 100vh;
}

/* 主题相关的通用类 */
.theme-bg-primary {
  background-color: var(--bg-primary);
}

.theme-bg-secondary {
  background-color: var(--bg-secondary);
}

.theme-bg-tertiary {
  background-color: var(--bg-tertiary);
}

.theme-bg-quaternary {
  background-color: var(--bg-quaternary);
}

.theme-bg-accent {
  background-color: var(--accent-primary);
}

.theme-text-primary {
  color: var(--text-primary);
}

.theme-text-secondary {
  color: var(--text-secondary);
}

.theme-text-tertiary {
  color: var(--text-tertiary);
}

.theme-text-accent-foreground {
  color: #ffffff;
}

.theme-border-primary {
  border-color: var(--border-primary);
}

.theme-border-secondary {
  border-color: var(--border-secondary);
}

.theme-border-accent {
  border-color: var(--accent-primary);
}

.theme-shadow-primary {
  box-shadow: 0 4px 6px -1px var(--shadow-primary), 0 2px 4px -1px var(--shadow-secondary);
}

.theme-shadow-secondary {
  box-shadow: 0 1px 3px 0 var(--shadow-primary), 0 1px 2px 0 var(--shadow-secondary);
}

/* Hover效果 */
.hover-theme-bg-secondary:hover {
  background-color: var(--bg-secondary);
}

.hover-theme-bg-tertiary:hover {
  background-color: var(--bg-tertiary);
}

.hover-theme-bg-quaternary:hover {
  background-color: var(--bg-quaternary);
}

.hover-theme-text-primary:hover {
  color: var(--text-primary);
}

.hover-theme-border-secondary:hover {
  border-color: var(--border-secondary);
}

/* 过渡效果 */
.theme-transition {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* 组合类 */
.theme-card {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.theme-card:hover {
  background-color: var(--bg-tertiary);
  border-color: var(--border-secondary);
}

.theme-button {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-primary);
  transition: all 0.3s ease;
}

.theme-button:hover {
  background-color: var(--bg-quaternary);
  color: var(--text-primary);
}

/* 占位符文字样式 */
.placeholder-theme-text-tertiary::placeholder {
  color: var(--text-tertiary);
}

.placeholder-theme-text-secondary::placeholder {
  color: var(--text-secondary);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

