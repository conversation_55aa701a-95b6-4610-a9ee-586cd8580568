import { createBrowserRouter } from 'react-router-dom';
import Login from './pages/login';
import Chat from './pages/chat';
import Agents from './pages/agents';
import SuperAgent from './pages/super-agent';
import GroupChat from './pages/group-chat';
import GroupChatDetail from './pages/group-chat-detail';
import Settings from './pages/settings';
import Profile from './pages/profile';
import UserManagement from './pages/user-management';
import BasicLayout from './layouts/BasicLayout';
import AuthGuard from './components/AuthGuard';

export const router = createBrowserRouter([
  {
    path: '/login',
    element: <Login />,
  },
  {
    path: '/',
    element: (
      <AuthGuard>
        <BasicLayout />
      </AuthGuard>
    ),
    children: [
      {
        path: '',
        element: <SuperAgent />,
      },
      {
        path: 'chat',
        element: <Chat />,
      },
      {
        path: 'agents',
        element: <Agents />,
      },
      {
        path: 'group-chat',
        element: <GroupChat />,
      },
      {
        path: 'group/:groupId',
        element: <GroupChatDetail />,
      },
      {
        path: 'settings',
        element: <Settings />,
      },
      {
        path: 'profile',
        element: <Profile />,
      },
      {
        path: 'user-management',
        element: <UserManagement />,
      },
    ],
  },
]);