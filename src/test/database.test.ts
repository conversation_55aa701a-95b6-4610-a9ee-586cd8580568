import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mockFetchSuccess, mockFetchError } from './utils'

describe('数据库连接和初始化测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    global.fetch = vi.fn()
  })

  describe('数据库初始化', () => {
    it('应该成功初始化数据库', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchSuccess({ message: '数据库初始化成功' })
      )

      const response = await fetch('/api/init-db')
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data.message).toBe('数据库初始化成功')
    })

    it('应该处理数据库初始化失败', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(500, '数据库初始化失败')
      )

      const response = await fetch('/api/init-db')
      const data = await response.json()

      expect(response.ok).toBe(false)
      expect(data.message).toBe('数据库初始化失败')
    })
  })

  describe('数据库表结构验证', () => {
    it('应该验证users表结构', async () => {
      const expectedColumns = [
        'id', 'username', 'password_hash', 'nickname', 'email', 
        'phone', 'avatar_url', 'role', 'status', 'created_at', 'updated_at'
      ]

      global.fetch = vi.fn().mockResolvedValue(
        mockFetchSuccess({ 
          table: 'users',
          columns: expectedColumns
        })
      )

      const response = await fetch('/api/db/schema/users')
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data.columns).toEqual(expect.arrayContaining(expectedColumns))
    })

    it('应该验证groups表结构', async () => {
      const expectedColumns = [
        'id', 'name', 'description', 'type', 'created_by', 
        'created_at', 'updated_at'
      ]

      global.fetch = vi.fn().mockResolvedValue(
        mockFetchSuccess({ 
          table: 'groups',
          columns: expectedColumns
        })
      )

      const response = await fetch('/api/db/schema/groups')
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data.columns).toEqual(expect.arrayContaining(expectedColumns))
    })

    it('应该验证group_members表结构包含新增字段', async () => {
      const expectedColumns = [
        'group_id', 'member_id', 'member_type', 'role', 'joined_at',
        'invited_by', 'last_active', 'message_count', 'total_chars', 'interaction_score'
      ]

      global.fetch = vi.fn().mockResolvedValue(
        mockFetchSuccess({ 
          table: 'group_members',
          columns: expectedColumns
        })
      )

      const response = await fetch('/api/db/schema/group_members')
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data.columns).toEqual(expect.arrayContaining(expectedColumns))
    })

    it('应该验证group_invitations表结构', async () => {
      const expectedColumns = [
        'id', 'group_id', 'inviter_id', 'invitee_id', 'status',
        'message', 'created_at', 'responded_at'
      ]

      global.fetch = vi.fn().mockResolvedValue(
        mockFetchSuccess({ 
          table: 'group_invitations',
          columns: expectedColumns
        })
      )

      const response = await fetch('/api/db/schema/group_invitations')
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data.columns).toEqual(expect.arrayContaining(expectedColumns))
    })
  })

  describe('数据库连接测试', () => {
    it('应该能够连接到数据库', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchSuccess({ 
          connected: true,
          database: 'bgdb',
          version: '3.x'
        })
      )

      const response = await fetch('/api/db/health')
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data.connected).toBe(true)
    })

    it('应该处理数据库连接失败', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(503, '数据库连接失败')
      )

      const response = await fetch('/api/db/health')
      const data = await response.json()

      expect(response.ok).toBe(false)
      expect(data.message).toBe('数据库连接失败')
    })
  })

  describe('数据完整性测试', () => {
    it('应该验证外键约束', async () => {
      // 测试插入无效的group_id到group_members表
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(400, '外键约束失败')
      )

      const response = await fetch('/api/db/test/foreign-key', {
        method: 'POST',
        body: JSON.stringify({
          table: 'group_members',
          data: { group_id: 999999, member_id: '1', member_type: 'user' }
        })
      })
      const data = await response.json()

      expect(response.ok).toBe(false)
      expect(data.message).toContain('外键约束')
    })

    it('应该验证唯一约束', async () => {
      // 测试插入重复的用户名
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(400, '唯一约束失败')
      )

      const response = await fetch('/api/db/test/unique-constraint', {
        method: 'POST',
        body: JSON.stringify({
          table: 'users',
          data: { username: 'admin', password_hash: 'test' }
        })
      })
      const data = await response.json()

      expect(response.ok).toBe(false)
      expect(data.message).toContain('唯一约束')
    })
  })

  describe('数据库事务测试', () => {
    it('应该支持事务回滚', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchSuccess({ 
          transaction: true,
          rollback: true,
          message: '事务已回滚'
        })
      )

      const response = await fetch('/api/db/test/transaction', {
        method: 'POST',
        body: JSON.stringify({
          operations: [
            { type: 'insert', table: 'users', data: { username: 'test1' } },
            { type: 'insert', table: 'users', data: { username: 'test1' } } // 重复，应该失败
          ]
        })
      })
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data.rollback).toBe(true)
    })
  })
})
