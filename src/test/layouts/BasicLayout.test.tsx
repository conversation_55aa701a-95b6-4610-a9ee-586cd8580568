import { describe, it, expect, beforeEach, vi } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { render, mockFetch, testUtils, userEvent } from '../utils'
import BasicLayout from '../../layouts/BasicLayout'

describe('BasicLayout - 主布局组件', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    localStorage.clear()
    
    // Mock user data in localStorage
    localStorage.setItem('token', 'mock-token')
    localStorage.setItem('user', JSON.stringify({
      id: '1',
      username: 'admin',
      nickname: '管理员',
      role: 'admin',
      balance: 10000
    }))
  })

  describe('布局结构测试', () => {
    it('应该正确渲染主布局的所有组件', () => {
      render(<BasicLayout />)
      
      // 检查侧边栏
      expect(screen.getByRole('navigation')).toBeInTheDocument()
      
      // 检查主要导航项
      expect(screen.getByText(/超级智能体/i)).toBeInTheDocument()
      expect(screen.getByText(/群聊/i)).toBeInTheDocument()
      expect(screen.getByText(/智能体商店/i)).toBeInTheDocument()
      
      // 检查用户信息区域
      expect(screen.getByText(/管理员/i)).toBeInTheDocument()
      
      // 检查主内容区域
      expect(screen.getByRole('main')).toBeInTheDocument()
    })

    it('应该显示正确的用户余额', () => {
      render(<BasicLayout />)
      
      expect(screen.getByText(/10000/)).toBeInTheDocument()
      expect(screen.getByText(/积分/i)).toBeInTheDocument()
    })

    it('应该显示通知中心', () => {
      render(<BasicLayout />)
      
      const notificationButton = screen.getByRole('button', { name: /通知/i })
      expect(notificationButton).toBeInTheDocument()
    })
  })

  describe('导航功能测试', () => {
    it('应该正确导航到超级智能体页面', async () => {
      render(<BasicLayout />, { initialEntries: ['/'] })
      
      const superAgentLink = screen.getByText(/超级智能体/i)
      await userEvent.click(superAgentLink)
      
      // 检查URL是否正确
      expect(window.location.pathname).toBe('/')
    })

    it('应该正确导航到群聊页面', async () => {
      render(<BasicLayout />, { initialEntries: ['/group-chat'] })
      
      const groupChatLink = screen.getByText(/群聊/i)
      await userEvent.click(groupChatLink)
      
      expect(window.location.pathname).toBe('/group-chat')
    })

    it('应该正确导航到智能体商店', async () => {
      render(<BasicLayout />, { initialEntries: ['/agents'] })
      
      const agentsLink = screen.getByText(/智能体商店/i)
      await userEvent.click(agentsLink)
      
      expect(window.location.pathname).toBe('/agents')
    })

    it('应该高亮显示当前活跃的导航项', () => {
      render(<BasicLayout />, { initialEntries: ['/group-chat'] })
      
      const groupChatLink = screen.getByText(/群聊/i)
      expect(groupChatLink.closest('a')).toHaveClass('active')
    })
  })

  describe('侧边栏功能测试', () => {
    it('应该显示群聊列表', async () => {
      const mockGroups = {
        success: true,
        data: {
          my_groups: [
            { id: 1, name: '技术讨论群', member_count: 5, agent_count: 2 },
            { id: 2, name: '产品设计团队', member_count: 3, agent_count: 1 }
          ],
          public_groups: [
            { id: 3, name: '公开讨论群', member_count: 10, agent_count: 3 }
          ]
        }
      }
      
      mockFetch(mockGroups)
      
      render(<BasicLayout />)
      
      await waitFor(() => {
        expect(screen.getByText(/技术讨论群/i)).toBeInTheDocument()
        expect(screen.getByText(/产品设计团队/i)).toBeInTheDocument()
        expect(screen.getByText(/公开讨论群/i)).toBeInTheDocument()
      })
    })

    it('应该正确分类显示我的群聊和公共群聊', async () => {
      const mockGroups = {
        success: true,
        data: {
          my_groups: [
            { id: 1, name: '我的群聊1', member_count: 5, agent_count: 2 }
          ],
          public_groups: [
            { id: 2, name: '公共群聊1', member_count: 10, agent_count: 3 }
          ]
        }
      }
      
      mockFetch(mockGroups)
      
      render(<BasicLayout />)
      
      await waitFor(() => {
        expect(screen.getByText(/我的群聊/i)).toBeInTheDocument()
        expect(screen.getByText(/公共群聊/i)).toBeInTheDocument()
      })
    })

    it('应该显示群聊成员和智能体数量', async () => {
      const mockGroups = {
        success: true,
        data: {
          my_groups: [
            { id: 1, name: '技术讨论群', member_count: 5, agent_count: 2 }
          ],
          public_groups: []
        }
      }
      
      mockFetch(mockGroups)
      
      render(<BasicLayout />)
      
      await waitFor(() => {
        expect(screen.getByText(/5人/)).toBeInTheDocument()
        expect(screen.getByText(/2个智能体/)).toBeInTheDocument()
      })
    })
  })

  describe('用户菜单测试', () => {
    it('应该显示用户下拉菜单', async () => {
      render(<BasicLayout />)
      
      const userButton = screen.getByText(/管理员/i)
      await userEvent.click(userButton)
      
      await waitFor(() => {
        expect(screen.getByText(/个人资料/i)).toBeInTheDocument()
        expect(screen.getByText(/系统设置/i)).toBeInTheDocument()
        expect(screen.getByText(/退出登录/i)).toBeInTheDocument()
      })
    })

    it('应该正确导航到个人资料页面', async () => {
      render(<BasicLayout />)
      
      const userButton = screen.getByText(/管理员/i)
      await userEvent.click(userButton)
      
      const profileLink = screen.getByText(/个人资料/i)
      await userEvent.click(profileLink)
      
      expect(window.location.pathname).toBe('/profile')
    })

    it('应该正确导航到系统设置页面', async () => {
      render(<BasicLayout />)
      
      const userButton = screen.getByText(/管理员/i)
      await userEvent.click(userButton)
      
      const settingsLink = screen.getByText(/系统设置/i)
      await userEvent.click(settingsLink)
      
      expect(window.location.pathname).toBe('/settings')
    })

    it('应该正确处理退出登录', async () => {
      render(<BasicLayout />)
      
      const userButton = screen.getByText(/管理员/i)
      await userEvent.click(userButton)
      
      const logoutButton = screen.getByText(/退出登录/i)
      await userEvent.click(logoutButton)
      
      // 检查是否清除了localStorage
      expect(localStorage.removeItem).toHaveBeenCalledWith('token')
      expect(localStorage.removeItem).toHaveBeenCalledWith('user')
    })
  })

  describe('通知中心测试', () => {
    it('应该显示通知数量徽章', async () => {
      // Mock有未读通知的情况
      const mockNotifications = {
        success: true,
        data: {
          unread_count: 3,
          notifications: []
        }
      }
      
      mockFetch(mockNotifications)
      
      render(<BasicLayout />)
      
      await waitFor(() => {
        const badge = screen.getByText('3')
        expect(badge).toBeInTheDocument()
      })
    })

    it('应该打开通知面板', async () => {
      render(<BasicLayout />)
      
      const notificationButton = screen.getByRole('button', { name: /通知/i })
      await userEvent.click(notificationButton)
      
      await waitFor(() => {
        expect(screen.getByText(/通知中心/i)).toBeInTheDocument()
      })
    })
  })

  describe('主题切换测试', () => {
    it('应该显示主题切换按钮', () => {
      render(<BasicLayout />)
      
      const themeToggle = screen.getByRole('button', { name: /切换主题/i })
      expect(themeToggle).toBeInTheDocument()
    })

    it('应该正确切换主题', async () => {
      render(<BasicLayout />)
      
      const themeToggle = screen.getByRole('button', { name: /切换主题/i })
      await userEvent.click(themeToggle)
      
      // 检查主题是否切换
      await waitFor(() => {
        expect(document.documentElement).toHaveClass('dark')
      })
    })
  })

  describe('响应式设计测试', () => {
    it('应该在移动设备上隐藏侧边栏', () => {
      // 模拟移动设备
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 768,
      })
      
      render(<BasicLayout />)
      
      const sidebar = screen.getByRole('navigation')
      expect(sidebar).toHaveClass('hidden', 'md:block')
    })

    it('应该在移动设备上显示汉堡菜单', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 768,
      })
      
      render(<BasicLayout />)
      
      const menuButton = screen.getByRole('button', { name: /菜单/i })
      expect(menuButton).toBeInTheDocument()
    })
  })

  describe('错误处理测试', () => {
    it('应该处理群聊列表加载失败', async () => {
      mockFetch({ success: false, message: '加载失败' }, 500)
      
      render(<BasicLayout />)
      
      await waitFor(() => {
        expect(screen.getByText(/加载群聊列表失败/i)).toBeInTheDocument()
      })
    })

    it('应该处理用户信息缺失的情况', () => {
      localStorage.removeItem('user')
      
      render(<BasicLayout />)
      
      // 应该显示默认用户信息或重定向到登录页
      expect(screen.getByText(/未知用户/i) || screen.getByText(/登录/i)).toBeInTheDocument()
    })
  })

  describe('性能测试', () => {
    it('应该正确处理大量群聊数据', async () => {
      const largeGroupList = {
        success: true,
        data: {
          my_groups: Array.from({ length: 50 }, (_, i) => ({
            id: i + 1,
            name: `群聊${i + 1}`,
            member_count: Math.floor(Math.random() * 20) + 1,
            agent_count: Math.floor(Math.random() * 5) + 1
          })),
          public_groups: []
        }
      }
      
      mockFetch(largeGroupList)
      
      render(<BasicLayout />)
      
      await waitFor(() => {
        expect(screen.getByText(/群聊1/i)).toBeInTheDocument()
        expect(screen.getByText(/群聊50/i)).toBeInTheDocument()
      })
    })
  })
})
