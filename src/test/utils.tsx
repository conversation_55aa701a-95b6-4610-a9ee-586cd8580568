import React from 'react'
import { render, RenderOptions, screen, fireEvent, waitFor as waitForRTL } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { <PERSON><PERSON>erRouter, MemoryRouter } from 'react-router-dom'
import { vi } from 'vitest'
import { ThemeProvider } from '../contexts/ThemeContext'
import { GroupChatProvider } from '../contexts/GroupChatContext'

// Mock data
export const mockUser = {
  id: '1',
  username: 'testuser',
  nickname: 'Test User',
  email: '<EMAIL>',
  role: 'user' as const,
}

export const mockGroup = {
  id: 1,
  name: 'Test Group',
  description: 'Test group description',
  type: 'public' as const,
  created_by: '1',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
}

export const mockGroupMember = {
  member_id: '1',
  member_type: 'user' as const,
  role: 'member' as const,
  joined_at: '2024-01-01T00:00:00Z',
  invited_by: null,
  last_active: '2024-01-01T00:00:00Z',
  message_count: 10,
  total_chars: 500,
  interaction_score: 5.0,
}

export const mockAgent = {
  id: '1',
  name: 'Test Agent',
  description: 'Test agent description',
  avatar: null,
  model: 'gpt-4',
  system_prompt: 'You are a helpful assistant',
  temperature: 0.7,
  max_tokens: 2000,
  created_by: '1',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
}

export const mockInvitation = {
  id: '1',
  group_id: 1,
  inviter_id: '1',
  invitee_id: '2',
  status: 'pending' as const,
  created_at: '2024-01-01T00:00:00Z',
  responded_at: null,
}

// Enhanced providers wrapper
const AllTheProviders: React.FC<{
  children: React.ReactNode
  initialEntries?: string[]
  theme?: 'light' | 'dark'
}> = ({ children, initialEntries = ['/'], theme = 'light' }) => {
  // 在测试环境中设置初始主题
  React.useEffect(() => {
    if (typeof document !== 'undefined') {
      document.documentElement.className = theme;
      document.documentElement.setAttribute('data-theme', theme);
    }
  }, [theme]);

  return (
    <MemoryRouter initialEntries={initialEntries}>
      <ThemeProvider>
        <GroupChatProvider>
          {children}
        </GroupChatProvider>
      </ThemeProvider>
    </MemoryRouter>
  )
}

// Export the render function with providers
export const renderWithProviders = (
  ui: React.ReactElement,
  options: {
    initialEntries?: string[]
    theme?: 'light' | 'dark'
  } = {}
) => {
  return render(ui, {
    wrapper: (props) => <AllTheProviders {...props} {...options} />,
  })
}

// Enhanced custom render function
const customRender = (
  ui: React.ReactElement,
  options?: Omit<RenderOptions, 'wrapper'> & {
    initialEntries?: string[]
    theme?: 'light' | 'dark'
  }
) => {
  const { initialEntries, theme, ...renderOptions } = options || {}
  return render(ui, {
    wrapper: (props) => <AllTheProviders {...props} initialEntries={initialEntries} theme={theme} />,
    ...renderOptions
  })
}

export * from '@testing-library/react'
export { customRender as render, userEvent }

// Enhanced mock fetch functions
export const mockFetch = (response: any, status = 200) => {
  global.fetch = vi.fn().mockResolvedValue({
    ok: status >= 200 && status < 300,
    status,
    json: async () => response,
    text: async () => JSON.stringify(response),
    headers: new Headers(),
    redirected: false,
    statusText: 'OK',
    type: 'basic',
    url: '',
    clone: vi.fn(),
    body: null,
    bodyUsed: false,
    arrayBuffer: vi.fn(),
    blob: vi.fn(),
    formData: vi.fn(),
  })
}

export const mockFetchError = (error: string) => {
  global.fetch = vi.fn().mockRejectedValue(new Error(error))
}

export const mockFetchSuccess = (data: any) => {
  return mockFetch({ success: true, data })
}

export const mockFetchFailure = (status: number, message: string) => {
  return mockFetch({ success: false, message }, status)
}

// Test utilities
export const testUtils = {
  // Wait for loading to complete
  waitForLoadingToFinish: async () => {
    await waitForRTL(() => {
      expect(screen.queryByText(/loading/i)).not.toBeInTheDocument()
      expect(screen.queryByText(/加载中/)).not.toBeInTheDocument()
      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument()
    }, { timeout: 5000 })
  },

  // Type in input field
  typeInInput: async (input: HTMLElement, value: string) => {
    const user = userEvent.setup()
    await user.clear(input)
    await user.type(input, value)
  },

  // Click button and wait
  clickAndWait: async (button: HTMLElement, waitTime = 100) => {
    const user = userEvent.setup()
    await user.click(button)
    await new Promise(resolve => setTimeout(resolve, waitTime))
  },

  // Check if element is visible
  isVisible: (element: HTMLElement) => {
    return element.offsetParent !== null &&
           !element.hidden &&
           element.style.display !== 'none' &&
           element.style.visibility !== 'hidden'
  },

  // Get form data
  getFormData: (form: HTMLFormElement) => {
    const formData = new FormData(form)
    const data: Record<string, string> = {}
    formData.forEach((value, key) => {
      data[key] = value.toString()
    })
    return data
  },

  // Wait for element to appear
  waitForElement: async (selector: string, timeout = 3000) => {
    return await waitForRTL(() => {
      const element = screen.getByTestId(selector) || screen.getByText(selector)
      expect(element).toBeInTheDocument()
      return element
    }, { timeout })
  },

  // Simulate network delay
  delay: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),

  // Mock localStorage
  mockLocalStorage: () => {
    const store: Record<string, string> = {}
    return {
      getItem: vi.fn((key: string) => store[key] || null),
      setItem: vi.fn((key: string, value: string) => { store[key] = value }),
      removeItem: vi.fn((key: string) => { delete store[key] }),
      clear: vi.fn(() => { Object.keys(store).forEach(key => delete store[key]) }),
    }
  }
}

// Helper to wait for async operations
export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))
