import React from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { <PERSON><PERSON><PERSON><PERSON>out<PERSON> } from 'react-router-dom'

// Mock data
export const mockUser = {
  id: '1',
  username: 'testuser',
  nickname: 'Test User',
  email: '<EMAIL>',
  role: 'user' as const,
}

export const mockGroup = {
  id: 1,
  name: 'Test Group',
  description: 'Test group description',
  type: 'public' as const,
  created_by: '1',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
}

export const mockGroupMember = {
  member_id: '1',
  member_type: 'user' as const,
  role: 'member' as const,
  joined_at: '2024-01-01T00:00:00Z',
  invited_by: null,
  last_active: '2024-01-01T00:00:00Z',
  message_count: 10,
  total_chars: 500,
  interaction_score: 5.0,
}

export const mockAgent = {
  id: '1',
  name: 'Test Agent',
  description: 'Test agent description',
  avatar: null,
  model: 'gpt-4',
  system_prompt: 'You are a helpful assistant',
  temperature: 0.7,
  max_tokens: 2000,
  created_by: '1',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
}

export const mockInvitation = {
  id: '1',
  group_id: 1,
  inviter_id: '1',
  invitee_id: '2',
  status: 'pending' as const,
  created_at: '2024-01-01T00:00:00Z',
  responded_at: null,
}

// Custom render function with providers
const AllTheProviders: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <BrowserRouter>
      {children}
    </BrowserRouter>
  )
}

const customRender = (
  ui: React.ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options })

export * from '@testing-library/react'
export { customRender as render }

// Mock fetch responses
export const mockFetchSuccess = (data: any) => {
  return Promise.resolve({
    ok: true,
    status: 200,
    json: () => Promise.resolve({ success: true, data }),
  } as Response)
}

export const mockFetchError = (status: number, message: string) => {
  return Promise.resolve({
    ok: false,
    status,
    statusText: message,
    json: () => Promise.resolve({ success: false, message }),
  } as Response)
}

// Helper to wait for async operations
export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))
