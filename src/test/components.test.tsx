import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from './utils'
import { GroupMembersDialog } from '../pages/chat/components/GroupMembersDialog'
import { InviteUserDialog } from '../pages/chat/components/InviteUserDialog'
import { WeightAnalysisDialog } from '../pages/chat/components/WeightAnalysisDialog'

// Mock the request function
vi.mock('../lib/request', () => ({
  default: vi.fn(),
  request: vi.fn(),
}))

describe('UI组件测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    global.fetch = vi.fn()
  })

  describe('GroupMembersDialog', () => {
    const defaultProps = {
      open: true,
      onClose: vi.fn(),
      groupId: 1,
      groupName: '测试群聊',
      onMembersUpdated: vi.fn(),
    }

    it('应该渲染群聊成员对话框', async () => {
      const mockGroupInfo = {
        id: 1,
        name: '测试群聊',
        members: [
          {
            member_id: '1',
            member_type: 'user',
            role: 'owner',
            username: 'owner',
            nickname: '群主'
          }
        ],
        agents: []
      }

      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ success: true, data: mockGroupInfo })
      })

      render(<GroupMembersDialog {...defaultProps} />)

      await waitFor(() => {
        expect(screen.getByText('群聊成员管理')).toBeInTheDocument()
        expect(screen.getByText('测试群聊')).toBeInTheDocument()
      })
    })

    it('应该显示邀请成员按钮（管理员权限）', async () => {
      const mockGroupInfo = {
        id: 1,
        name: '测试群聊',
        members: [
          {
            member_id: '1',
            member_type: 'user',
            role: 'admin',
            username: 'admin',
            nickname: '管理员'
          }
        ],
        agents: []
      }

      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ success: true, data: mockGroupInfo })
      })

      render(<GroupMembersDialog {...defaultProps} />)

      await waitFor(() => {
        expect(screen.getByText('邀请成员')).toBeInTheDocument()
      })
    })

    it('应该显示权重分析按钮', async () => {
      const mockGroupInfo = {
        id: 1,
        name: '测试群聊',
        members: [],
        agents: []
      }

      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ success: true, data: mockGroupInfo })
      })

      render(<GroupMembersDialog {...defaultProps} />)

      await waitFor(() => {
        expect(screen.getByText('权重分析')).toBeInTheDocument()
      })
    })

    it('应该正确显示角色图标', async () => {
      const mockGroupInfo = {
        id: 1,
        name: '测试群聊',
        members: [
          {
            member_id: '1',
            member_type: 'user',
            role: 'owner',
            username: 'owner',
            nickname: '群主'
          },
          {
            member_id: '2',
            member_type: 'user',
            role: 'admin',
            username: 'admin',
            nickname: '管理员'
          },
          {
            member_id: '3',
            member_type: 'user',
            role: 'member',
            username: 'member',
            nickname: '成员'
          }
        ],
        agents: []
      }

      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ success: true, data: mockGroupInfo })
      })

      render(<GroupMembersDialog {...defaultProps} />)

      await waitFor(() => {
        // 应该有不同的角色图标
        expect(document.querySelector('.text-yellow-500')).toBeInTheDocument() // owner
        expect(document.querySelector('.text-blue-500')).toBeInTheDocument()   // admin
        expect(document.querySelector('.text-gray-500')).toBeInTheDocument()   // member
      })
    })
  })

  describe('InviteUserDialog', () => {
    const defaultProps = {
      open: true,
      onClose: vi.fn(),
      groupId: 1,
      groupName: '测试群聊',
      onInviteSuccess: vi.fn(),
    }

    it('应该渲染邀请用户对话框', () => {
      render(<InviteUserDialog {...defaultProps} />)

      expect(screen.getByText('邀请用户')).toBeInTheDocument()
      expect(screen.getByText('测试群聊')).toBeInTheDocument()
      expect(screen.getByPlaceholderText('搜索用户名或昵称')).toBeInTheDocument()
    })

    it('应该能够搜索用户', async () => {
      const mockUsers = [
        { id: '1', username: 'user1', nickname: 'User 1' },
        { id: '2', username: 'user2', nickname: 'User 2' },
      ]

      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ success: true, data: mockUsers })
      })

      render(<InviteUserDialog {...defaultProps} />)

      const searchInput = screen.getByPlaceholderText('搜索用户名或昵称')
      fireEvent.change(searchInput, { target: { value: 'user' } })

      await waitFor(() => {
        expect(screen.getByText('User 1')).toBeInTheDocument()
        expect(screen.getByText('User 2')).toBeInTheDocument()
      })
    })

    it('应该能够选择和取消选择用户', async () => {
      const mockUsers = [
        { id: '1', username: 'user1', nickname: 'User 1' },
      ]

      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ success: true, data: mockUsers })
      })

      render(<InviteUserDialog {...defaultProps} />)

      const searchInput = screen.getByPlaceholderText('搜索用户名或昵称')
      fireEvent.change(searchInput, { target: { value: 'user' } })

      await waitFor(() => {
        const userItem = screen.getByText('User 1').closest('div')
        fireEvent.click(userItem!)
      })

      // 应该显示已选择的用户
      expect(screen.getByText('已选择 1 个用户')).toBeInTheDocument()
    })
  })

  describe('WeightAnalysisDialog', () => {
    const defaultProps = {
      open: true,
      onClose: vi.fn(),
      groupId: 1,
      groupName: '测试群聊',
    }

    it('应该渲染权重分析对话框', () => {
      render(<WeightAnalysisDialog {...defaultProps} />)

      expect(screen.getByText('用户参与度权重分析')).toBeInTheDocument()
      expect(screen.getByText('测试群聊')).toBeInTheDocument()
    })

    it('应该显示加载状态', () => {
      render(<WeightAnalysisDialog {...defaultProps} />)

      expect(screen.getByText('分析中...')).toBeInTheDocument()
    })

    it('应该显示权重分析结果', async () => {
      const mockWeightData = {
        group_id: 1,
        total_users: 2,
        analysis_time: '2024-01-01T00:00:00Z',
        user_weights: [
          {
            user_id: '1',
            username: 'user1',
            message_count: 50,
            total_chars: 2500,
            interaction_score: 10.0,
            last_active: '2024-01-01T00:00:00Z',
            weight_score: 45.5,
            priority_level: 'high'
          }
        ],
        recommendations: [
          '高活跃用户 (1人): user1 - 建议优先响应其需求'
        ]
      }

      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ success: true, data: mockWeightData })
      })

      render(<WeightAnalysisDialog {...defaultProps} />)

      await waitFor(() => {
        expect(screen.getByText('user1')).toBeInTheDocument()
        expect(screen.getByText('高活跃')).toBeInTheDocument()
        expect(screen.getByText('45.5')).toBeInTheDocument()
        expect(screen.getByText('智能建议')).toBeInTheDocument()
      })
    })

    it('应该显示统计信息', async () => {
      const mockWeightData = {
        group_id: 1,
        total_users: 3,
        analysis_time: '2024-01-01T00:00:00Z',
        user_weights: [
          {
            user_id: '1',
            username: 'user1',
            message_count: 50,
            total_chars: 2500,
            interaction_score: 10.0,
            last_active: '2024-01-01T00:00:00Z',
            weight_score: 45.5,
            priority_level: 'high'
          }
        ],
        recommendations: []
      }

      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ success: true, data: mockWeightData })
      })

      render(<WeightAnalysisDialog {...defaultProps} />)

      await waitFor(() => {
        expect(screen.getByText('3')).toBeInTheDocument() // 总用户数
        expect(screen.getByText('1')).toBeInTheDocument() // 高活跃用户数
        expect(screen.getByText('50')).toBeInTheDocument() // 总消息数
      })
    })
  })
})
