import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mockFetchSuccess, mockFetchError } from './utils'

describe('集成测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    global.fetch = vi.fn()
  })

  describe('完整的邀请流程', () => {
    it('应该完成从搜索用户到发送邀请的完整流程', async () => {
      // 1. 搜索用户
      const mockUsers = [
        { id: '2', username: 'user2', nickname: 'User 2' },
        { id: '3', username: 'user3', nickname: 'User 3' },
      ]

      global.fetch = vi.fn()
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true, data: mockUsers })
        })
        // 2. 发送邀请
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ 
            success: true, 
            data: { message: '邀请已发送' } 
          })
        })
        // 3. 获取邀请列表
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ 
            success: true, 
            data: [
              {
                id: '1',
                group_id: 1,
                group_name: '测试群聊',
                inviter_name: '邀请者',
                status: 'pending'
              }
            ]
          })
        })

      // 搜索用户
      const searchResponse = await fetch('/api/users/search?q=user')
      const searchData = await searchResponse.json()
      expect(searchData.data).toHaveLength(2)

      // 发送邀请
      const inviteResponse = await fetch('/api/groups/invite', {
        method: 'POST',
        body: JSON.stringify({
          group_id: 1,
          user_ids: ['2', '3']
        })
      })
      const inviteData = await inviteResponse.json()
      expect(inviteData.success).toBe(true)

      // 获取邀请列表
      const invitationsResponse = await fetch('/api/invitations')
      const invitationsData = await invitationsResponse.json()
      expect(invitationsData.data).toHaveLength(1)
    })

    it('应该完成邀请响应流程', async () => {
      global.fetch = vi.fn()
        // 1. 接受邀请
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ 
            success: true, 
            data: { message: '已成功加入群聊' } 
          })
        })
        // 2. 更新群聊列表
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ 
            success: true, 
            data: {
              my_groups: [],
              public_groups: [
                {
                  id: 1,
                  name: '测试群聊',
                  type: 'public',
                  member_count: 2
                }
              ]
            }
          })
        })

      // 接受邀请
      const respondResponse = await fetch('/api/invitations/1/respond', {
        method: 'POST',
        body: JSON.stringify({ action: 'accept' })
      })
      const respondData = await respondResponse.json()
      expect(respondData.success).toBe(true)

      // 验证群聊列表更新
      const groupsResponse = await fetch('/api/groups/list')
      const groupsData = await groupsResponse.json()
      expect(groupsData.data.public_groups).toHaveLength(1)
      expect(groupsData.data.public_groups[0].member_count).toBe(2)
    })
  })

  describe('权重分析与活跃度更新流程', () => {
    it('应该完成从发送消息到权重更新的完整流程', async () => {
      global.fetch = vi.fn()
        // 1. 更新消息活跃度
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ 
            success: true, 
            data: { message: '活跃度数据更新成功' } 
          })
        })
        // 2. 更新字符数活跃度
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ 
            success: true, 
            data: { message: '活跃度数据更新成功' } 
          })
        })
        // 3. 获取更新后的权重分析
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ 
            success: true, 
            data: {
              group_id: 1,
              total_users: 1,
              user_weights: [
                {
                  user_id: '1',
                  username: 'user1',
                  message_count: 51, // 增加了1
                  total_chars: 2600, // 增加了100
                  interaction_score: 10.0,
                  weight_score: 46.0, // 权重增加
                  priority_level: 'high'
                }
              ],
              recommendations: []
            }
          })
        })

      // 更新消息数量
      const messageResponse = await fetch('/api/groups/1/weights', {
        method: 'POST',
        body: JSON.stringify({
          user_id: '1',
          activity_type: 'message',
          value: 1
        })
      })
      expect((await messageResponse.json()).success).toBe(true)

      // 更新字符数
      const charsResponse = await fetch('/api/groups/1/weights', {
        method: 'POST',
        body: JSON.stringify({
          user_id: '1',
          activity_type: 'chars',
          value: 100
        })
      })
      expect((await charsResponse.json()).success).toBe(true)

      // 获取更新后的权重分析
      const weightsResponse = await fetch('/api/groups/1/weights')
      const weightsData = await weightsResponse.json()
      expect(weightsData.data.user_weights[0].message_count).toBe(51)
      expect(weightsData.data.user_weights[0].total_chars).toBe(2600)
      expect(weightsData.data.user_weights[0].weight_score).toBe(46.0)
    })
  })

  describe('群聊管理完整流程', () => {
    it('应该完成创建群聊、邀请成员、管理权限的完整流程', async () => {
      global.fetch = vi.fn()
        // 1. 创建群聊
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ 
            success: true, 
            data: { 
              id: 1, 
              name: '新群聊',
              type: 'private'
            } 
          })
        })
        // 2. 邀请成员
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ 
            success: true, 
            data: { message: '邀请已发送' } 
          })
        })
        // 3. 获取群聊成员
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ 
            success: true, 
            data: {
              id: 1,
              name: '新群聊',
              members: [
                {
                  member_id: '1',
                  member_type: 'user',
                  role: 'owner',
                  username: 'creator'
                },
                {
                  member_id: '2',
                  member_type: 'user',
                  role: 'member',
                  username: 'member1'
                }
              ],
              agents: []
            }
          })
        })
        // 4. 更新成员角色
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ 
            success: true, 
            data: { message: '角色更新成功' } 
          })
        })

      // 创建群聊
      const createResponse = await fetch('/api/groups', {
        method: 'POST',
        body: JSON.stringify({
          name: '新群聊',
          type: 'private'
        })
      })
      const createData = await createResponse.json()
      expect(createData.success).toBe(true)
      expect(createData.data.name).toBe('新群聊')

      // 邀请成员
      const inviteResponse = await fetch('/api/groups/invite', {
        method: 'POST',
        body: JSON.stringify({
          group_id: 1,
          user_ids: ['2']
        })
      })
      expect((await inviteResponse.json()).success).toBe(true)

      // 获取群聊成员
      const membersResponse = await fetch('/api/groups/1/members')
      const membersData = await membersResponse.json()
      expect(membersData.data.members).toHaveLength(2)
      expect(membersData.data.members[0].role).toBe('owner')

      // 更新成员角色
      const roleResponse = await fetch('/api/groups/1/members/2/role', {
        method: 'PUT',
        body: JSON.stringify({ role: 'admin' })
      })
      expect((await roleResponse.json()).success).toBe(true)
    })
  })

  describe('错误处理和边界情况', () => {
    it('应该正确处理网络错误', async () => {
      global.fetch = vi.fn().mockRejectedValue(new Error('Network error'))

      try {
        await fetch('/api/groups/list')
        expect.fail('应该抛出错误')
      } catch (error) {
        expect(error).toBeInstanceOf(Error)
        expect((error as Error).message).toBe('Network error')
      }
    })

    it('应该正确处理API错误响应', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(500, 'Internal Server Error')
      )

      const response = await fetch('/api/groups/list')
      expect(response.ok).toBe(false)
      expect(response.status).toBe(500)
    })

    it('应该处理空数据情况', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchSuccess({
          my_groups: [],
          public_groups: []
        })
      )

      const response = await fetch('/api/groups/list')
      const data = await response.json()
      expect(data.data.my_groups).toHaveLength(0)
      expect(data.data.public_groups).toHaveLength(0)
    })

    it('应该处理权限不足的情况', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(403, '权限不足')
      )

      const response = await fetch('/api/groups/1/invite', {
        method: 'POST',
        body: JSON.stringify({ user_ids: ['2'] })
      })
      const data = await response.json()
      expect(response.ok).toBe(false)
      expect(data.message).toBe('权限不足')
    })
  })
})
