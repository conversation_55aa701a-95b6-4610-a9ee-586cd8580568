import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mockFetchSuccess, mockFetchError } from './utils'

describe('认证和授权测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    global.fetch = vi.fn()
    // 清除localStorage
    localStorage.clear()
  })

  describe('用户登录', () => {
    it('应该成功登录管理员账户', async () => {
      const mockResponse = {
        user: {
          id: '1',
          username: 'admin',
          nickname: '管理员',
          role: 'admin'
        },
        token: 'mock-jwt-token'
      }

      global.fetch = vi.fn().mockResolvedValue(
        mockFetchSuccess(mockResponse)
      )

      const response = await fetch('/api/auth/login', {
        method: 'POST',
        body: JSON.stringify({
          username: 'admin',
          password: 'admin123'
        })
      })
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data.user.username).toBe('admin')
      expect(data.data.user.role).toBe('admin')
      expect(data.data.token).toBeDefined()
    })

    it('应该拒绝错误的登录凭据', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(401, '用户名或密码错误')
      )

      const response = await fetch('/api/auth/login', {
        method: 'POST',
        body: JSON.stringify({
          username: 'admin',
          password: 'wrongpassword'
        })
      })
      const data = await response.json()

      expect(response.ok).toBe(false)
      expect(data.message).toBe('用户名或密码错误')
    })

    it('应该拒绝空的登录凭据', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(400, '用户名和密码不能为空')
      )

      const response = await fetch('/api/auth/login', {
        method: 'POST',
        body: JSON.stringify({
          username: '',
          password: ''
        })
      })
      const data = await response.json()

      expect(response.ok).toBe(false)
      expect(data.message).toBe('用户名和密码不能为空')
    })

    it('应该处理被禁用的用户账户', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(403, '账户已被禁用')
      )

      const response = await fetch('/api/auth/login', {
        method: 'POST',
        body: JSON.stringify({
          username: 'disabled_user',
          password: 'password123'
        })
      })
      const data = await response.json()

      expect(response.ok).toBe(false)
      expect(data.message).toBe('账户已被禁用')
    })
  })

  describe('会话管理', () => {
    it('应该验证有效的JWT令牌', async () => {
      const mockUser = {
        id: '1',
        username: 'admin',
        role: 'admin'
      }

      global.fetch = vi.fn().mockResolvedValue(
        mockFetchSuccess(mockUser)
      )

      const response = await fetch('/api/auth/verify', {
        method: 'GET',
        headers: {
          'Authorization': 'Bearer mock-jwt-token'
        }
      })
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data.username).toBe('admin')
    })

    it('应该拒绝无效的JWT令牌', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(401, '无效的令牌')
      )

      const response = await fetch('/api/auth/verify', {
        method: 'GET',
        headers: {
          'Authorization': 'Bearer invalid-token'
        }
      })
      const data = await response.json()

      expect(response.ok).toBe(false)
      expect(data.message).toBe('无效的令牌')
    })

    it('应该拒绝过期的JWT令牌', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(401, '令牌已过期')
      )

      const response = await fetch('/api/auth/verify', {
        method: 'GET',
        headers: {
          'Authorization': 'Bearer expired-token'
        }
      })
      const data = await response.json()

      expect(response.ok).toBe(false)
      expect(data.message).toBe('令牌已过期')
    })

    it('应该成功注销用户', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchSuccess({ message: '注销成功' })
      )

      const response = await fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer mock-jwt-token'
        }
      })
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data.message).toBe('注销成功')
    })
  })

  describe('权限控制', () => {
    it('应该允许管理员访问管理功能', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchSuccess({ message: '访问成功' })
      )

      const response = await fetch('/api/admin/users', {
        method: 'GET',
        headers: {
          'Authorization': 'Bearer admin-token'
        }
      })
      const data = await response.json()

      expect(data.success).toBe(true)
    })

    it('应该拒绝普通用户访问管理功能', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(403, '权限不足')
      )

      const response = await fetch('/api/admin/users', {
        method: 'GET',
        headers: {
          'Authorization': 'Bearer user-token'
        }
      })
      const data = await response.json()

      expect(response.ok).toBe(false)
      expect(data.message).toBe('权限不足')
    })

    it('应该验证群聊管理权限', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchSuccess({ message: '有管理权限' })
      )

      const response = await fetch('/api/groups/1/permissions', {
        method: 'GET',
        headers: {
          'Authorization': 'Bearer owner-token'
        }
      })
      const data = await response.json()

      expect(data.success).toBe(true)
    })

    it('应该拒绝无权限用户管理群聊', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(403, '您不是群主或管理员')
      )

      const response = await fetch('/api/groups/1/members/2', {
        method: 'DELETE',
        headers: {
          'Authorization': 'Bearer member-token'
        }
      })
      const data = await response.json()

      expect(response.ok).toBe(false)
      expect(data.message).toBe('您不是群主或管理员')
    })
  })

  describe('密码安全', () => {
    it('应该要求强密码', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(400, '密码强度不足')
      )

      const response = await fetch('/api/auth/change-password', {
        method: 'POST',
        body: JSON.stringify({
          oldPassword: 'admin123',
          newPassword: '123' // 弱密码
        })
      })
      const data = await response.json()

      expect(response.ok).toBe(false)
      expect(data.message).toBe('密码强度不足')
    })

    it('应该验证旧密码', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(400, '旧密码错误')
      )

      const response = await fetch('/api/auth/change-password', {
        method: 'POST',
        body: JSON.stringify({
          oldPassword: 'wrongpassword',
          newPassword: 'newStrongPassword123!'
        })
      })
      const data = await response.json()

      expect(response.ok).toBe(false)
      expect(data.message).toBe('旧密码错误')
    })

    it('应该成功修改密码', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchSuccess({ message: '密码修改成功' })
      )

      const response = await fetch('/api/auth/change-password', {
        method: 'POST',
        body: JSON.stringify({
          oldPassword: 'admin123',
          newPassword: 'newStrongPassword123!'
        })
      })
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data.message).toBe('密码修改成功')
    })
  })

  describe('会话超时', () => {
    it('应该处理会话超时', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(401, '会话已超时，请重新登录')
      )

      const response = await fetch('/api/groups/list', {
        method: 'GET',
        headers: {
          'Authorization': 'Bearer expired-session-token'
        }
      })
      const data = await response.json()

      expect(response.ok).toBe(false)
      expect(data.message).toBe('会话已超时，请重新登录')
    })

    it('应该刷新即将过期的令牌', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchSuccess({ 
          token: 'new-refreshed-token',
          expiresIn: 3600
        })
      )

      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer soon-to-expire-token'
        }
      })
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data.token).toBe('new-refreshed-token')
    })
  })
})
