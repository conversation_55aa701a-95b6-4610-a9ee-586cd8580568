import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mockFetchSuccess, mockFetchError } from './utils'

// Mock the API functions
const mockGroupsAPI = {
  getGroupsList: vi.fn(),
  createGroup: vi.fn(),
  joinGroup: vi.fn(),
  leaveGroup: vi.fn(),
}

describe('群聊分类管理', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    global.fetch = vi.fn()
  })

  describe('群聊列表分类', () => {
    it('应该正确分类我的群聊和公共群聊', async () => {
      const mockResponse = {
        my_groups: [
          { id: 1, name: '我的群聊1', type: 'private', created_by: 'user1' },
          { id: 2, name: '我的群聊2', type: 'private', created_by: 'user1' },
        ],
        public_groups: [
          { id: 3, name: '公共群聊1', type: 'public', created_by: 'user2' },
          { id: 4, name: '公共群聊2', type: 'public', created_by: 'user3' },
        ]
      }

      global.fetch = vi.fn().mockResolvedValue(mockFetchSuccess(mockResponse))

      const response = await fetch('/api/groups/list')
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data.my_groups).toHaveLength(2)
      expect(data.data.public_groups).toHaveLength(2)
      expect(data.data.my_groups[0].created_by).toBe('user1')
      expect(data.data.public_groups[0].type).toBe('public')
    })

    it('应该限制我的群聊最多5个', async () => {
      const myGroups = Array.from({ length: 6 }, (_, i) => ({
        id: i + 1,
        name: `我的群聊${i + 1}`,
        type: 'private',
        created_by: 'user1'
      }))

      const mockResponse = {
        my_groups: myGroups.slice(0, 5), // 应该只返回前5个
        public_groups: []
      }

      global.fetch = vi.fn().mockResolvedValue(mockFetchSuccess(mockResponse))

      const response = await fetch('/api/groups/list')
      const data = await response.json()

      expect(data.data.my_groups).toHaveLength(5)
    })

    it('应该限制公共群聊最多10个', async () => {
      const publicGroups = Array.from({ length: 12 }, (_, i) => ({
        id: i + 1,
        name: `公共群聊${i + 1}`,
        type: 'public',
        created_by: 'user2'
      }))

      const mockResponse = {
        my_groups: [],
        public_groups: publicGroups.slice(0, 10) // 应该只返回前10个
      }

      global.fetch = vi.fn().mockResolvedValue(mockFetchSuccess(mockResponse))

      const response = await fetch('/api/groups/list')
      const data = await response.json()

      expect(data.data.public_groups).toHaveLength(10)
    })
  })

  describe('群聊创建限制', () => {
    it('应该阻止创建超过5个我的群聊', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(400, '您最多只能创建5个群聊')
      )

      const response = await fetch('/api/groups', {
        method: 'POST',
        body: JSON.stringify({ name: '新群聊', type: 'private' })
      })
      const data = await response.json()

      expect(response.ok).toBe(false)
      expect(data.message).toContain('最多只能创建5个群聊')
    })

    it('应该允许加入公共群聊（不超过10个）', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchSuccess({ message: '成功加入群聊' })
      )

      const response = await fetch('/api/groups/1/join', {
        method: 'POST'
      })
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data.message).toBe('成功加入群聊')
    })
  })

  describe('群聊数据结构', () => {
    it('应该包含正确的群聊字段', async () => {
      const mockGroup = {
        id: 1,
        name: '测试群聊',
        description: '测试描述',
        type: 'public',
        created_by: 'user1',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        member_count: 5,
        agent_count: 2
      }

      global.fetch = vi.fn().mockResolvedValue(mockFetchSuccess(mockGroup))

      const response = await fetch('/api/groups/1')
      const data = await response.json()

      expect(data.data).toHaveProperty('id')
      expect(data.data).toHaveProperty('name')
      expect(data.data).toHaveProperty('type')
      expect(data.data).toHaveProperty('member_count')
      expect(data.data).toHaveProperty('agent_count')
    })
  })
})
