import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { vi, describe, it, expect, beforeEach } from 'vitest';

// 模拟组件
const MockGroupChatPage = () => {
  const [showModal, setShowModal] = React.useState(false);
  const [groups, setGroups] = React.useState([
    { id: '1', name: '测试群聊1', type: 'my' },
    { id: '2', name: '测试群聊2', type: 'public' }
  ]);

  const handleCreateGroup = () => {
    console.log('创建群聊按钮被点击');
    setShowModal(true);
  };

  const handleGroupClick = (groupId: string) => {
    console.log('群聊被点击:', groupId);
    // 模拟跳转
    window.location.hash = `#/chat/${groupId}`;
  };

  return (
    <div data-testid="group-chat-page">
      <h1>群聊管理</h1>
      
      {/* 创建群聊按钮 */}
      <button 
        data-testid="create-group-btn"
        onClick={handleCreateGroup}
        className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
      >
        新建群聊
      </button>

      {/* 我的群聊 */}
      <div data-testid="my-groups-section">
        <h2>我的群聊 (最多5个)</h2>
        <div data-testid="my-groups-list">
          {groups.filter(g => g.type === 'my').map(group => (
            <div 
              key={group.id}
              data-testid={`my-group-${group.id}`}
              onClick={() => handleGroupClick(group.id)}
              className="cursor-pointer p-2 border rounded hover:bg-gray-100"
            >
              {group.name}
            </div>
          ))}
        </div>
      </div>

      {/* 公共群聊 */}
      <div data-testid="public-groups-section">
        <h2>公共群聊 (最多10个)</h2>
        <div data-testid="public-groups-list">
          {groups.filter(g => g.type === 'public').map(group => (
            <div 
              key={group.id}
              data-testid={`public-group-${group.id}`}
              onClick={() => handleGroupClick(group.id)}
              className="cursor-pointer p-2 border rounded hover:bg-gray-100"
            >
              {group.name}
            </div>
          ))}
        </div>
      </div>

      {/* 创建群聊模态框 */}
      {showModal && (
        <div data-testid="create-group-modal" className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <div className="bg-white p-6 rounded">
            <h3>创建新群聊</h3>
            <button 
              data-testid="close-modal-btn"
              onClick={() => setShowModal(false)}
              className="mt-4 px-4 py-2 bg-gray-500 text-white rounded"
            >
              关闭
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

const MockSuperAgentPage = () => {
  const [selectedAgent, setSelectedAgent] = React.useState<string | null>(null);
  
  const agents = [
    { id: '1', name: '超级智能体', description: '任务协调与智能分析' },
    { id: '2', name: '开发助手', description: '专业代码开发与技术支持' },
    { id: '3', name: 'UI设计师', description: '用户界面与体验设计' }
  ];

  const handleAgentClick = (agentId: string) => {
    console.log('智能体卡片被点击:', agentId);
    setSelectedAgent(agentId);
    // 模拟跳转到聊天页面
    window.location.hash = `#/chat/agent/${agentId}`;
  };

  return (
    <div data-testid="super-agent-page">
      <h1>超级智能体</h1>
      <div data-testid="agents-grid" className="grid grid-cols-3 gap-4">
        {agents.map(agent => (
          <div 
            key={agent.id}
            data-testid={`agent-card-${agent.id}`}
            onClick={() => handleAgentClick(agent.id)}
            className={`cursor-pointer p-4 border rounded hover:shadow-lg transition-shadow ${
              selectedAgent === agent.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
            }`}
          >
            <h3 className="font-bold">{agent.name}</h3>
            <p className="text-gray-600">{agent.description}</p>
          </div>
        ))}
      </div>
      
      {selectedAgent && (
        <div data-testid="selected-agent-info" className="mt-4 p-4 bg-green-100 rounded">
          已选择智能体: {agents.find(a => a.id === selectedAgent)?.name}
        </div>
      )}
    </div>
  );
};

// 简化的测试包装器，避免导入问题
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <BrowserRouter>
    {children}
  </BrowserRouter>
);

describe('Click Response Tests - 点击响应测试', () => {
  beforeEach(() => {
    // 清理之前的测试状态
    window.location.hash = '';
    vi.clearAllMocks();
  });

  describe('群聊页面点击测试', () => {
    it('应该能成功点击新建群聊按钮', async () => {
      const user = userEvent.setup();
      render(
        <TestWrapper>
          <MockGroupChatPage />
        </TestWrapper>
      );

      // 验证页面渲染
      expect(screen.getByTestId('group-chat-page')).toBeInTheDocument();
      
      // 查找并点击新建群聊按钮
      const createBtn = screen.getByTestId('create-group-btn');
      expect(createBtn).toBeInTheDocument();
      expect(createBtn).toBeVisible();

      // 点击按钮
      await user.click(createBtn);

      // 验证模态框出现
      await waitFor(() => {
        expect(screen.getByTestId('create-group-modal')).toBeInTheDocument();
      });
    });

    it('应该能成功点击群聊卡片', async () => {
      const user = userEvent.setup();
      render(
        <TestWrapper>
          <MockGroupChatPage />
        </TestWrapper>
      );

      // 点击我的群聊
      const myGroup = screen.getByTestId('my-group-1');
      expect(myGroup).toBeInTheDocument();
      expect(myGroup).toBeVisible();

      await user.click(myGroup);

      // 验证跳转
      await waitFor(() => {
        expect(window.location.hash).toBe('#/chat/1');
      });
    });

    it('应该能成功点击公共群聊卡片', async () => {
      const user = userEvent.setup();
      render(
        <TestWrapper>
          <MockGroupChatPage />
        </TestWrapper>
      );

      // 点击公共群聊
      const publicGroup = screen.getByTestId('public-group-2');
      expect(publicGroup).toBeInTheDocument();
      expect(publicGroup).toBeVisible();

      await user.click(publicGroup);

      // 验证跳转
      await waitFor(() => {
        expect(window.location.hash).toBe('#/chat/2');
      });
    });

    it('应该正确区分我的群聊和公共群聊', () => {
      render(
        <TestWrapper>
          <MockGroupChatPage />
        </TestWrapper>
      );

      // 验证我的群聊区域
      const myGroupsSection = screen.getByTestId('my-groups-section');
      expect(myGroupsSection).toBeInTheDocument();
      expect(myGroupsSection).toHaveTextContent('我的群聊 (最多5个)');

      // 验证公共群聊区域
      const publicGroupsSection = screen.getByTestId('public-groups-section');
      expect(publicGroupsSection).toBeInTheDocument();
      expect(publicGroupsSection).toHaveTextContent('公共群聊 (最多10个)');
    });
  });

  describe('超级智能体页面点击测试', () => {
    it('应该能成功点击智能体卡片', async () => {
      const user = userEvent.setup();
      render(
        <TestWrapper>
          <MockSuperAgentPage />
        </TestWrapper>
      );

      // 验证页面渲染
      expect(screen.getByTestId('super-agent-page')).toBeInTheDocument();
      
      // 点击第一个智能体卡片
      const agentCard = screen.getByTestId('agent-card-1');
      expect(agentCard).toBeInTheDocument();
      expect(agentCard).toBeVisible();

      await user.click(agentCard);

      // 验证选择状态
      await waitFor(() => {
        expect(screen.getByTestId('selected-agent-info')).toBeInTheDocument();
        expect(screen.getByTestId('selected-agent-info')).toHaveTextContent('已选择智能体: 超级智能体');
      });

      // 验证跳转
      expect(window.location.hash).toBe('#/chat/agent/1');
    });

    it('应该能切换选择不同的智能体', async () => {
      const user = userEvent.setup();
      render(
        <TestWrapper>
          <MockSuperAgentPage />
        </TestWrapper>
      );

      // 先点击第一个智能体
      await user.click(screen.getByTestId('agent-card-1'));
      
      await waitFor(() => {
        expect(screen.getByTestId('selected-agent-info')).toHaveTextContent('已选择智能体: 超级智能体');
      });

      // 再点击第二个智能体
      await user.click(screen.getByTestId('agent-card-2'));
      
      await waitFor(() => {
        expect(screen.getByTestId('selected-agent-info')).toHaveTextContent('已选择智能体: 开发助手');
      });

      // 验证最新的跳转
      expect(window.location.hash).toBe('#/chat/agent/2');
    });

    it('应该正确显示智能体卡片的悬停效果', async () => {
      const user = userEvent.setup();
      render(
        <TestWrapper>
          <MockSuperAgentPage />
        </TestWrapper>
      );

      const agentCard = screen.getByTestId('agent-card-1');
      
      // 悬停
      await user.hover(agentCard);
      
      // 验证卡片有正确的样式类
      expect(agentCard).toHaveClass('hover:shadow-lg');
      expect(agentCard).toHaveClass('cursor-pointer');
    });
  });

  describe('通用点击响应测试', () => {
    it('应该正确处理快速连续点击', async () => {
      const user = userEvent.setup();
      render(
        <TestWrapper>
          <MockGroupChatPage />
        </TestWrapper>
      );

      const createBtn = screen.getByTestId('create-group-btn');
      
      // 快速连续点击
      await user.click(createBtn);
      await user.click(createBtn);
      await user.click(createBtn);

      // 应该只有一个模态框
      await waitFor(() => {
        const modals = screen.getAllByTestId('create-group-modal');
        expect(modals).toHaveLength(1);
      });
    });

    it('应该正确处理键盘导航', async () => {
      const user = userEvent.setup();
      render(
        <TestWrapper>
          <MockSuperAgentPage />
        </TestWrapper>
      );

      const agentCard = screen.getByTestId('agent-card-1');
      
      // 使用Tab键导航到卡片
      agentCard.focus();
      
      // 使用Enter键激活
      await user.keyboard('{Enter}');

      await waitFor(() => {
        expect(screen.getByTestId('selected-agent-info')).toBeInTheDocument();
      });
    });
  });
});
