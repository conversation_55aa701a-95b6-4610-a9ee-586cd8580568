import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { renderWithProviders } from '../utils'
import { performance } from 'perf_hooks'

// Import pages for performance testing
import AgentsPage from '@/pages/agents'
import ChatPage from '@/pages/chat'
import GroupChatPage from '@/pages/group-chat'
import SuperAgentPage from '@/pages/super-agent'

describe('Performance Tests - 性能测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Mock performance APIs
    global.performance = {
      ...performance,
      mark: vi.fn(),
      measure: vi.fn(),
      getEntriesByType: vi.fn(() => []),
      getEntriesByName: vi.fn(() => []),
      now: vi.fn(() => Date.now())
    } as any
    
    // Mock localStorage
    const localStorageMock = {
      getItem: vi.fn((key) => {
        if (key === 'token') return 'mock-jwt-token'
        return null
      }),
      setItem: vi.fn(),
      removeItem: vi.fn(),
      clear: vi.fn(),
    }
    vi.stubGlobal('localStorage', localStorageMock)
    
    // Mock fetch with realistic delays
    global.fetch = vi.fn().mockImplementation(() => 
      new Promise(resolve => 
        setTimeout(() => resolve({
          ok: true,
          json: async () => ({ success: true, data: [] })
        }), 100) // 100ms delay to simulate network
      )
    )
  })

  describe('页面加载性能测试', () => {
    it('登录页面应该在500ms内完成初始渲染', async () => {
      const startTime = performance.now()
      
      const { container } = renderWithProviders(<div>Login Page Mock</div>)
      
      // 等待DOM渲染完成
      await waitFor(() => {
        expect(container.firstChild).toBeInTheDocument()
      })
      
      const endTime = performance.now()
      const renderTime = endTime - startTime
      
      expect(renderTime).toBeLessThan(500) // 500ms内完成渲染
    })

    it('智能体商店页面应该在1秒内完成加载', async () => {
      const startTime = performance.now()
      
      renderWithProviders(<AgentsPage />)
      
      // 等待页面主要内容加载
      await waitFor(() => {
        const mainContent = screen.getByRole('main') || screen.getByTestId('agents-page')
        expect(mainContent).toBeInTheDocument()
      }, { timeout: 1000 })
      
      const endTime = performance.now()
      const loadTime = endTime - startTime
      
      expect(loadTime).toBeLessThan(1000) // 1秒内完成加载
    })

    it('聊天页面应该快速响应用户输入', async () => {
      renderWithProviders(<ChatPage />)
      
      const startTime = performance.now()
      
      // 模拟用户输入
      const messageInput = screen.getByRole('textbox') || screen.getByPlaceholderText(/输入消息/i)
      
      if (messageInput) {
        // 测试输入响应时间
        messageInput.focus()
        
        const endTime = performance.now()
        const responseTime = endTime - startTime
        
        expect(responseTime).toBeLessThan(100) // 100ms内响应
      }
    })
  })

  describe('内存使用测试', () => {
    it('应该正确清理组件内存', async () => {
      const { unmount } = renderWithProviders(<AgentsPage />)
      
      // 检查组件是否正确挂载
      await waitFor(() => {
        expect(screen.getByRole('main') || document.body).toBeInTheDocument()
      })
      
      // 卸载组件
      unmount()
      
      // 验证组件已被清理
      expect(screen.queryByRole('main')).not.toBeInTheDocument()
    })

    it('应该避免内存泄漏', async () => {
      // 模拟多次渲染和卸载
      for (let i = 0; i < 5; i++) {
        const { unmount } = renderWithProviders(<ChatPage />)
        
        await waitFor(() => {
          expect(document.body).toBeInTheDocument()
        })
        
        unmount()
      }
      
      // 检查是否有残留的事件监听器或定时器
      // 这里可以添加更具体的内存泄漏检测逻辑
      expect(true).toBe(true) // 占位符，实际项目中需要更具体的检测
    })
  })

  describe('网络请求性能测试', () => {
    it('应该正确处理并发API请求', async () => {
      // Mock多个API调用
      const apiCalls = [
        fetch('/api/agents'),
        fetch('/api/groups'),
        fetch('/api/user/profile')
      ]
      
      const startTime = performance.now()
      
      // 并发执行API调用
      const results = await Promise.all(apiCalls)
      
      const endTime = performance.now()
      const totalTime = endTime - startTime
      
      // 验证所有请求都成功
      results.forEach(result => {
        expect(result.ok).toBe(true)
      })
      
      // 并发请求应该比串行请求快
      expect(totalTime).toBeLessThan(500) // 500ms内完成所有请求
    })

    it('应该正确处理API请求超时', async () => {
      // Mock慢速API
      global.fetch = vi.fn().mockImplementation(() => 
        new Promise(resolve => 
          setTimeout(() => resolve({
            ok: true,
            json: async () => ({ success: true, data: [] })
          }), 2000) // 2秒延迟
        )
      )
      
      renderWithProviders(<AgentsPage />)
      
      // 检查是否显示加载状态
      await waitFor(() => {
        const loadingIndicator = screen.getByText(/加载中/i) || 
                               screen.getByRole('progressbar') ||
                               screen.getByTestId('loading')
        expect(loadingIndicator).toBeInTheDocument()
      }, { timeout: 1000 })
    })
  })

  describe('大数据量处理性能测试', () => {
    it('应该高效渲染大量智能体列表', async () => {
      // Mock大量数据
      const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
        id: i,
        name: `智能体 ${i}`,
        description: `这是第 ${i} 个智能体的描述`
      }))
      
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: async () => ({ success: true, data: largeDataset })
      })
      
      const startTime = performance.now()
      
      renderWithProviders(<AgentsPage />)
      
      // 等待数据加载和渲染
      await waitFor(() => {
        const agentItems = screen.getAllByText(/智能体/i)
        expect(agentItems.length).toBeGreaterThan(0)
      }, { timeout: 3000 })
      
      const endTime = performance.now()
      const renderTime = endTime - startTime
      
      // 大数据量渲染应该在3秒内完成
      expect(renderTime).toBeLessThan(3000)
    })

    it('应该支持虚拟滚动优化', async () => {
      // 测试虚拟滚动性能
      const largeDataset = Array.from({ length: 10000 }, (_, i) => ({
        id: i,
        name: `项目 ${i}`
      }))
      
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: async () => ({ success: true, data: largeDataset })
      })
      
      renderWithProviders(<AgentsPage />)
      
      // 检查是否只渲染可见项目
      await waitFor(() => {
        const visibleItems = screen.getAllByText(/项目/i)
        // 虚拟滚动应该只渲染少量可见项目
        expect(visibleItems.length).toBeLessThan(100)
      })
    })
  })

  describe('用户交互响应性能测试', () => {
    it('搜索功能应该快速响应', async () => {
      renderWithProviders(<AgentsPage />)
      
      const searchInput = screen.getByRole('searchbox') || screen.getByPlaceholderText(/搜索/i)
      
      if (searchInput) {
        const startTime = performance.now()
        
        // 模拟用户输入
        searchInput.focus()
        
        const endTime = performance.now()
        const responseTime = endTime - startTime
        
        expect(responseTime).toBeLessThan(50) // 50ms内响应
      }
    })

    it('按钮点击应该立即响应', async () => {
      renderWithProviders(<GroupChatPage />)
      
      const createButton = screen.getByRole('button', { name: /创建/i }) || 
                          screen.getByText(/创建/i)
      
      if (createButton) {
        const startTime = performance.now()
        
        createButton.click()
        
        const endTime = performance.now()
        const responseTime = endTime - startTime
        
        expect(responseTime).toBeLessThan(100) // 100ms内响应
      }
    })
  })

  describe('资源加载性能测试', () => {
    it('图片应该懒加载', async () => {
      renderWithProviders(<AgentsPage />)
      
      // 检查图片是否有懒加载属性
      await waitFor(() => {
        const images = screen.getAllByRole('img')
        images.forEach(img => {
          expect(img).toHaveAttribute('loading', 'lazy')
        })
      })
    })

    it('应该正确缓存静态资源', async () => {
      // 检查缓存策略
      const cacheHeaders = {
        'Cache-Control': 'public, max-age=31536000',
        'ETag': 'mock-etag'
      }
      
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        headers: new Headers(cacheHeaders),
        json: async () => ({ success: true })
      })
      
      await fetch('/api/static/resource')
      
      expect(global.fetch).toHaveBeenCalledWith('/api/static/resource')
    })
  })

  describe('移动端性能测试', () => {
    it('应该在移动设备上保持良好性能', async () => {
      // 模拟移动设备
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })
      
      Object.defineProperty(window, 'innerHeight', {
        writable: true,
        configurable: true,
        value: 667,
      })
      
      const startTime = performance.now()
      
      renderWithProviders(<ChatPage />)
      
      await waitFor(() => {
        expect(screen.getByRole('main') || document.body).toBeInTheDocument()
      })
      
      const endTime = performance.now()
      const mobileRenderTime = endTime - startTime
      
      // 移动端渲染时间应该合理
      expect(mobileRenderTime).toBeLessThan(1000)
    })
  })
})
