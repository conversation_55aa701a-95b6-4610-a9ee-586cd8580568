import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mockFetchSuccess, mockFetchError } from './utils'

describe('API端点详细测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    global.fetch = vi.fn()
  })

  describe('群聊列表API (/api/groups/list)', () => {
    it('应该返回正确的数据结构', async () => {
      const mockResponse = {
        my_groups: [
          {
            id: 1,
            name: '我的群聊1',
            type: 'private',
            created_by: 'user1',
            member_count: 3,
            agent_count: 2,
            last_message: '最后一条消息',
            last_message_time: '2024-01-01T00:00:00Z'
          }
        ],
        public_groups: [
          {
            id: 2,
            name: '公共群聊1',
            type: 'public',
            created_by: 'user2',
            member_count: 5,
            agent_count: 1,
            last_message: null,
            last_message_time: null
          }
        ]
      }

      global.fetch = vi.fn().mockResolvedValue(mockFetchSuccess(mockResponse))

      const response = await fetch('/api/groups/list')
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data).toHaveProperty('my_groups')
      expect(data.data).toHaveProperty('public_groups')
      expect(data.data.my_groups[0]).toHaveProperty('id')
      expect(data.data.my_groups[0]).toHaveProperty('name')
      expect(data.data.my_groups[0]).toHaveProperty('member_count')
      expect(data.data.my_groups[0]).toHaveProperty('agent_count')
    })

    it('应该处理空列表', async () => {
      const mockResponse = {
        my_groups: [],
        public_groups: []
      }

      global.fetch = vi.fn().mockResolvedValue(mockFetchSuccess(mockResponse))

      const response = await fetch('/api/groups/list')
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data.my_groups).toHaveLength(0)
      expect(data.data.public_groups).toHaveLength(0)
    })

    it('应该处理未认证用户', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(401, '用户未登录')
      )

      const response = await fetch('/api/groups/list')
      const data = await response.json()

      expect(response.ok).toBe(false)
      expect(data.message).toBe('用户未登录')
    })
  })

  describe('用户搜索API (/api/users/search)', () => {
    it('应该根据查询参数搜索用户', async () => {
      const mockUsers = [
        {
          id: '1',
          username: 'user1',
          nickname: 'User One',
          phone: '13800138001',
          avatar_url: null,
          status: 1
        },
        {
          id: '2',
          username: 'user2',
          nickname: 'User Two',
          phone: '13800138002',
          avatar_url: 'avatar.jpg',
          status: 1
        }
      ]

      global.fetch = vi.fn().mockResolvedValue(mockFetchSuccess(mockUsers))

      const response = await fetch('/api/users/search?q=user')
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data).toHaveLength(2)
      expect(data.data[0]).toHaveProperty('id')
      expect(data.data[0]).toHaveProperty('username')
      expect(data.data[0]).toHaveProperty('nickname')
    })

    it('应该排除指定群聊的成员', async () => {
      const mockUsers = [
        {
          id: '3',
          username: 'user3',
          nickname: 'User Three',
          phone: '13800138003',
          status: 1
        }
      ]

      global.fetch = vi.fn().mockResolvedValue(mockFetchSuccess(mockUsers))

      const response = await fetch('/api/users/search?q=user&exclude_group=1')
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data).toHaveLength(1)
      expect(data.data[0].id).toBe('3')
    })

    it('应该处理空查询', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(400, '搜索关键词不能为空')
      )

      const response = await fetch('/api/users/search?q=')
      const data = await response.json()

      expect(response.ok).toBe(false)
      expect(data.message).toBe('搜索关键词不能为空')
    })

    it('应该限制搜索结果数量', async () => {
      const mockUsers = Array.from({ length: 50 }, (_, i) => ({
        id: String(i + 1),
        username: `user${i + 1}`,
        nickname: `User ${i + 1}`,
        status: 1
      }))

      global.fetch = vi.fn().mockResolvedValue(
        mockFetchSuccess(mockUsers.slice(0, 20)) // 限制20个结果
      )

      const response = await fetch('/api/users/search?q=user&limit=20')
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data).toHaveLength(20)
    })
  })

  describe('邀请API (/api/groups/invite)', () => {
    it('应该成功发送邀请', async () => {
      const mockResponse = {
        invite_results: [
          {
            invitee: 'user2',
            success: true,
            message: '邀请已发送',
            invitation_id: 'inv_123'
          },
          {
            invitee: 'user3',
            success: true,
            message: '邀请已发送',
            invitation_id: 'inv_124'
          }
        ]
      }

      global.fetch = vi.fn().mockResolvedValue(mockFetchSuccess(mockResponse))

      const response = await fetch('/api/groups/invite', {
        method: 'POST',
        body: JSON.stringify({
          group_id: 1,
          invitees: ['user2', 'user3'],
          role: 'member',
          message: '邀请您加入群聊'
        })
      })
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data.invite_results).toHaveLength(2)
      expect(data.data.invite_results[0].success).toBe(true)
    })

    it('应该处理部分邀请失败', async () => {
      const mockResponse = {
        invite_results: [
          {
            invitee: 'user2',
            success: true,
            message: '邀请已发送',
            invitation_id: 'inv_123'
          },
          {
            invitee: 'user3',
            success: false,
            message: '用户已在群聊中'
          }
        ]
      }

      global.fetch = vi.fn().mockResolvedValue(mockFetchSuccess(mockResponse))

      const response = await fetch('/api/groups/invite', {
        method: 'POST',
        body: JSON.stringify({
          group_id: 1,
          invitees: ['user2', 'user3'],
          role: 'member'
        })
      })
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data.invite_results).toHaveLength(2)
      expect(data.data.invite_results[0].success).toBe(true)
      expect(data.data.invite_results[1].success).toBe(false)
    })

    it('应该验证邀请权限', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(403, '您没有邀请权限')
      )

      const response = await fetch('/api/groups/invite', {
        method: 'POST',
        body: JSON.stringify({
          group_id: 1,
          invitees: ['user2']
        })
      })
      const data = await response.json()

      expect(response.ok).toBe(false)
      expect(data.message).toBe('您没有邀请权限')
    })

    it('应该验证必填字段', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(400, '缺少必填字段')
      )

      const response = await fetch('/api/groups/invite', {
        method: 'POST',
        body: JSON.stringify({
          group_id: 1
          // 缺少invitees字段
        })
      })
      const data = await response.json()

      expect(response.ok).toBe(false)
      expect(data.message).toBe('缺少必填字段')
    })
  })

  describe('权重分析API (/api/groups/[groupId]/weights)', () => {
    it('应该返回完整的权重分析数据', async () => {
      const mockResponse = {
        group_id: 1,
        total_users: 3,
        analysis_time: '2024-01-01T00:00:00Z',
        user_weights: [
          {
            user_id: '1',
            username: 'user1',
            message_count: 50,
            total_chars: 2500,
            interaction_score: 10.0,
            last_active: '2024-01-01T00:00:00Z',
            weight_score: 45.5,
            priority_level: 'high'
          }
        ],
        recommendations: [
          '高活跃用户 (1人): user1 - 建议优先响应其需求'
        ]
      }

      global.fetch = vi.fn().mockResolvedValue(mockFetchSuccess(mockResponse))

      const response = await fetch('/api/groups/1/weights')
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data).toHaveProperty('group_id')
      expect(data.data).toHaveProperty('total_users')
      expect(data.data).toHaveProperty('user_weights')
      expect(data.data).toHaveProperty('recommendations')
      expect(data.data.user_weights[0]).toHaveProperty('weight_score')
      expect(data.data.user_weights[0]).toHaveProperty('priority_level')
    })

    it('应该处理空群聊', async () => {
      const mockResponse = {
        group_id: 1,
        total_users: 0,
        analysis_time: '2024-01-01T00:00:00Z',
        user_weights: [],
        recommendations: ['群聊中暂无用户成员']
      }

      global.fetch = vi.fn().mockResolvedValue(mockFetchSuccess(mockResponse))

      const response = await fetch('/api/groups/1/weights')
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data.total_users).toBe(0)
      expect(data.data.user_weights).toHaveLength(0)
    })

    it('应该验证群聊访问权限', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(403, '您不在此群聊中')
      )

      const response = await fetch('/api/groups/999/weights')
      const data = await response.json()

      expect(response.ok).toBe(false)
      expect(data.message).toBe('您不在此群聊中')
    })
  })

  describe('活跃度更新API (/api/groups/[groupId]/weights POST)', () => {
    it('应该成功更新消息活跃度', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchSuccess({ message: '活跃度数据更新成功' })
      )

      const response = await fetch('/api/groups/1/weights', {
        method: 'POST',
        body: JSON.stringify({
          user_id: '1',
          activity_type: 'message',
          value: 1
        })
      })
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data.message).toBe('活跃度数据更新成功')
    })

    it('应该验证活跃度类型', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(400, '无效的活跃度类型')
      )

      const response = await fetch('/api/groups/1/weights', {
        method: 'POST',
        body: JSON.stringify({
          user_id: '1',
          activity_type: 'invalid_type',
          value: 1
        })
      })
      const data = await response.json()

      expect(response.ok).toBe(false)
      expect(data.message).toBe('无效的活跃度类型')
    })

    it('应该验证数值范围', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(400, '数值超出有效范围')
      )

      const response = await fetch('/api/groups/1/weights', {
        method: 'POST',
        body: JSON.stringify({
          user_id: '1',
          activity_type: 'message',
          value: -1 // 负数无效
        })
      })
      const data = await response.json()

      expect(response.ok).toBe(false)
      expect(data.message).toBe('数值超出有效范围')
    })
  })
})
