import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mockFetchSuccess, mockFetchError } from './utils'

describe('权重分析功能', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    global.fetch = vi.fn()
  })

  describe('权重计算', () => {
    it('应该正确计算用户权重分数', async () => {
      const mockWeightData = {
        group_id: 1,
        total_users: 3,
        analysis_time: '2024-01-01T00:00:00Z',
        user_weights: [
          {
            user_id: '1',
            username: 'user1',
            message_count: 50,
            total_chars: 2500,
            interaction_score: 10.0,
            last_active: '2024-01-01T00:00:00Z',
            weight_score: 45.5,
            priority_level: 'high'
          },
          {
            user_id: '2',
            username: 'user2',
            message_count: 20,
            total_chars: 1000,
            interaction_score: 5.0,
            last_active: '2024-01-01T00:00:00Z',
            weight_score: 22.0,
            priority_level: 'medium'
          },
          {
            user_id: '3',
            username: 'user3',
            message_count: 5,
            total_chars: 200,
            interaction_score: 1.0,
            last_active: '2024-01-01T00:00:00Z',
            weight_score: 8.2,
            priority_level: 'low'
          }
        ],
        recommendations: [
          '高活跃用户 (1人): user1 - 建议优先响应其需求',
          '中等活跃用户 (1人): 建议适度关注并鼓励参与',
          '低活跃用户 (1人): 建议主动引导参与讨论'
        ]
      }

      global.fetch = vi.fn().mockResolvedValue(mockFetchSuccess(mockWeightData))

      const response = await fetch('/api/groups/1/weights')
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data.user_weights).toHaveLength(3)
      expect(data.data.user_weights[0].priority_level).toBe('high')
      expect(data.data.user_weights[0].weight_score).toBe(45.5)
      expect(data.data.recommendations).toHaveLength(3)
    })

    it('应该按权重分数排序用户', async () => {
      const mockWeightData = {
        group_id: 1,
        total_users: 3,
        analysis_time: '2024-01-01T00:00:00Z',
        user_weights: [
          { user_id: '1', weight_score: 45.5, priority_level: 'high' },
          { user_id: '2', weight_score: 22.0, priority_level: 'medium' },
          { user_id: '3', weight_score: 8.2, priority_level: 'low' }
        ],
        recommendations: []
      }

      global.fetch = vi.fn().mockResolvedValue(mockFetchSuccess(mockWeightData))

      const response = await fetch('/api/groups/1/weights')
      const data = await response.json()

      const weights = data.data.user_weights
      expect(weights[0].weight_score).toBeGreaterThan(weights[1].weight_score)
      expect(weights[1].weight_score).toBeGreaterThan(weights[2].weight_score)
    })

    it('应该正确分类优先级', async () => {
      const mockWeightData = {
        group_id: 1,
        total_users: 3,
        analysis_time: '2024-01-01T00:00:00Z',
        user_weights: [
          { user_id: '1', weight_score: 35.0, priority_level: 'high' },   // >= 30
          { user_id: '2', weight_score: 20.0, priority_level: 'medium' }, // >= 15
          { user_id: '3', weight_score: 10.0, priority_level: 'low' }     // < 15
        ],
        recommendations: []
      }

      global.fetch = vi.fn().mockResolvedValue(mockFetchSuccess(mockWeightData))

      const response = await fetch('/api/groups/1/weights')
      const data = await response.json()

      const weights = data.data.user_weights
      expect(weights.find(w => w.weight_score >= 30)?.priority_level).toBe('high')
      expect(weights.find(w => w.weight_score >= 15 && w.weight_score < 30)?.priority_level).toBe('medium')
      expect(weights.find(w => w.weight_score < 15)?.priority_level).toBe('low')
    })
  })

  describe('活跃度更新', () => {
    it('应该能够更新消息数量', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchSuccess({ message: '活跃度数据更新成功' })
      )

      const response = await fetch('/api/groups/1/weights', {
        method: 'POST',
        body: JSON.stringify({
          user_id: '1',
          activity_type: 'message',
          value: 1
        })
      })
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data.message).toBe('活跃度数据更新成功')
    })

    it('应该能够更新字符数', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchSuccess({ message: '活跃度数据更新成功' })
      )

      const response = await fetch('/api/groups/1/weights', {
        method: 'POST',
        body: JSON.stringify({
          user_id: '1',
          activity_type: 'chars',
          value: 100
        })
      })
      const data = await response.json()

      expect(data.success).toBe(true)
    })

    it('应该能够更新互动分数', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchSuccess({ message: '活跃度数据更新成功' })
      )

      const response = await fetch('/api/groups/1/weights', {
        method: 'POST',
        body: JSON.stringify({
          user_id: '1',
          activity_type: 'interaction',
          value: 2.0
        })
      })
      const data = await response.json()

      expect(data.success).toBe(true)
    })

    it('应该验证必填字段', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(400, '缺少必填字段：groupId, user_id, activity_type')
      )

      const response = await fetch('/api/groups/1/weights', {
        method: 'POST',
        body: JSON.stringify({
          user_id: '1'
          // 缺少 activity_type
        })
      })
      const data = await response.json()

      expect(response.ok).toBe(false)
      expect(data.message).toContain('缺少必填字段')
    })
  })

  describe('智能建议生成', () => {
    it('应该为不同活跃度用户生成建议', async () => {
      const mockWeightData = {
        group_id: 1,
        total_users: 5,
        analysis_time: '2024-01-01T00:00:00Z',
        user_weights: [
          { user_id: '1', priority_level: 'high', username: 'user1' },
          { user_id: '2', priority_level: 'high', username: 'user2' },
          { user_id: '3', priority_level: 'medium', username: 'user3' },
          { user_id: '4', priority_level: 'low', username: 'user4' },
          { user_id: '5', priority_level: 'low', username: 'user5' }
        ],
        recommendations: [
          '高活跃用户 (2人): user1, user2 - 建议优先响应其需求',
          '中等活跃用户 (1人): 建议适度关注并鼓励参与',
          '低活跃用户 (2人): 建议主动引导参与讨论'
        ]
      }

      global.fetch = vi.fn().mockResolvedValue(mockFetchSuccess(mockWeightData))

      const response = await fetch('/api/groups/1/weights')
      const data = await response.json()

      expect(data.data.recommendations).toHaveLength(3)
      expect(data.data.recommendations[0]).toContain('高活跃用户')
      expect(data.data.recommendations[1]).toContain('中等活跃用户')
      expect(data.data.recommendations[2]).toContain('低活跃用户')
    })

    it('应该为低活跃群聊生成特殊建议', async () => {
      const mockWeightData = {
        group_id: 1,
        total_users: 3,
        analysis_time: '2024-01-01T00:00:00Z',
        user_weights: [
          { user_id: '1', priority_level: 'low' },
          { user_id: '2', priority_level: 'low' },
          { user_id: '3', priority_level: 'low' }
        ],
        recommendations: [
          '低活跃用户 (3人): 建议主动引导参与讨论',
          '群聊活跃度较低，建议发起话题讨论或分享有价值的内容'
        ]
      }

      global.fetch = vi.fn().mockResolvedValue(mockFetchSuccess(mockWeightData))

      const response = await fetch('/api/groups/1/weights')
      const data = await response.json()

      expect(data.data.recommendations.some(r => r.includes('群聊活跃度较低'))).toBe(true)
    })
  })

  describe('权限验证', () => {
    it('应该验证用户是否在群聊中', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(403, '您不在此群聊中')
      )

      const response = await fetch('/api/groups/999/weights')
      const data = await response.json()

      expect(response.ok).toBe(false)
      expect(data.message).toBe('您不在此群聊中')
    })

    it('应该验证用户登录状态', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(401, '用户未登录')
      )

      const response = await fetch('/api/groups/1/weights')
      const data = await response.json()

      expect(response.ok).toBe(false)
      expect(data.message).toBe('用户未登录')
    })
  })
})
