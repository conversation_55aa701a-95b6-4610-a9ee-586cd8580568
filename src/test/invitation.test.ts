import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mockFetchSuccess, mockFetchError, mockInvitation, mockUser } from './utils'

describe('邀请功能', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    global.fetch = vi.fn()
  })

  describe('用户搜索', () => {
    it('应该能够搜索用户', async () => {
      const mockUsers = [
        { id: '1', username: 'user1', nickname: 'User 1' },
        { id: '2', username: 'user2', nickname: 'User 2' },
      ]

      global.fetch = vi.fn().mockResolvedValue(mockFetchSuccess(mockUsers))

      const response = await fetch('/api/users/search?q=user')
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data).toHaveLength(2)
      expect(data.data[0]).toHaveProperty('username')
      expect(data.data[0]).toHaveProperty('nickname')
    })

    it('应该过滤已在群聊中的用户', async () => {
      const mockUsers = [
        { id: '3', username: 'user3', nickname: 'User 3' },
      ]

      global.fetch = vi.fn().mockResolvedValue(mockFetchSuccess(mockUsers))

      const response = await fetch('/api/users/search?q=user&exclude_group=1')
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data).toHaveLength(1)
      expect(data.data[0].id).toBe('3')
    })

    it('应该处理空搜索结果', async () => {
      global.fetch = vi.fn().mockResolvedValue(mockFetchSuccess([]))

      const response = await fetch('/api/users/search?q=nonexistent')
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data).toHaveLength(0)
    })
  })

  describe('发送邀请', () => {
    it('应该能够发送邀请', async () => {
      const mockInviteResponse = {
        invitation_id: '1',
        message: '邀请已发送'
      }

      global.fetch = vi.fn().mockResolvedValue(mockFetchSuccess(mockInviteResponse))

      const response = await fetch('/api/groups/invite', {
        method: 'POST',
        body: JSON.stringify({
          group_id: 1,
          user_ids: ['2', '3']
        })
      })
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data.message).toBe('邀请已发送')
    })

    it('应该验证邀请权限', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(403, '您没有邀请权限')
      )

      const response = await fetch('/api/groups/invite', {
        method: 'POST',
        body: JSON.stringify({
          group_id: 1,
          user_ids: ['2']
        })
      })
      const data = await response.json()

      expect(response.ok).toBe(false)
      expect(data.message).toContain('没有邀请权限')
    })

    it('应该检查群聊成员数量限制', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(400, '群聊成员已达上限')
      )

      const response = await fetch('/api/groups/invite', {
        method: 'POST',
        body: JSON.stringify({
          group_id: 1,
          user_ids: ['2', '3', '4', '5', '6']
        })
      })
      const data = await response.json()

      expect(response.ok).toBe(false)
      expect(data.message).toContain('成员已达上限')
    })
  })

  describe('响应邀请', () => {
    it('应该能够接受邀请', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchSuccess({ message: '已成功加入群聊' })
      )

      const response = await fetch('/api/invitations/1/respond', {
        method: 'POST',
        body: JSON.stringify({ action: 'accept' })
      })
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data.message).toBe('已成功加入群聊')
    })

    it('应该能够拒绝邀请', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchSuccess({ message: '已拒绝邀请' })
      )

      const response = await fetch('/api/invitations/1/respond', {
        method: 'POST',
        body: JSON.stringify({ action: 'reject' })
      })
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data.message).toBe('已拒绝邀请')
    })

    it('应该验证邀请状态', async () => {
      global.fetch = vi.fn().mockResolvedValue(
        mockFetchError(400, '邀请已过期或无效')
      )

      const response = await fetch('/api/invitations/1/respond', {
        method: 'POST',
        body: JSON.stringify({ action: 'accept' })
      })
      const data = await response.json()

      expect(response.ok).toBe(false)
      expect(data.message).toContain('邀请已过期或无效')
    })
  })

  describe('邀请通知', () => {
    it('应该获取用户的邀请列表', async () => {
      const mockInvitations = [
        {
          ...mockInvitation,
          group_name: '测试群聊',
          inviter_name: '邀请者'
        }
      ]

      global.fetch = vi.fn().mockResolvedValue(mockFetchSuccess(mockInvitations))

      const response = await fetch('/api/invitations')
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data).toHaveLength(1)
      expect(data.data[0]).toHaveProperty('group_name')
      expect(data.data[0]).toHaveProperty('inviter_name')
    })

    it('应该只显示待处理的邀请', async () => {
      const mockInvitations = [
        { ...mockInvitation, id: '1', status: 'pending' },
        { ...mockInvitation, id: '2', status: 'accepted' },
        { ...mockInvitation, id: '3', status: 'rejected' },
      ]

      global.fetch = vi.fn().mockResolvedValue(
        mockFetchSuccess(mockInvitations.filter(inv => inv.status === 'pending'))
      )

      const response = await fetch('/api/invitations?status=pending')
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data).toHaveLength(1)
      expect(data.data[0].status).toBe('pending')
    })
  })
})
