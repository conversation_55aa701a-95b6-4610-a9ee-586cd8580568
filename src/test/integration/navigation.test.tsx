import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { MemoryRouter } from 'react-router-dom'
import { renderWithProviders } from '../utils'
import App from '@/App'

describe('Navigation and Routing Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Mock localStorage with login token
    const localStorageMock = {
      getItem: vi.fn((key) => {
        if (key === 'token') return 'mock-jwt-token'
        if (key === 'theme') return 'dark'
        return null
      }),
      setItem: vi.fn(),
      removeItem: vi.fn(),
      clear: vi.fn(),
    }
    vi.stubGlobal('localStorage', localStorageMock)
    
    // Mock fetch for API calls
    global.fetch = vi.fn().mockResolvedValue({
      ok: true,
      json: async () => ({
        success: true,
        data: { user: { id: '1', username: 'admin', role: 'admin' } }
      })
    })
  })

  describe('页面间导航测试', () => {
    it('应该能够在主要页面间正确导航', async () => {
      const user = userEvent.setup()
      
      render(
        <MemoryRouter initialEntries={['/']}>
          <App />
        </MemoryRouter>
      )
      
      // 等待页面加载
      await waitFor(() => {
        expect(screen.getByRole('main') || screen.getByTestId('app-container')).toBeInTheDocument()
      })
      
      // 测试导航到智能体商店
      const agentsLink = screen.getByText(/智能体商店/i) || screen.getByText(/Agents/i)
      if (agentsLink) {
        await user.click(agentsLink)
        await waitFor(() => {
          expect(window.location.pathname).toBe('/agents')
        })
      }
      
      // 测试导航到群聊
      const groupChatLink = screen.getByText(/群聊/i) || screen.getByText(/Group Chat/i)
      if (groupChatLink) {
        await user.click(groupChatLink)
        await waitFor(() => {
          expect(window.location.pathname).toBe('/group-chat')
        })
      }
    })

    it('应该正确处理受保护的路由', async () => {
      // 测试未登录状态
      const localStorageMock = {
        getItem: vi.fn(() => null), // 没有token
        setItem: vi.fn(),
        removeItem: vi.fn(),
        clear: vi.fn(),
      }
      vi.stubGlobal('localStorage', localStorageMock)
      
      render(
        <MemoryRouter initialEntries={['/settings']}>
          <App />
        </MemoryRouter>
      )
      
      // 应该重定向到登录页面
      await waitFor(() => {
        expect(window.location.pathname).toBe('/login')
      })
    })

    it('应该支持浏览器前进后退功能', async () => {
      const user = userEvent.setup()
      
      render(
        <MemoryRouter initialEntries={['/']}>
          <App />
        </MemoryRouter>
      )
      
      // 导航到不同页面
      const agentsLink = screen.getByText(/智能体商店/i) || screen.getByText(/Agents/i)
      if (agentsLink) {
        await user.click(agentsLink)
      }
      
      // 模拟浏览器后退
      window.history.back()
      
      await waitFor(() => {
        expect(window.location.pathname).toBe('/')
      })
    })
  })

  describe('侧边栏导航测试', () => {
    it('应该正确显示侧边栏菜单项', async () => {
      renderWithProviders(<App />, { initialEntries: ['/'] })
      
      // 检查主要菜单项
      const menuItems = [
        /超级智能体/i,
        /智能体商店/i,
        /群聊/i,
        /设置/i
      ]
      
      for (const item of menuItems) {
        const menuItem = screen.getByText(item) || screen.getByLabelText(item)
        expect(menuItem).toBeInTheDocument()
      }
    })

    it('应该支持侧边栏折叠展开', async () => {
      const user = userEvent.setup()
      renderWithProviders(<App />, { initialEntries: ['/'] })
      
      // 查找折叠按钮
      const collapseButton = screen.getByLabelText(/折叠/i) || screen.getByRole('button', { name: /menu/i })
      
      if (collapseButton) {
        await user.click(collapseButton)
        
        // 检查侧边栏是否折叠
        await waitFor(() => {
          const sidebar = screen.getByTestId('sidebar') || screen.getByRole('navigation')
          expect(sidebar).toHaveClass(/collapsed|minimized/)
        })
      }
    })

    it('应该高亮当前活动页面', async () => {
      renderWithProviders(<App />, { initialEntries: ['/agents'] })
      
      // 检查智能体商店菜单项是否高亮
      const agentsMenuItem = screen.getByText(/智能体商店/i)
      expect(agentsMenuItem.closest('li') || agentsMenuItem).toHaveClass(/active|selected|current/)
    })
  })

  describe('面包屑导航测试', () => {
    it('应该正确显示面包屑导航', async () => {
      renderWithProviders(<App />, { initialEntries: ['/group-chat/detail/123'] })
      
      // 检查面包屑
      const breadcrumb = screen.getByRole('navigation', { name: /breadcrumb/i }) || 
                        screen.getByTestId('breadcrumb')
      
      if (breadcrumb) {
        expect(breadcrumb).toBeInTheDocument()
        expect(breadcrumb).toHaveTextContent(/群聊/)
        expect(breadcrumb).toHaveTextContent(/详情/)
      }
    })

    it('应该支持面包屑点击导航', async () => {
      const user = userEvent.setup()
      renderWithProviders(<App />, { initialEntries: ['/group-chat/detail/123'] })
      
      const groupChatBreadcrumb = screen.getByText(/群聊/i)
      
      if (groupChatBreadcrumb && groupChatBreadcrumb.tagName === 'A') {
        await user.click(groupChatBreadcrumb)
        
        await waitFor(() => {
          expect(window.location.pathname).toBe('/group-chat')
        })
      }
    })
  })

  describe('搜索和筛选导航测试', () => {
    it('应该支持全局搜索功能', async () => {
      const user = userEvent.setup()
      renderWithProviders(<App />, { initialEntries: ['/'] })
      
      // 查找全局搜索框
      const globalSearch = screen.getByPlaceholderText(/全局搜索/i) || 
                          screen.getByRole('searchbox')
      
      if (globalSearch) {
        await user.type(globalSearch, '智能体')
        await user.keyboard('{Enter}')
        
        // 检查是否导航到搜索结果页面
        await waitFor(() => {
          expect(window.location.pathname).toMatch(/search|results/)
        })
      }
    })

    it('应该保持搜索状态在页面导航中', async () => {
      const user = userEvent.setup()
      renderWithProviders(<App />, { initialEntries: ['/agents?search=助手'] })
      
      // 检查搜索框是否保持搜索词
      const searchInput = screen.getByDisplayValue('助手') || 
                         screen.getByPlaceholderText(/搜索/i)
      
      expect(searchInput).toBeInTheDocument()
    })
  })

  describe('错误页面和404处理', () => {
    it('应该正确处理404页面', async () => {
      renderWithProviders(<App />, { initialEntries: ['/non-existent-page'] })
      
      // 检查404页面
      await waitFor(() => {
        expect(screen.getByText(/404/i) || screen.getByText(/页面不存在/i)).toBeInTheDocument()
      })
    })

    it('应该提供返回首页的链接', async () => {
      const user = userEvent.setup()
      renderWithProviders(<App />, { initialEntries: ['/non-existent-page'] })
      
      const homeLink = screen.getByText(/返回首页/i) || screen.getByText(/Home/i)
      
      if (homeLink) {
        await user.click(homeLink)
        
        await waitFor(() => {
          expect(window.location.pathname).toBe('/')
        })
      }
    })
  })

  describe('权限控制导航测试', () => {
    it('应该根据用户权限显示不同菜单', async () => {
      // 测试管理员权限
      renderWithProviders(<App />, { initialEntries: ['/'] })
      
      // 管理员应该能看到用户管理
      const userManagement = screen.getByText(/用户管理/i)
      expect(userManagement).toBeInTheDocument()
    })

    it('应该阻止无权限用户访问管理页面', async () => {
      // Mock普通用户
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: async () => ({
          success: true,
          data: { user: { id: '2', username: 'user', role: 'user' } }
        })
      })
      
      renderWithProviders(<App />, { initialEntries: ['/user-management'] })
      
      // 应该重定向或显示无权限页面
      await waitFor(() => {
        expect(screen.getByText(/无权限/i) || screen.getByText(/Unauthorized/i)).toBeInTheDocument()
      })
    })
  })
})
