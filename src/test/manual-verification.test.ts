import { describe, it, expect, beforeAll } from 'vitest'

// 手动验证测试 - 测试实际的API端点和功能
const BASE_URL = 'http://localhost:3001'

describe('手动功能验证测试', () => {
  let authToken: string | null = null
  let testGroupId: number | null = null

  beforeAll(async () => {
    // 尝试登录获取认证令牌
    try {
      const response = await fetch(`${BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: 'admin',
          password: 'admin123'
        })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success && data.data.token) {
          authToken = data.data.token
          console.log('✅ 登录成功，获取到认证令牌')
        }
      }
    } catch (error) {
      console.warn('⚠️ 无法连接到测试服务器，跳过实际API测试')
    }
  })

  describe('🔐 认证功能验证', () => {
    it('应该能够成功登录管理员账户', async () => {
      if (!authToken) {
        console.warn('⚠️ 跳过测试：无法获取认证令牌')
        return
      }

      const response = await fetch(`${BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: 'admin',
          password: 'admin123'
        })
      })

      expect(response.ok).toBe(true)
      
      const data = await response.json()
      expect(data.success).toBe(true)
      expect(data.data).toHaveProperty('user')
      expect(data.data).toHaveProperty('token')
      expect(data.data.user.username).toBe('admin')
      expect(data.data.user.role).toBe('admin')
      
      console.log('✅ 管理员登录功能正常')
    })

    it('应该拒绝错误的登录凭据', async () => {
      const response = await fetch(`${BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: 'admin',
          password: 'wrongpassword'
        })
      })

      expect(response.ok).toBe(false)
      
      const data = await response.json()
      expect(data.success).toBe(false)
      
      console.log('✅ 错误凭据拒绝功能正常')
    })
  })

  describe('📊 数据库功能验证', () => {
    it('应该能够初始化数据库', async () => {
      const response = await fetch(`${BASE_URL}/api/init-db`)
      
      expect(response.ok).toBe(true)
      
      const data = await response.json()
      expect(data.success).toBe(true)
      
      console.log('✅ 数据库初始化功能正常')
    })
  })

  describe('👥 群聊功能验证', () => {
    it('应该能够获取群聊列表', async () => {
      if (!authToken) {
        console.warn('⚠️ 跳过测试：无认证令牌')
        return
      }

      const response = await fetch(`${BASE_URL}/api/groups/list`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      })

      expect(response.ok).toBe(true)
      
      const data = await response.json()
      expect(data.success).toBe(true)
      expect(data.data).toHaveProperty('my_groups')
      expect(data.data).toHaveProperty('public_groups')
      expect(Array.isArray(data.data.my_groups)).toBe(true)
      expect(Array.isArray(data.data.public_groups)).toBe(true)
      
      console.log('✅ 群聊列表获取功能正常')
      console.log(`📊 我的群聊: ${data.data.my_groups.length}个`)
      console.log(`📊 公共群聊: ${data.data.public_groups.length}个`)
    })

    it('应该能够创建群聊', async () => {
      if (!authToken) {
        console.warn('⚠️ 跳过测试：无认证令牌')
        return
      }

      const testGroupName = `测试群聊_${Date.now()}`
      const response = await fetch(`${BASE_URL}/api/groups/create`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: testGroupName,
          description: '这是一个自动化测试创建的群聊',
          type: 'private'
        })
      })

      if (response.ok) {
        const data = await response.json()
        expect(data.success).toBe(true)
        expect(data.data).toHaveProperty('id')
        expect(data.data.name).toBe(testGroupName)
        
        testGroupId = data.data.id
        console.log('✅ 群聊创建功能正常')
        console.log(`📊 创建的群聊ID: ${testGroupId}`)
      } else {
        const errorData = await response.json()
        console.warn(`⚠️ 群聊创建失败: ${errorData.message}`)
        
        // 如果是因为数量限制失败，这也是正常的
        if (errorData.message && errorData.message.includes('上限')) {
          console.log('✅ 群聊数量限制功能正常')
        }
      }
    })
  })

  describe('🔍 用户搜索功能验证', () => {
    it('应该能够搜索用户', async () => {
      if (!authToken) {
        console.warn('⚠️ 跳过测试：无认证令牌')
        return
      }

      const response = await fetch(`${BASE_URL}/api/users/search?q=admin`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      })

      expect(response.ok).toBe(true)
      
      const data = await response.json()
      expect(data.success).toBe(true)
      expect(Array.isArray(data.data)).toBe(true)
      
      console.log('✅ 用户搜索功能正常')
      console.log(`📊 搜索到用户数量: ${data.data.length}`)
    })

    it('应该处理空搜索查询', async () => {
      if (!authToken) {
        console.warn('⚠️ 跳过测试：无认证令牌')
        return
      }

      const response = await fetch(`${BASE_URL}/api/users/search?q=`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      })

      // 应该返回错误或空结果
      if (response.ok) {
        const data = await response.json()
        expect(Array.isArray(data.data)).toBe(true)
        console.log('✅ 空搜索处理正常')
      } else {
        console.log('✅ 空搜索错误处理正常')
      }
    })
  })

  describe('📈 权重分析功能验证', () => {
    it('应该能够获取权重分析（如果有群聊）', async () => {
      if (!authToken) {
        console.warn('⚠️ 跳过测试：无认证令牌')
        return
      }

      // 先获取群聊列表
      const groupsResponse = await fetch(`${BASE_URL}/api/groups/list`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      })

      if (groupsResponse.ok) {
        const groupsData = await groupsResponse.json()
        const allGroups = [...groupsData.data.my_groups, ...groupsData.data.public_groups]
        
        if (allGroups.length > 0) {
          const firstGroup = allGroups[0]
          const weightsResponse = await fetch(`${BASE_URL}/api/groups/${firstGroup.id}/weights`, {
            headers: {
              'Authorization': `Bearer ${authToken}`
            }
          })

          if (weightsResponse.ok) {
            const weightsData = await weightsResponse.json()
            expect(weightsData.success).toBe(true)
            expect(weightsData.data).toHaveProperty('group_id')
            expect(weightsData.data).toHaveProperty('total_users')
            expect(weightsData.data).toHaveProperty('user_weights')
            expect(weightsData.data).toHaveProperty('recommendations')
            
            console.log('✅ 权重分析功能正常')
            console.log(`📊 群聊ID: ${weightsData.data.group_id}`)
            console.log(`📊 用户数量: ${weightsData.data.total_users}`)
            console.log(`📊 建议数量: ${weightsData.data.recommendations.length}`)
          } else {
            console.warn('⚠️ 权重分析API调用失败')
          }
        } else {
          console.warn('⚠️ 没有群聊可以测试权重分析')
        }
      }
    })
  })

  describe('🔗 API端点存在性验证', () => {
    const criticalEndpoints = [
      '/api/init-db',
      '/api/auth/login',
      '/api/groups/list',
      '/api/groups/create',
      '/api/users/search'
    ]

    criticalEndpoints.forEach(endpoint => {
      it(`应该存在关键端点: ${endpoint}`, async () => {
        const response = await fetch(`${BASE_URL}${endpoint}`)
        
        // 端点存在，即使返回401/403也是正常的
        expect(response.status).not.toBe(404)
        
        console.log(`✅ 端点 ${endpoint} 存在 (状态: ${response.status})`)
      })
    })
  })
})
