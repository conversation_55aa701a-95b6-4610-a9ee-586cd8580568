import { describe, it, expect, beforeEach, vi } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { render, mockFetch, mockFetchError, testUtils, userEvent } from '../utils'
import Login from '../../pages/login'

describe('Login Page - 登录页面', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    localStorage.clear()
    sessionStorage.clear()
  })

  describe('页面渲染测试', () => {
    it('应该正确渲染登录页面的所有元素', () => {
      render(<Login />)
      
      // 检查页面标题
      expect(screen.getByText(/AgentGroup/i)).toBeInTheDocument()
      expect(screen.getByText(/智能群聊平台/i)).toBeInTheDocument()
      
      // 检查表单元素
      expect(screen.getByLabelText(/用户名/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/密码/i)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /登录/i })).toBeInTheDocument()
      
      // 检查记住我选项
      expect(screen.getByLabelText(/记住我/i)).toBeInTheDocument()
      
      // 检查忘记密码链接
      expect(screen.getByText(/忘记密码/i)).toBeInTheDocument()
    })

    it('应该正确显示主题切换按钮', () => {
      render(<Login />)
      
      const themeToggle = screen.getByRole('button', { name: /切换主题/i })
      expect(themeToggle).toBeInTheDocument()
    })

    it('应该在不同主题下正确渲染', () => {
      const { rerender } = render(<Login />, { theme: 'light' })
      expect(document.documentElement).toHaveClass('light')
      
      rerender(<Login />)
      // 测试主题切换功能
    })
  })

  describe('表单验证测试', () => {
    it('应该显示空用户名的错误信息', async () => {
      render(<Login />)
      
      const submitButton = screen.getByRole('button', { name: /登录/i })
      await userEvent.click(submitButton)
      
      await waitFor(() => {
        expect(screen.getByText(/请输入用户名/i)).toBeInTheDocument()
      })
    })

    it('应该显示空密码的错误信息', async () => {
      render(<Login />)
      
      const usernameInput = screen.getByLabelText(/用户名/i)
      await userEvent.type(usernameInput, 'admin')
      
      const submitButton = screen.getByRole('button', { name: /登录/i })
      await userEvent.click(submitButton)
      
      await waitFor(() => {
        expect(screen.getByText(/请输入密码/i)).toBeInTheDocument()
      })
    })

    it('应该显示用户名长度验证错误', async () => {
      render(<Login />)
      
      const usernameInput = screen.getByLabelText(/用户名/i)
      await userEvent.type(usernameInput, 'ab') // 太短
      
      const submitButton = screen.getByRole('button', { name: /登录/i })
      await userEvent.click(submitButton)
      
      await waitFor(() => {
        expect(screen.getByText(/用户名至少需要3个字符/i)).toBeInTheDocument()
      })
    })

    it('应该显示密码长度验证错误', async () => {
      render(<Login />)
      
      const usernameInput = screen.getByLabelText(/用户名/i)
      const passwordInput = screen.getByLabelText(/密码/i)
      
      await userEvent.type(usernameInput, 'admin')
      await userEvent.type(passwordInput, '123') // 太短
      
      const submitButton = screen.getByRole('button', { name: /登录/i })
      await userEvent.click(submitButton)
      
      await waitFor(() => {
        expect(screen.getByText(/密码至少需要6个字符/i)).toBeInTheDocument()
      })
    })
  })

  describe('登录功能测试', () => {
    it('应该成功登录并跳转到主页', async () => {
      const mockResponse = {
        success: true,
        data: {
          token: 'mock-jwt-token',
          user: {
            id: '1',
            username: 'admin',
            nickname: '管理员',
            role: 'admin',
            balance: 10000
          }
        }
      }
      
      mockFetch(mockResponse)
      
      render(<Login />)
      
      const usernameInput = screen.getByLabelText(/用户名/i)
      const passwordInput = screen.getByLabelText(/密码/i)
      const submitButton = screen.getByRole('button', { name: /登录/i })
      
      await userEvent.type(usernameInput, 'admin')
      await userEvent.type(passwordInput, 'admin123')
      await userEvent.click(submitButton)
      
      // 检查加载状态
      expect(screen.getByText(/登录中/i)).toBeInTheDocument()
      
      // 等待登录完成
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            username: 'admin',
            password: 'admin123'
          })
        })
      })
      
      // 检查token是否保存到localStorage
      await waitFor(() => {
        expect(localStorage.setItem).toHaveBeenCalledWith('token', 'mock-jwt-token')
      })
    })

    it('应该处理登录失败的情况', async () => {
      const mockResponse = {
        success: false,
        message: '用户名或密码错误'
      }
      
      mockFetch(mockResponse, 401)
      
      render(<Login />)
      
      const usernameInput = screen.getByLabelText(/用户名/i)
      const passwordInput = screen.getByLabelText(/密码/i)
      const submitButton = screen.getByRole('button', { name: /登录/i })
      
      await userEvent.type(usernameInput, 'admin')
      await userEvent.type(passwordInput, 'wrongpassword')
      await userEvent.click(submitButton)
      
      await waitFor(() => {
        expect(screen.getByText(/用户名或密码错误/i)).toBeInTheDocument()
      })
      
      // 确保没有保存token
      expect(localStorage.setItem).not.toHaveBeenCalledWith('token', expect.any(String))
    })

    it('应该处理网络错误', async () => {
      mockFetchError('Network error')
      
      render(<Login />)
      
      const usernameInput = screen.getByLabelText(/用户名/i)
      const passwordInput = screen.getByLabelText(/密码/i)
      const submitButton = screen.getByRole('button', { name: /登录/i })
      
      await userEvent.type(usernameInput, 'admin')
      await userEvent.type(passwordInput, 'admin123')
      await userEvent.click(submitButton)
      
      await waitFor(() => {
        expect(screen.getByText(/网络错误，请稍后重试/i)).toBeInTheDocument()
      })
    })
  })

  describe('记住我功能测试', () => {
    it('应该在勾选记住我时保存用户名', async () => {
      render(<Login />)
      
      const usernameInput = screen.getByLabelText(/用户名/i)
      const rememberCheckbox = screen.getByLabelText(/记住我/i)
      
      await userEvent.type(usernameInput, 'admin')
      await userEvent.click(rememberCheckbox)
      
      expect(rememberCheckbox).toBeChecked()
      
      // 模拟页面刷新后检查用户名是否被记住
      // 这里需要检查localStorage中是否保存了用户名
    })
  })

  describe('键盘交互测试', () => {
    it('应该支持Enter键提交表单', async () => {
      const mockResponse = {
        success: true,
        data: { token: 'test-token', user: { id: '1', username: 'admin' } }
      }
      
      mockFetch(mockResponse)
      
      render(<Login />)
      
      const usernameInput = screen.getByLabelText(/用户名/i)
      const passwordInput = screen.getByLabelText(/密码/i)
      
      await userEvent.type(usernameInput, 'admin')
      await userEvent.type(passwordInput, 'admin123')
      await userEvent.keyboard('{Enter}')
      
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalled()
      })
    })

    it('应该支持Tab键在表单元素间导航', async () => {
      render(<Login />)
      
      const usernameInput = screen.getByLabelText(/用户名/i)
      const passwordInput = screen.getByLabelText(/密码/i)
      const rememberCheckbox = screen.getByLabelText(/记住我/i)
      const submitButton = screen.getByRole('button', { name: /登录/i })
      
      usernameInput.focus()
      expect(usernameInput).toHaveFocus()
      
      await userEvent.keyboard('{Tab}')
      expect(passwordInput).toHaveFocus()
      
      await userEvent.keyboard('{Tab}')
      expect(rememberCheckbox).toHaveFocus()
      
      await userEvent.keyboard('{Tab}')
      expect(submitButton).toHaveFocus()
    })
  })

  describe('响应式设计测试', () => {
    it('应该在移动设备上正确显示', () => {
      // 模拟移动设备视口
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })
      
      render(<Login />)
      
      // 检查移动端特定的样式或布局
      const container = screen.getByRole('main') || screen.getByTestId('login-container')
      expect(container).toBeInTheDocument()
    })
  })

  describe('无障碍性测试', () => {
    it('应该有正确的ARIA标签', () => {
      render(<Login />)
      
      const usernameInput = screen.getByLabelText(/用户名/i)
      const passwordInput = screen.getByLabelText(/密码/i)
      
      expect(usernameInput).toHaveAttribute('aria-required', 'true')
      expect(passwordInput).toHaveAttribute('aria-required', 'true')
    })

    it('应该在表单验证失败时设置正确的ARIA属性', async () => {
      render(<Login />)
      
      const submitButton = screen.getByRole('button', { name: /登录/i })
      await userEvent.click(submitButton)
      
      await waitFor(() => {
        const usernameInput = screen.getByLabelText(/用户名/i)
        expect(usernameInput).toHaveAttribute('aria-invalid', 'true')
      })
    })
  })
})
