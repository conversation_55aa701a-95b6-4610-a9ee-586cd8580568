import { describe, it, expect, beforeEach, vi } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { render, mockFetch, mockFetchError, testUtils, userEvent } from '../utils'
import GroupChat from '../../pages/group-chat'

describe('GroupChat Page - 群聊页面', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    localStorage.clear()
    
    // Mock user data
    localStorage.setItem('token', 'mock-token')
    localStorage.setItem('user', JSON.stringify({
      id: '1',
      username: 'admin',
      nickname: '管理员',
      role: 'admin'
    }))
  })

  describe('页面渲染测试', () => {
    it('应该正确渲染群聊页面的所有元素', async () => {
      const mockGroups = {
        success: true,
        data: {
          my_groups: [
            { id: 1, name: '技术讨论群', type: 'mixed', member_count: 5, agent_count: 2 }
          ],
          public_groups: [
            { id: 2, name: '公开讨论群', type: 'public', member_count: 10, agent_count: 3 }
          ]
        }
      }
      
      mockFetch(mockGroups)
      
      render(<GroupChat />)
      
      // 检查页面标题
      expect(screen.getByText(/群聊管理/i)).toBeInTheDocument()
      
      // 检查创建群聊按钮
      expect(screen.getByRole('button', { name: /创建群聊/i })).toBeInTheDocument()
      
      // 等待群聊列表加载
      await waitFor(() => {
        expect(screen.getByText(/技术讨论群/i)).toBeInTheDocument()
        expect(screen.getByText(/公开讨论群/i)).toBeInTheDocument()
      })
    })

    it('应该正确分类显示我的群聊和公共群聊', async () => {
      const mockGroups = {
        success: true,
        data: {
          my_groups: [
            { id: 1, name: '我的群聊1', type: 'mixed', member_count: 5, agent_count: 2 }
          ],
          public_groups: [
            { id: 2, name: '公共群聊1', type: 'public', member_count: 10, agent_count: 3 }
          ]
        }
      }
      
      mockFetch(mockGroups)
      
      render(<GroupChat />)
      
      await waitFor(() => {
        expect(screen.getByText(/我的群聊/i)).toBeInTheDocument()
        expect(screen.getByText(/公共群聊/i)).toBeInTheDocument()
      })
    })

    it('应该显示群聊的详细信息', async () => {
      const mockGroups = {
        success: true,
        data: {
          my_groups: [
            { 
              id: 1, 
              name: '技术讨论群', 
              type: 'mixed', 
              member_count: 5, 
              agent_count: 2,
              description: '讨论技术问题的群聊',
              created_at: '2024-01-01T10:00:00Z'
            }
          ],
          public_groups: []
        }
      }
      
      mockFetch(mockGroups)
      
      render(<GroupChat />)
      
      await waitFor(() => {
        expect(screen.getByText(/技术讨论群/i)).toBeInTheDocument()
        expect(screen.getByText(/5人/)).toBeInTheDocument()
        expect(screen.getByText(/2个智能体/)).toBeInTheDocument()
        expect(screen.getByText(/讨论技术问题的群聊/i)).toBeInTheDocument()
      })
    })
  })

  describe('创建群聊功能测试', () => {
    it('应该打开创建群聊对话框', async () => {
      render(<GroupChat />)
      
      const createButton = screen.getByRole('button', { name: /创建群聊/i })
      await userEvent.click(createButton)
      
      await waitFor(() => {
        expect(screen.getByText(/创建新群聊/i)).toBeInTheDocument()
        expect(screen.getByLabelText(/群聊名称/i)).toBeInTheDocument()
        expect(screen.getByLabelText(/群聊描述/i)).toBeInTheDocument()
        expect(screen.getByLabelText(/群聊类型/i)).toBeInTheDocument()
      })
    })

    it('应该验证群聊名称必填', async () => {
      render(<GroupChat />)
      
      const createButton = screen.getByRole('button', { name: /创建群聊/i })
      await userEvent.click(createButton)
      
      await waitFor(async () => {
        const submitButton = screen.getByRole('button', { name: /确认创建/i })
        await userEvent.click(submitButton)
        
        expect(screen.getByText(/请输入群聊名称/i)).toBeInTheDocument()
      })
    })

    it('应该成功创建群聊', async () => {
      const mockCreateResponse = {
        success: true,
        data: {
          id: 3,
          name: '新建群聊',
          type: 'mixed',
          description: '这是一个新建的群聊'
        }
      }
      
      mockFetch(mockCreateResponse)
      
      render(<GroupChat />)
      
      const createButton = screen.getByRole('button', { name: /创建群聊/i })
      await userEvent.click(createButton)
      
      await waitFor(async () => {
        const nameInput = screen.getByLabelText(/群聊名称/i)
        const descInput = screen.getByLabelText(/群聊描述/i)
        const typeSelect = screen.getByLabelText(/群聊类型/i)
        
        await userEvent.type(nameInput, '新建群聊')
        await userEvent.type(descInput, '这是一个新建的群聊')
        await userEvent.selectOptions(typeSelect, 'mixed')
        
        const submitButton = screen.getByRole('button', { name: /确认创建/i })
        await userEvent.click(submitButton)
      })
      
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/groups/create', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer mock-token'
          },
          body: JSON.stringify({
            name: '新建群聊',
            description: '这是一个新建的群聊',
            group_type: 'mixed'
          })
        })
      })
      
      await waitFor(() => {
        expect(screen.getByText(/群聊创建成功/i)).toBeInTheDocument()
      })
    })

    it('应该处理创建群聊失败的情况', async () => {
      mockFetch({ success: false, message: '群聊名称已存在' }, 400)
      
      render(<GroupChat />)
      
      const createButton = screen.getByRole('button', { name: /创建群聊/i })
      await userEvent.click(createButton)
      
      await waitFor(async () => {
        const nameInput = screen.getByLabelText(/群聊名称/i)
        await userEvent.type(nameInput, '重复名称')
        
        const submitButton = screen.getByRole('button', { name: /确认创建/i })
        await userEvent.click(submitButton)
      })
      
      await waitFor(() => {
        expect(screen.getByText(/群聊名称已存在/i)).toBeInTheDocument()
      })
    })
  })

  describe('群聊操作功能测试', () => {
    beforeEach(async () => {
      const mockGroups = {
        success: true,
        data: {
          my_groups: [
            { id: 1, name: '技术讨论群', type: 'mixed', member_count: 5, agent_count: 2 }
          ],
          public_groups: []
        }
      }
      
      mockFetch(mockGroups)
    })

    it('应该能够进入群聊', async () => {
      render(<GroupChat />)
      
      await waitFor(async () => {
        const groupItem = screen.getByText(/技术讨论群/i)
        await userEvent.click(groupItem)
      })
      
      // 检查是否导航到群聊详情页
      expect(window.location.pathname).toBe('/group/1')
    })

    it('应该显示群聊操作菜单', async () => {
      render(<GroupChat />)
      
      await waitFor(async () => {
        const moreButton = screen.getByRole('button', { name: /更多操作/i })
        await userEvent.click(moreButton)
        
        expect(screen.getByText(/编辑群聊/i)).toBeInTheDocument()
        expect(screen.getByText(/邀请成员/i)).toBeInTheDocument()
        expect(screen.getByText(/群聊设置/i)).toBeInTheDocument()
        expect(screen.getByText(/删除群聊/i)).toBeInTheDocument()
      })
    })

    it('应该能够编辑群聊信息', async () => {
      const mockUpdateResponse = { success: true }
      mockFetch(mockUpdateResponse)
      
      render(<GroupChat />)
      
      await waitFor(async () => {
        const moreButton = screen.getByRole('button', { name: /更多操作/i })
        await userEvent.click(moreButton)
        
        const editButton = screen.getByText(/编辑群聊/i)
        await userEvent.click(editButton)
      })
      
      await waitFor(async () => {
        const nameInput = screen.getByDisplayValue(/技术讨论群/i)
        await userEvent.clear(nameInput)
        await userEvent.type(nameInput, '技术讨论群（已更新）')
        
        const saveButton = screen.getByRole('button', { name: /保存/i })
        await userEvent.click(saveButton)
      })
      
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/groups/1', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer mock-token'
          },
          body: expect.stringContaining('技术讨论群（已更新）')
        })
      })
    })

    it('应该能够删除群聊', async () => {
      const mockDeleteResponse = { success: true }
      mockFetch(mockDeleteResponse)
      
      render(<GroupChat />)
      
      await waitFor(async () => {
        const moreButton = screen.getByRole('button', { name: /更多操作/i })
        await userEvent.click(moreButton)
        
        const deleteButton = screen.getByText(/删除群聊/i)
        await userEvent.click(deleteButton)
      })
      
      await waitFor(async () => {
        // 确认删除对话框
        expect(screen.getByText(/确认删除群聊/i)).toBeInTheDocument()
        
        const confirmButton = screen.getByRole('button', { name: /确认删除/i })
        await userEvent.click(confirmButton)
      })
      
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/groups/1', {
          method: 'DELETE',
          headers: {
            'Authorization': 'Bearer mock-token'
          }
        })
      })
    })
  })

  describe('邀请成员功能测试', () => {
    it('应该打开邀请成员对话框', async () => {
      const mockGroups = {
        success: true,
        data: {
          my_groups: [
            { id: 1, name: '技术讨论群', type: 'mixed', member_count: 5, agent_count: 2 }
          ],
          public_groups: []
        }
      }
      
      mockFetch(mockGroups)
      
      render(<GroupChat />)
      
      await waitFor(async () => {
        const moreButton = screen.getByRole('button', { name: /更多操作/i })
        await userEvent.click(moreButton)
        
        const inviteButton = screen.getByText(/邀请成员/i)
        await userEvent.click(inviteButton)
      })
      
      await waitFor(() => {
        expect(screen.getByText(/邀请新成员/i)).toBeInTheDocument()
        expect(screen.getByPlaceholderText(/搜索用户/i)).toBeInTheDocument()
      })
    })

    it('应该能够搜索用户', async () => {
      const mockUsers = {
        success: true,
        data: [
          { id: '2', username: 'alice', nickname: '爱丽丝', phone: '13800138001' }
        ]
      }
      
      mockFetch(mockUsers)
      
      render(<GroupChat />)
      
      // 打开邀请对话框
      await waitFor(async () => {
        const moreButton = screen.getByRole('button', { name: /更多操作/i })
        await userEvent.click(moreButton)
        
        const inviteButton = screen.getByText(/邀请成员/i)
        await userEvent.click(inviteButton)
      })
      
      await waitFor(async () => {
        const searchInput = screen.getByPlaceholderText(/搜索用户/i)
        await userEvent.type(searchInput, 'alice')
      })
      
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(
          '/api/users/search?q=alice',
          expect.objectContaining({
            headers: expect.objectContaining({
              'Authorization': 'Bearer mock-token'
            })
          })
        )
      })
      
      await waitFor(() => {
        expect(screen.getByText(/爱丽丝/i)).toBeInTheDocument()
        expect(screen.getByText(/alice/i)).toBeInTheDocument()
      })
    })

    it('应该能够发送邀请', async () => {
      const mockInviteResponse = { success: true }
      mockFetch(mockInviteResponse)
      
      render(<GroupChat />)
      
      // 模拟选择用户并发送邀请
      await waitFor(async () => {
        const userItem = screen.getByText(/爱丽丝/i)
        await userEvent.click(userItem)
        
        const inviteButton = screen.getByRole('button', { name: /发送邀请/i })
        await userEvent.click(inviteButton)
      })
      
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/groups/invite', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer mock-token'
          },
          body: expect.stringContaining('alice')
        })
      })
    })
  })

  describe('搜索和筛选功能测试', () => {
    it('应该能够搜索群聊', async () => {
      const mockGroups = {
        success: true,
        data: {
          my_groups: [
            { id: 1, name: '技术讨论群', type: 'mixed', member_count: 5, agent_count: 2 },
            { id: 2, name: '产品设计团队', type: 'mixed', member_count: 3, agent_count: 1 }
          ],
          public_groups: []
        }
      }
      
      mockFetch(mockGroups)
      
      render(<GroupChat />)
      
      await waitFor(async () => {
        const searchInput = screen.getByPlaceholderText(/搜索群聊/i)
        await userEvent.type(searchInput, '技术')
      })
      
      await waitFor(() => {
        expect(screen.getByText(/技术讨论群/i)).toBeInTheDocument()
        expect(screen.queryByText(/产品设计团队/i)).not.toBeInTheDocument()
      })
    })

    it('应该能够按类型筛选群聊', async () => {
      const mockGroups = {
        success: true,
        data: {
          my_groups: [
            { id: 1, name: '混合群聊', type: 'mixed', member_count: 5, agent_count: 2 },
            { id: 2, name: '纯AI群聊', type: 'ai_only', member_count: 0, agent_count: 5 }
          ],
          public_groups: []
        }
      }
      
      mockFetch(mockGroups)
      
      render(<GroupChat />)
      
      await waitFor(async () => {
        const filterSelect = screen.getByLabelText(/筛选类型/i)
        await userEvent.selectOptions(filterSelect, 'ai_only')
      })
      
      await waitFor(() => {
        expect(screen.getByText(/纯AI群聊/i)).toBeInTheDocument()
        expect(screen.queryByText(/混合群聊/i)).not.toBeInTheDocument()
      })
    })
  })

  describe('错误处理测试', () => {
    it('应该处理群聊列表加载失败', async () => {
      mockFetchError('Network error')
      
      render(<GroupChat />)
      
      await waitFor(() => {
        expect(screen.getByText(/加载群聊列表失败/i)).toBeInTheDocument()
      })
    })

    it('应该显示空状态', async () => {
      const mockEmptyGroups = {
        success: true,
        data: {
          my_groups: [],
          public_groups: []
        }
      }
      
      mockFetch(mockEmptyGroups)
      
      render(<GroupChat />)
      
      await waitFor(() => {
        expect(screen.getByText(/暂无群聊/i)).toBeInTheDocument()
        expect(screen.getByText(/创建您的第一个群聊/i)).toBeInTheDocument()
      })
    })
  })

  describe('响应式设计测试', () => {
    it('应该在移动设备上正确显示', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })
      
      render(<GroupChat />)
      
      // 检查移动端布局
      const container = screen.getByRole('main')
      expect(container).toHaveClass('mobile-layout')
    })
  })

  describe('无障碍性测试', () => {
    it('应该有正确的ARIA标签', () => {
      render(<GroupChat />)
      
      const searchInput = screen.getByPlaceholderText(/搜索群聊/i)
      expect(searchInput).toHaveAttribute('aria-label', '搜索群聊')
      
      const createButton = screen.getByRole('button', { name: /创建群聊/i })
      expect(createButton).toHaveAttribute('aria-label', '创建新群聊')
    })
  })
})
