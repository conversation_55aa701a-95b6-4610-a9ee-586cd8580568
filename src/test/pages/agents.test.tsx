import { describe, it, expect, beforeEach, vi } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { render, mockFetch, mockFetchError, testUtils, userEvent } from '../utils'
import Agents from '../../pages/agents'

describe('Agents Page - 智能体商店页面', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    localStorage.clear()
    
    // Mock user data
    localStorage.setItem('token', 'mock-token')
    localStorage.setItem('user', JSON.stringify({
      id: '1',
      username: 'admin',
      nickname: '管理员',
      role: 'admin'
    }))
  })

  describe('页面渲染测试', () => {
    it('应该正确渲染智能体商店页面的所有元素', async () => {
      const mockAgents = {
        success: true,
        data: [
          {
            id: '1',
            name: '代码审查助手',
            description: '帮助审查代码质量和安全性',
            category: 'development',
            avatar: null,
            rating: 4.8,
            downloads: 1250,
            created_by: 'system'
          },
          {
            id: '2',
            name: '数据分析师',
            description: '专业的数据分析和可视化助手',
            category: 'analytics',
            avatar: null,
            rating: 4.6,
            downloads: 890,
            created_by: 'system'
          }
        ]
      }
      
      mockFetch(mockAgents)
      
      render(<Agents />)
      
      // 检查页面标题
      expect(screen.getByText(/智能体商店/i)).toBeInTheDocument()
      
      // 检查搜索框
      expect(screen.getByPlaceholderText(/搜索智能体/i)).toBeInTheDocument()
      
      // 检查分类筛选
      expect(screen.getByText(/全部分类/i)).toBeInTheDocument()
      
      // 检查上传按钮
      expect(screen.getByRole('button', { name: /上传智能体/i })).toBeInTheDocument()
      
      // 等待智能体列表加载
      await waitFor(() => {
        expect(screen.getByText(/代码审查助手/i)).toBeInTheDocument()
        expect(screen.getByText(/数据分析师/i)).toBeInTheDocument()
      })
    })

    it('应该显示智能体的详细信息', async () => {
      const mockAgents = {
        success: true,
        data: [
          {
            id: '1',
            name: '代码审查助手',
            description: '帮助审查代码质量和安全性',
            category: 'development',
            avatar: null,
            rating: 4.8,
            downloads: 1250,
            created_by: 'system',
            tags: ['代码', '审查', '安全'],
            model: 'gpt-4',
            price: 0
          }
        ]
      }
      
      mockFetch(mockAgents)
      
      render(<Agents />)
      
      await waitFor(() => {
        expect(screen.getByText(/代码审查助手/i)).toBeInTheDocument()
        expect(screen.getByText(/帮助审查代码质量和安全性/i)).toBeInTheDocument()
        expect(screen.getByText(/4.8/)).toBeInTheDocument()
        expect(screen.getByText(/1250/)).toBeInTheDocument()
        expect(screen.getByText(/免费/i)).toBeInTheDocument()
      })
    })

    it('应该显示分类标签', async () => {
      const mockAgents = {
        success: true,
        data: [
          {
            id: '1',
            name: '代码审查助手',
            category: 'development',
            description: '测试描述'
          }
        ]
      }
      
      mockFetch(mockAgents)
      
      render(<Agents />)
      
      await waitFor(() => {
        expect(screen.getByText(/开发工具/i)).toBeInTheDocument()
      })
    })
  })

  describe('搜索功能测试', () => {
    beforeEach(async () => {
      const mockAgents = {
        success: true,
        data: [
          {
            id: '1',
            name: '代码审查助手',
            description: '帮助审查代码质量',
            category: 'development'
          },
          {
            id: '2',
            name: '数据分析师',
            description: '数据分析和可视化',
            category: 'analytics'
          }
        ]
      }
      
      mockFetch(mockAgents)
    })

    it('应该能够搜索智能体', async () => {
      render(<Agents />)
      
      await waitFor(async () => {
        const searchInput = screen.getByPlaceholderText(/搜索智能体/i)
        await userEvent.type(searchInput, '代码')
      })
      
      await waitFor(() => {
        expect(screen.getByText(/代码审查助手/i)).toBeInTheDocument()
        expect(screen.queryByText(/数据分析师/i)).not.toBeInTheDocument()
      })
    })

    it('应该支持实时搜索', async () => {
      render(<Agents />)
      
      const searchInput = screen.getByPlaceholderText(/搜索智能体/i)
      
      await userEvent.type(searchInput, '数据')
      
      await waitFor(() => {
        expect(screen.getByText(/数据分析师/i)).toBeInTheDocument()
        expect(screen.queryByText(/代码审查助手/i)).not.toBeInTheDocument()
      })
      
      await userEvent.clear(searchInput)
      await userEvent.type(searchInput, '代码')
      
      await waitFor(() => {
        expect(screen.getByText(/代码审查助手/i)).toBeInTheDocument()
        expect(screen.queryByText(/数据分析师/i)).not.toBeInTheDocument()
      })
    })

    it('应该显示搜索无结果状态', async () => {
      render(<Agents />)
      
      const searchInput = screen.getByPlaceholderText(/搜索智能体/i)
      await userEvent.type(searchInput, '不存在的智能体')
      
      await waitFor(() => {
        expect(screen.getByText(/未找到相关智能体/i)).toBeInTheDocument()
      })
    })
  })

  describe('分类筛选功能测试', () => {
    beforeEach(async () => {
      const mockAgents = {
        success: true,
        data: [
          {
            id: '1',
            name: '代码审查助手',
            category: 'development'
          },
          {
            id: '2',
            name: '数据分析师',
            category: 'analytics'
          },
          {
            id: '3',
            name: '写作助手',
            category: 'writing'
          }
        ]
      }
      
      mockFetch(mockAgents)
    })

    it('应该能够按分类筛选智能体', async () => {
      render(<Agents />)
      
      await waitFor(async () => {
        const categoryFilter = screen.getByLabelText(/分类筛选/i)
        await userEvent.selectOptions(categoryFilter, 'development')
      })
      
      await waitFor(() => {
        expect(screen.getByText(/代码审查助手/i)).toBeInTheDocument()
        expect(screen.queryByText(/数据分析师/i)).not.toBeInTheDocument()
        expect(screen.queryByText(/写作助手/i)).not.toBeInTheDocument()
      })
    })

    it('应该显示所有分类选项', () => {
      render(<Agents />)
      
      const categoryFilter = screen.getByLabelText(/分类筛选/i)
      
      expect(screen.getByText(/全部分类/i)).toBeInTheDocument()
      expect(screen.getByText(/开发工具/i)).toBeInTheDocument()
      expect(screen.getByText(/数据分析/i)).toBeInTheDocument()
      expect(screen.getByText(/写作助手/i)).toBeInTheDocument()
      expect(screen.getByText(/客服助手/i)).toBeInTheDocument()
    })
  })

  describe('智能体操作功能测试', () => {
    beforeEach(async () => {
      const mockAgents = {
        success: true,
        data: [
          {
            id: '1',
            name: '代码审查助手',
            description: '帮助审查代码质量',
            category: 'development',
            rating: 4.8,
            downloads: 1250
          }
        ]
      }
      
      mockFetch(mockAgents)
    })

    it('应该能够查看智能体详情', async () => {
      render(<Agents />)
      
      await waitFor(async () => {
        const agentCard = screen.getByText(/代码审查助手/i)
        await userEvent.click(agentCard)
      })
      
      await waitFor(() => {
        expect(screen.getByText(/智能体详情/i)).toBeInTheDocument()
        expect(screen.getByText(/帮助审查代码质量/i)).toBeInTheDocument()
      })
    })

    it('应该能够添加智能体到群聊', async () => {
      const mockAddResponse = { success: true }
      mockFetch(mockAddResponse)
      
      render(<Agents />)
      
      await waitFor(async () => {
        const addButton = screen.getByRole('button', { name: /添加到群聊/i })
        await userEvent.click(addButton)
      })
      
      await waitFor(() => {
        expect(screen.getByText(/选择群聊/i)).toBeInTheDocument()
      })
      
      // 选择群聊并确认
      await waitFor(async () => {
        const groupOption = screen.getByText(/技术讨论群/i)
        await userEvent.click(groupOption)
        
        const confirmButton = screen.getByRole('button', { name: /确认添加/i })
        await userEvent.click(confirmButton)
      })
      
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/groups/1/agents', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer mock-token'
          },
          body: JSON.stringify({
            agent_id: '1'
          })
        })
      })
    })

    it('应该能够收藏智能体', async () => {
      const mockFavoriteResponse = { success: true }
      mockFetch(mockFavoriteResponse)
      
      render(<Agents />)
      
      await waitFor(async () => {
        const favoriteButton = screen.getByRole('button', { name: /收藏/i })
        await userEvent.click(favoriteButton)
      })
      
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/agents/1/favorite', {
          method: 'POST',
          headers: {
            'Authorization': 'Bearer mock-token'
          }
        })
      })
      
      // 检查收藏状态变化
      await waitFor(() => {
        const favoriteButton = screen.getByRole('button', { name: /已收藏/i })
        expect(favoriteButton).toBeInTheDocument()
      })
    })

    it('应该能够评价智能体', async () => {
      const mockRatingResponse = { success: true }
      mockFetch(mockRatingResponse)
      
      render(<Agents />)
      
      await waitFor(async () => {
        const ratingButton = screen.getByRole('button', { name: /评价/i })
        await userEvent.click(ratingButton)
      })
      
      await waitFor(() => {
        expect(screen.getByText(/为智能体评分/i)).toBeInTheDocument()
      })
      
      // 选择评分
      await waitFor(async () => {
        const fiveStars = screen.getByLabelText(/5星/i)
        await userEvent.click(fiveStars)
        
        const commentInput = screen.getByPlaceholderText(/写下您的评价/i)
        await userEvent.type(commentInput, '非常好用的智能体！')
        
        const submitButton = screen.getByRole('button', { name: /提交评价/i })
        await userEvent.click(submitButton)
      })
      
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/agents/1/rating', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer mock-token'
          },
          body: JSON.stringify({
            rating: 5,
            comment: '非常好用的智能体！'
          })
        })
      })
    })
  })

  describe('上传智能体功能测试', () => {
    it('应该打开上传智能体对话框', async () => {
      render(<Agents />)
      
      const uploadButton = screen.getByRole('button', { name: /上传智能体/i })
      await userEvent.click(uploadButton)
      
      await waitFor(() => {
        expect(screen.getByText(/上传新智能体/i)).toBeInTheDocument()
        expect(screen.getByLabelText(/智能体名称/i)).toBeInTheDocument()
        expect(screen.getByLabelText(/描述/i)).toBeInTheDocument()
        expect(screen.getByLabelText(/分类/i)).toBeInTheDocument()
        expect(screen.getByLabelText(/系统提示词/i)).toBeInTheDocument()
      })
    })

    it('应该验证必填字段', async () => {
      render(<Agents />)
      
      const uploadButton = screen.getByRole('button', { name: /上传智能体/i })
      await userEvent.click(uploadButton)
      
      await waitFor(async () => {
        const submitButton = screen.getByRole('button', { name: /确认上传/i })
        await userEvent.click(submitButton)
        
        expect(screen.getByText(/请输入智能体名称/i)).toBeInTheDocument()
        expect(screen.getByText(/请输入描述/i)).toBeInTheDocument()
        expect(screen.getByText(/请选择分类/i)).toBeInTheDocument()
      })
    })

    it('应该成功上传智能体', async () => {
      const mockUploadResponse = {
        success: true,
        data: {
          id: '3',
          name: '新智能体',
          description: '这是一个新的智能体'
        }
      }
      
      mockFetch(mockUploadResponse)
      
      render(<Agents />)
      
      const uploadButton = screen.getByRole('button', { name: /上传智能体/i })
      await userEvent.click(uploadButton)
      
      await waitFor(async () => {
        const nameInput = screen.getByLabelText(/智能体名称/i)
        const descInput = screen.getByLabelText(/描述/i)
        const categorySelect = screen.getByLabelText(/分类/i)
        const promptInput = screen.getByLabelText(/系统提示词/i)
        
        await userEvent.type(nameInput, '新智能体')
        await userEvent.type(descInput, '这是一个新的智能体')
        await userEvent.selectOptions(categorySelect, 'development')
        await userEvent.type(promptInput, '你是一个有用的助手')
        
        const submitButton = screen.getByRole('button', { name: /确认上传/i })
        await userEvent.click(submitButton)
      })
      
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/agents', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer mock-token'
          },
          body: JSON.stringify({
            name: '新智能体',
            description: '这是一个新的智能体',
            category: 'development',
            system_prompt: '你是一个有用的助手',
            model: 'gpt-3.5-turbo',
            temperature: 0.7,
            max_tokens: 2000
          })
        })
      })
      
      await waitFor(() => {
        expect(screen.getByText(/智能体上传成功/i)).toBeInTheDocument()
      })
    })
  })

  describe('排序功能测试', () => {
    beforeEach(async () => {
      const mockAgents = {
        success: true,
        data: [
          {
            id: '1',
            name: '智能体A',
            rating: 4.8,
            downloads: 1250,
            created_at: '2024-01-01T10:00:00Z'
          },
          {
            id: '2',
            name: '智能体B',
            rating: 4.6,
            downloads: 890,
            created_at: '2024-01-02T10:00:00Z'
          }
        ]
      }
      
      mockFetch(mockAgents)
    })

    it('应该能够按评分排序', async () => {
      render(<Agents />)
      
      await waitFor(async () => {
        const sortSelect = screen.getByLabelText(/排序方式/i)
        await userEvent.selectOptions(sortSelect, 'rating')
      })
      
      // 检查排序结果
      const agentCards = screen.getAllByTestId('agent-card')
      expect(agentCards[0]).toHaveTextContent('智能体A')
      expect(agentCards[1]).toHaveTextContent('智能体B')
    })

    it('应该能够按下载量排序', async () => {
      render(<Agents />)
      
      await waitFor(async () => {
        const sortSelect = screen.getByLabelText(/排序方式/i)
        await userEvent.selectOptions(sortSelect, 'downloads')
      })
      
      const agentCards = screen.getAllByTestId('agent-card')
      expect(agentCards[0]).toHaveTextContent('智能体A')
      expect(agentCards[1]).toHaveTextContent('智能体B')
    })

    it('应该能够按时间排序', async () => {
      render(<Agents />)
      
      await waitFor(async () => {
        const sortSelect = screen.getByLabelText(/排序方式/i)
        await userEvent.selectOptions(sortSelect, 'newest')
      })
      
      const agentCards = screen.getAllByTestId('agent-card')
      expect(agentCards[0]).toHaveTextContent('智能体B')
      expect(agentCards[1]).toHaveTextContent('智能体A')
    })
  })

  describe('分页功能测试', () => {
    it('应该显示分页控件', async () => {
      const mockManyAgents = {
        success: true,
        data: Array.from({ length: 25 }, (_, i) => ({
          id: `${i + 1}`,
          name: `智能体${i + 1}`,
          description: `描述${i + 1}`,
          category: 'development'
        })),
        pagination: {
          current_page: 1,
          total_pages: 3,
          total_items: 25,
          per_page: 10
        }
      }
      
      mockFetch(mockManyAgents)
      
      render(<Agents />)
      
      await waitFor(() => {
        expect(screen.getByText(/第 1 页，共 3 页/i)).toBeInTheDocument()
        expect(screen.getByRole('button', { name: /下一页/i })).toBeInTheDocument()
      })
    })

    it('应该能够翻页', async () => {
      const mockPage2 = {
        success: true,
        data: Array.from({ length: 10 }, (_, i) => ({
          id: `${i + 11}`,
          name: `智能体${i + 11}`,
          description: `描述${i + 11}`,
          category: 'development'
        })),
        pagination: {
          current_page: 2,
          total_pages: 3,
          total_items: 25,
          per_page: 10
        }
      }
      
      mockFetch(mockPage2)
      
      render(<Agents />)
      
      await waitFor(async () => {
        const nextButton = screen.getByRole('button', { name: /下一页/i })
        await userEvent.click(nextButton)
      })
      
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(
          '/api/agents?page=2&per_page=10',
          expect.any(Object)
        )
      })
    })
  })

  describe('错误处理测试', () => {
    it('应该处理智能体列表加载失败', async () => {
      mockFetchError('Network error')
      
      render(<Agents />)
      
      await waitFor(() => {
        expect(screen.getByText(/加载智能体列表失败/i)).toBeInTheDocument()
      })
    })

    it('应该显示空状态', async () => {
      const mockEmptyAgents = {
        success: true,
        data: []
      }
      
      mockFetch(mockEmptyAgents)
      
      render(<Agents />)
      
      await waitFor(() => {
        expect(screen.getByText(/暂无智能体/i)).toBeInTheDocument()
        expect(screen.getByText(/上传您的第一个智能体/i)).toBeInTheDocument()
      })
    })
  })

  describe('响应式设计测试', () => {
    it('应该在移动设备上正确显示', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })
      
      render(<Agents />)
      
      // 检查移动端网格布局
      const agentGrid = screen.getByTestId('agents-grid')
      expect(agentGrid).toHaveClass('grid-cols-1', 'sm:grid-cols-2')
    })
  })

  describe('无障碍性测试', () => {
    it('应该有正确的ARIA标签', () => {
      render(<Agents />)
      
      const searchInput = screen.getByPlaceholderText(/搜索智能体/i)
      expect(searchInput).toHaveAttribute('aria-label', '搜索智能体')
      
      const categoryFilter = screen.getByLabelText(/分类筛选/i)
      expect(categoryFilter).toHaveAttribute('aria-label', '按分类筛选智能体')
    })
  })
})
