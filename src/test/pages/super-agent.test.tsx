import { describe, it, expect, beforeEach, vi } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { render, mockFetch, mockFetchError, testUtils, userEvent } from '../utils'
import SuperAgent from '../../pages/super-agent'

describe('SuperAgent Page - 超级智能体页面', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    localStorage.clear()
    
    // Mock user data
    localStorage.setItem('token', 'mock-token')
    localStorage.setItem('user', JSON.stringify({
      id: '1',
      username: 'admin',
      nickname: '管理员',
      role: 'admin'
    }))
  })

  describe('页面渲染测试', () => {
    it('应该正确渲染超级智能体页面的所有元素', () => {
      render(<SuperAgent />)
      
      // 检查页面标题
      expect(screen.getByText(/超级智能体/i)).toBeInTheDocument()
      
      // 检查聊天输入框
      expect(screen.getByPlaceholderText(/请输入您的问题/i)).toBeInTheDocument()
      
      // 检查发送按钮
      expect(screen.getByRole('button', { name: /发送/i })).toBeInTheDocument()
      
      // 检查设置按钮
      expect(screen.getByRole('button', { name: /设置/i })).toBeInTheDocument()
    })

    it('应该显示欢迎消息', () => {
      render(<SuperAgent />)
      
      expect(screen.getByText(/您好！我是您的超级智能体助手/i)).toBeInTheDocument()
    })

    it('应该显示功能介绍卡片', () => {
      render(<SuperAgent />)
      
      expect(screen.getByText(/任务规划/i)).toBeInTheDocument()
      expect(screen.getByText(/智能分析/i)).toBeInTheDocument()
      expect(screen.getByText(/多模态交互/i)).toBeInTheDocument()
    })
  })

  describe('聊天功能测试', () => {
    it('应该能够发送消息', async () => {
      const mockResponse = {
        success: true,
        data: {
          message: '这是AI的回复',
          conversation_id: 'conv-123'
        }
      }
      
      mockFetch(mockResponse)
      
      render(<SuperAgent />)
      
      const input = screen.getByPlaceholderText(/请输入您的问题/i)
      const sendButton = screen.getByRole('button', { name: /发送/i })
      
      await userEvent.type(input, '你好，请帮我分析一下数据')
      await userEvent.click(sendButton)
      
      // 检查用户消息是否显示
      expect(screen.getByText(/你好，请帮我分析一下数据/i)).toBeInTheDocument()
      
      // 检查API调用
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/super-agent/chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer mock-token'
          },
          body: JSON.stringify({
            message: '你好，请帮我分析一下数据',
            conversation_id: null
          })
        })
      })
      
      // 检查AI回复是否显示
      await waitFor(() => {
        expect(screen.getByText(/这是AI的回复/i)).toBeInTheDocument()
      })
    })

    it('应该支持Enter键发送消息', async () => {
      const mockResponse = {
        success: true,
        data: { message: 'AI回复', conversation_id: 'conv-123' }
      }
      
      mockFetch(mockResponse)
      
      render(<SuperAgent />)
      
      const input = screen.getByPlaceholderText(/请输入您的问题/i)
      
      await userEvent.type(input, '测试消息')
      await userEvent.keyboard('{Enter}')
      
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalled()
      })
    })

    it('应该在发送消息时显示加载状态', async () => {
      // 模拟延迟响应
      global.fetch = vi.fn().mockImplementation(() => 
        new Promise(resolve => 
          setTimeout(() => resolve({
            ok: true,
            json: () => Promise.resolve({
              success: true,
              data: { message: 'AI回复', conversation_id: 'conv-123' }
            })
          }), 1000)
        )
      )
      
      render(<SuperAgent />)
      
      const input = screen.getByPlaceholderText(/请输入您的问题/i)
      const sendButton = screen.getByRole('button', { name: /发送/i })
      
      await userEvent.type(input, '测试消息')
      await userEvent.click(sendButton)
      
      // 检查加载状态
      expect(screen.getByText(/发送中/i) || screen.getByRole('progressbar')).toBeInTheDocument()
      
      // 检查发送按钮是否被禁用
      expect(sendButton).toBeDisabled()
    })

    it('应该处理发送消息失败的情况', async () => {
      mockFetchError('Network error')
      
      render(<SuperAgent />)
      
      const input = screen.getByPlaceholderText(/请输入您的问题/i)
      const sendButton = screen.getByRole('button', { name: /发送/i })
      
      await userEvent.type(input, '测试消息')
      await userEvent.click(sendButton)
      
      await waitFor(() => {
        expect(screen.getByText(/发送失败，请重试/i)).toBeInTheDocument()
      })
    })

    it('应该清空输入框在发送成功后', async () => {
      const mockResponse = {
        success: true,
        data: { message: 'AI回复', conversation_id: 'conv-123' }
      }
      
      mockFetch(mockResponse)
      
      render(<SuperAgent />)
      
      const input = screen.getByPlaceholderText(/请输入您的问题/i) as HTMLInputElement
      
      await userEvent.type(input, '测试消息')
      expect(input.value).toBe('测试消息')
      
      const sendButton = screen.getByRole('button', { name: /发送/i })
      await userEvent.click(sendButton)
      
      await waitFor(() => {
        expect(input.value).toBe('')
      })
    })
  })

  describe('文件上传功能测试', () => {
    it('应该显示文件上传按钮', () => {
      render(<SuperAgent />)
      
      const uploadButton = screen.getByRole('button', { name: /上传文件/i })
      expect(uploadButton).toBeInTheDocument()
    })

    it('应该能够上传文件', async () => {
      const mockResponse = {
        success: true,
        data: { file_id: 'file-123', filename: 'test.pdf' }
      }
      
      mockFetch(mockResponse)
      
      render(<SuperAgent />)
      
      const uploadButton = screen.getByRole('button', { name: /上传文件/i })
      const fileInput = screen.getByLabelText(/选择文件/i)
      
      const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' })
      
      await userEvent.upload(fileInput, file)
      
      await waitFor(() => {
        expect(screen.getByText(/test.pdf/i)).toBeInTheDocument()
      })
    })

    it('应该限制文件大小', async () => {
      render(<SuperAgent />)
      
      const fileInput = screen.getByLabelText(/选择文件/i)
      
      // 创建一个超大文件（模拟）
      const largeFile = new File(['x'.repeat(10 * 1024 * 1024)], 'large.pdf', { 
        type: 'application/pdf' 
      })
      
      await userEvent.upload(fileInput, largeFile)
      
      await waitFor(() => {
        expect(screen.getByText(/文件大小不能超过/i)).toBeInTheDocument()
      })
    })

    it('应该限制文件类型', async () => {
      render(<SuperAgent />)
      
      const fileInput = screen.getByLabelText(/选择文件/i)
      
      const invalidFile = new File(['test'], 'test.exe', { type: 'application/exe' })
      
      await userEvent.upload(fileInput, invalidFile)
      
      await waitFor(() => {
        expect(screen.getByText(/不支持的文件类型/i)).toBeInTheDocument()
      })
    })
  })

  describe('MCP工具功能测试', () => {
    it('应该显示可用的MCP工具', () => {
      render(<SuperAgent />)
      
      // 检查内置工具
      expect(screen.getByText(/网页获取/i)).toBeInTheDocument()
      expect(screen.getByText(/序列思考/i)).toBeInTheDocument()
      expect(screen.getByText(/学术搜索/i)).toBeInTheDocument()
    })

    it('应该能够使用网页获取工具', async () => {
      const mockResponse = {
        success: true,
        data: {
          message: '已获取网页内容',
          tool_result: {
            tool: 'fetch',
            content: '网页内容...'
          }
        }
      }
      
      mockFetch(mockResponse)
      
      render(<SuperAgent />)
      
      const input = screen.getByPlaceholderText(/请输入您的问题/i)
      
      await userEvent.type(input, '请帮我获取 https://example.com 的内容')
      await userEvent.keyboard('{Enter}')
      
      await waitFor(() => {
        expect(screen.getByText(/已获取网页内容/i)).toBeInTheDocument()
      })
    })
  })

  describe('设置功能测试', () => {
    it('应该打开设置面板', async () => {
      render(<SuperAgent />)
      
      const settingsButton = screen.getByRole('button', { name: /设置/i })
      await userEvent.click(settingsButton)
      
      await waitFor(() => {
        expect(screen.getByText(/超级智能体设置/i)).toBeInTheDocument()
      })
    })

    it('应该能够调整AI模型参数', async () => {
      render(<SuperAgent />)
      
      const settingsButton = screen.getByRole('button', { name: /设置/i })
      await userEvent.click(settingsButton)
      
      await waitFor(() => {
        const temperatureSlider = screen.getByLabelText(/创造性/i)
        expect(temperatureSlider).toBeInTheDocument()
        
        const maxTokensInput = screen.getByLabelText(/最大回复长度/i)
        expect(maxTokensInput).toBeInTheDocument()
      })
    })

    it('应该保存设置到localStorage', async () => {
      render(<SuperAgent />)
      
      const settingsButton = screen.getByRole('button', { name: /设置/i })
      await userEvent.click(settingsButton)
      
      await waitFor(async () => {
        const temperatureSlider = screen.getByLabelText(/创造性/i)
        await userEvent.clear(temperatureSlider)
        await userEvent.type(temperatureSlider, '0.8')
        
        const saveButton = screen.getByRole('button', { name: /保存设置/i })
        await userEvent.click(saveButton)
      })
      
      expect(localStorage.setItem).toHaveBeenCalledWith(
        'super-agent-settings',
        expect.stringContaining('0.8')
      )
    })
  })

  describe('对话历史测试', () => {
    it('应该显示对话历史', async () => {
      const mockHistory = {
        success: true,
        data: {
          conversations: [
            {
              id: 'conv-1',
              title: '数据分析讨论',
              created_at: '2024-01-01T10:00:00Z',
              updated_at: '2024-01-01T10:30:00Z'
            }
          ]
        }
      }
      
      mockFetch(mockHistory)
      
      render(<SuperAgent />)
      
      const historyButton = screen.getByRole('button', { name: /历史记录/i })
      await userEvent.click(historyButton)
      
      await waitFor(() => {
        expect(screen.getByText(/数据分析讨论/i)).toBeInTheDocument()
      })
    })

    it('应该能够加载历史对话', async () => {
      const mockConversation = {
        success: true,
        data: {
          messages: [
            { role: 'user', content: '你好' },
            { role: 'assistant', content: '您好！有什么可以帮助您的吗？' }
          ]
        }
      }
      
      mockFetch(mockConversation)
      
      render(<SuperAgent />)
      
      // 模拟点击历史对话
      const historyButton = screen.getByRole('button', { name: /历史记录/i })
      await userEvent.click(historyButton)
      
      await waitFor(async () => {
        const conversationItem = screen.getByText(/数据分析讨论/i)
        await userEvent.click(conversationItem)
      })
      
      await waitFor(() => {
        expect(screen.getByText(/你好/i)).toBeInTheDocument()
        expect(screen.getByText(/您好！有什么可以帮助您的吗？/i)).toBeInTheDocument()
      })
    })

    it('应该能够删除对话历史', async () => {
      const mockResponse = { success: true }
      mockFetch(mockResponse)
      
      render(<SuperAgent />)
      
      const historyButton = screen.getByRole('button', { name: /历史记录/i })
      await userEvent.click(historyButton)
      
      await waitFor(async () => {
        const deleteButton = screen.getByRole('button', { name: /删除/i })
        await userEvent.click(deleteButton)
        
        // 确认删除
        const confirmButton = screen.getByRole('button', { name: /确认删除/i })
        await userEvent.click(confirmButton)
      })
      
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('/api/super-agent/conversations/'),
          expect.objectContaining({ method: 'DELETE' })
        )
      })
    })
  })

  describe('响应式设计测试', () => {
    it('应该在移动设备上正确显示', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })
      
      render(<SuperAgent />)
      
      // 检查移动端布局
      const chatContainer = screen.getByRole('main')
      expect(chatContainer).toHaveClass('mobile-layout')
    })

    it('应该在移动设备上隐藏侧边栏', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })
      
      render(<SuperAgent />)
      
      const sidebar = screen.queryByText(/历史记录/i)
      expect(sidebar).not.toBeVisible()
    })
  })

  describe('无障碍性测试', () => {
    it('应该有正确的ARIA标签', () => {
      render(<SuperAgent />)
      
      const chatInput = screen.getByPlaceholderText(/请输入您的问题/i)
      expect(chatInput).toHaveAttribute('aria-label', '聊天输入框')
      
      const sendButton = screen.getByRole('button', { name: /发送/i })
      expect(sendButton).toHaveAttribute('aria-label', '发送消息')
    })

    it('应该支持键盘导航', async () => {
      render(<SuperAgent />)
      
      const chatInput = screen.getByPlaceholderText(/请输入您的问题/i)
      const sendButton = screen.getByRole('button', { name: /发送/i })
      
      chatInput.focus()
      expect(chatInput).toHaveFocus()
      
      await userEvent.keyboard('{Tab}')
      expect(sendButton).toHaveFocus()
    })
  })

  describe('性能测试', () => {
    it('应该正确处理长对话历史', async () => {
      const longConversation = {
        success: true,
        data: {
          messages: Array.from({ length: 100 }, (_, i) => ({
            role: i % 2 === 0 ? 'user' : 'assistant',
            content: `消息 ${i + 1}`,
            timestamp: new Date().toISOString()
          }))
        }
      }
      
      mockFetch(longConversation)
      
      render(<SuperAgent />)
      
      // 应该能够渲染大量消息而不卡顿
      await waitFor(() => {
        expect(screen.getByText(/消息 1/i)).toBeInTheDocument()
        expect(screen.getByText(/消息 100/i)).toBeInTheDocument()
      })
    })
  })
})
