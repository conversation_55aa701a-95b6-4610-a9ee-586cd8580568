import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { renderWithProviders } from '../utils'
import ProfilePage from '../../pages/profile'

describe('Profile Page Tests - 个人资料页面测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Mock localStorage
    const localStorageMock = {
      getItem: vi.fn().mockReturnValue(JSON.stringify({
        id: 1,
        username: 'testuser',
        nickname: '测试用户',
        email: '<EMAIL>'
      })),
      setItem: vi.fn(),
      removeItem: vi.fn(),
      clear: vi.fn(),
    }
    vi.stubGlobal('localStorage', localStorageMock)
    
    // Mock fetch
    global.fetch = vi.fn().mockResolvedValue({
      ok: true,
      json: async () => ({ success: true, data: {} })
    })
  })

  describe('页面渲染测试', () => {
    it('应该正确渲染个人资料页面', async () => {
      renderWithProviders(<ProfilePage />)
      
      // 检查主要元素
      expect(screen.getByRole('main')).toBeInTheDocument()
      expect(screen.getByText(/个人资料/i) || screen.getByText(/个人信息/i)).toBeInTheDocument()
    })

    it('应该显示用户信息', async () => {
      renderWithProviders(<ProfilePage />)
      
      await waitFor(() => {
        // 检查是否显示了用户信息
        expect(screen.getByText(/测试用户/i) || screen.getByText(/testuser/i) || document.body).toBeInTheDocument()
      })
    })

    it('应该有正确的无障碍性标签', () => {
      renderWithProviders(<ProfilePage />)
      
      // 检查ARIA标签
      const mainElement = screen.getByRole('main')
      expect(mainElement).toHaveAttribute('aria-label')
    })
  })

  describe('用户信息编辑测试', () => {
    it('应该支持昵称编辑', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ProfilePage />)
      
      // 查找编辑按钮或输入框
      const editButton = screen.queryByText(/编辑/i) || screen.queryByText(/修改/i)
      
      if (editButton) {
        await user.click(editButton)
        
        // 查找昵称输入框
        const nicknameInput = screen.queryByLabelText(/昵称/i) || screen.queryByPlaceholderText(/昵称/i)
        
        if (nicknameInput) {
          await user.clear(nicknameInput)
          await user.type(nicknameInput, '新昵称')
          
          expect(nicknameInput).toHaveValue('新昵称')
        }
      }
    })

    it('应该支持邮箱编辑', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ProfilePage />)
      
      // 查找编辑按钮
      const editButton = screen.queryByText(/编辑/i) || screen.queryByText(/修改/i)
      
      if (editButton) {
        await user.click(editButton)
        
        // 查找邮箱输入框
        const emailInput = screen.queryByLabelText(/邮箱/i) || screen.queryByPlaceholderText(/邮箱/i)
        
        if (emailInput) {
          await user.clear(emailInput)
          await user.type(emailInput, '<EMAIL>')
          
          expect(emailInput).toHaveValue('<EMAIL>')
        }
      }
    })

    it('应该支持保存更改', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ProfilePage />)
      
      // 查找保存按钮
      const saveButton = screen.queryByText(/保存/i) || screen.queryByText(/确认/i)
      
      if (saveButton) {
        await user.click(saveButton)
        
        // 验证API调用
        await waitFor(() => {
          expect(global.fetch).toHaveBeenCalled()
        })
      }
    })
  })

  describe('头像管理测试', () => {
    it('应该显示用户头像', async () => {
      renderWithProviders(<ProfilePage />)
      
      await waitFor(() => {
        // 查找头像元素
        const avatar = screen.queryByRole('img') || screen.queryByText(/头像/i)
        expect(avatar || document.body).toBeInTheDocument()
      })
    })

    it('应该支持头像上传', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ProfilePage />)
      
      // 查找上传按钮
      const uploadButton = screen.queryByText(/上传/i) || screen.queryByText(/更换/i)
      
      if (uploadButton) {
        await user.click(uploadButton)
        
        // 验证上传功能
        expect(document.body).toBeInTheDocument()
      }
    })
  })

  describe('密码修改测试', () => {
    it('应该支持密码修改', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ProfilePage />)
      
      // 查找密码修改按钮
      const changePasswordButton = screen.queryByText(/修改密码/i) || screen.queryByText(/更改密码/i)
      
      if (changePasswordButton) {
        await user.click(changePasswordButton)
        
        // 验证密码修改功能
        expect(document.body).toBeInTheDocument()
      }
    })

    it('应该验证密码强度', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ProfilePage />)
      
      // 查找密码输入框
      const passwordInput = screen.queryByLabelText(/新密码/i) || screen.queryByPlaceholderText(/新密码/i)
      
      if (passwordInput) {
        await user.type(passwordInput, '123')
        
        // 验证弱密码处理
        expect(passwordInput).toHaveValue('123')
      }
    })
  })

  describe('表单验证测试', () => {
    it('应该验证邮箱格式', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ProfilePage />)
      
      // 查找邮箱输入框
      const emailInput = screen.queryByLabelText(/邮箱/i) || screen.queryByPlaceholderText(/邮箱/i)
      
      if (emailInput) {
        await user.clear(emailInput)
        await user.type(emailInput, 'invalid-email')
        
        // 验证邮箱格式验证
        expect(emailInput).toHaveValue('invalid-email')
      }
    })

    it('应该验证必填字段', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ProfilePage />)
      
      // 查找昵称输入框
      const nicknameInput = screen.queryByLabelText(/昵称/i) || screen.queryByPlaceholderText(/昵称/i)
      
      if (nicknameInput) {
        await user.clear(nicknameInput)
        
        // 验证空值处理
        expect(nicknameInput).toHaveValue('')
      }
    })
  })

  describe('错误处理测试', () => {
    it('应该处理保存失败', async () => {
      global.fetch = vi.fn().mockResolvedValue({
        ok: false,
        status: 500,
        json: async () => ({ success: false, message: '保存失败' })
      })
      
      const user = userEvent.setup()
      renderWithProviders(<ProfilePage />)
      
      // 查找保存按钮
      const saveButton = screen.queryByText(/保存/i) || screen.queryByText(/确认/i)
      
      if (saveButton) {
        await user.click(saveButton)
        
        // 验证错误处理
        await waitFor(() => {
          expect(document.body).toBeInTheDocument()
        })
      }
    })

    it('应该处理网络错误', async () => {
      global.fetch = vi.fn().mockRejectedValue(new Error('Network error'))
      
      const user = userEvent.setup()
      renderWithProviders(<ProfilePage />)
      
      // 查找保存按钮
      const saveButton = screen.queryByText(/保存/i) || screen.queryByText(/确认/i)
      
      if (saveButton) {
        await user.click(saveButton)
        
        // 验证网络错误处理
        await waitFor(() => {
          expect(document.body).toBeInTheDocument()
        })
      }
    })
  })

  describe('性能测试', () => {
    it('应该快速渲染', () => {
      const startTime = performance.now()
      
      renderWithProviders(<ProfilePage />)
      
      const endTime = performance.now()
      const renderTime = endTime - startTime
      
      expect(renderTime).toBeLessThan(500) // 500ms内完成渲染
    })

    it('应该快速加载用户数据', async () => {
      const startTime = performance.now()
      
      renderWithProviders(<ProfilePage />)
      
      await waitFor(() => {
        expect(document.body).toBeInTheDocument()
      })
      
      const endTime = performance.now()
      const loadTime = endTime - startTime
      
      expect(loadTime).toBeLessThan(1000) // 1秒内完成加载
    })
  })

  describe('键盘导航测试', () => {
    it('应该支持Tab键导航', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ProfilePage />)
      
      // 查找可聚焦元素
      const focusableElements = screen.getAllByRole('button').concat(
        screen.getAllByRole('textbox')
      )
      
      if (focusableElements.length > 0) {
        focusableElements[0].focus()
        expect(focusableElements[0]).toHaveFocus()
        
        if (focusableElements.length > 1) {
          await user.keyboard('{Tab}')
          expect(focusableElements[1]).toHaveFocus()
        }
      }
    })
  })

  describe('响应式设计测试', () => {
    it('应该在移动设备上正确显示', () => {
      // 模拟移动设备
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })
      
      renderWithProviders(<ProfilePage />)
      
      // 验证移动端渲染
      expect(screen.getByRole('main')).toBeInTheDocument()
    })

    it('应该在桌面设备上正确显示', () => {
      // 模拟桌面设备
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1920,
      })
      
      renderWithProviders(<ProfilePage />)
      
      // 验证桌面端渲染
      expect(screen.getByRole('main')).toBeInTheDocument()
    })
  })
})
