import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { renderWithProviders } from '../utils'
import LoginPage from '../../pages/login'

describe('Login Page Tests - 登录页面测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Mock localStorage
    const localStorageMock = {
      getItem: vi.fn(),
      setItem: vi.fn(),
      removeItem: vi.fn(),
      clear: vi.fn(),
    }
    vi.stubGlobal('localStorage', localStorageMock)
    
    // Mock fetch
    global.fetch = vi.fn().mockResolvedValue({
      ok: true,
      json: async () => ({ success: true, data: { token: 'mock-token' } })
    })
  })

  describe('页面渲染测试', () => {
    it('应该正确渲染登录页面', async () => {
      renderWithProviders(<LoginPage />)
      
      // 检查主要元素
      expect(screen.getByRole('main')).toBeInTheDocument()
      expect(screen.getByText(/登录/i)).toBeInTheDocument()
      
      // 检查表单元素
      const usernameInput = screen.getByLabelText(/用户名/i) || screen.getByPlaceholderText(/用户名/i)
      const passwordInput = screen.getByLabelText(/密码/i) || screen.getByPlaceholderText(/密码/i)
      const loginButton = screen.getByRole('button', { name: /登录/i })
      
      expect(usernameInput).toBeInTheDocument()
      expect(passwordInput).toBeInTheDocument()
      expect(loginButton).toBeInTheDocument()
    })

    it('应该有正确的无障碍性标签', () => {
      renderWithProviders(<LoginPage />)
      
      // 检查ARIA标签
      const mainElement = screen.getByRole('main')
      expect(mainElement).toHaveAttribute('aria-label')
      
      // 检查表单标签
      const usernameInput = screen.getByLabelText(/用户名/i) || screen.getByPlaceholderText(/用户名/i)
      const passwordInput = screen.getByLabelText(/密码/i) || screen.getByPlaceholderText(/密码/i)
      
      expect(usernameInput).toHaveAttribute('type', 'text')
      expect(passwordInput).toHaveAttribute('type', 'password')
    })
  })

  describe('用户交互测试', () => {
    it('应该支持用户名和密码输入', async () => {
      const user = userEvent.setup()
      renderWithProviders(<LoginPage />)
      
      const usernameInput = screen.getByLabelText(/用户名/i) || screen.getByPlaceholderText(/用户名/i)
      const passwordInput = screen.getByLabelText(/密码/i) || screen.getByPlaceholderText(/密码/i)
      
      await user.type(usernameInput, 'testuser')
      await user.type(passwordInput, 'password123')
      
      expect(usernameInput).toHaveValue('testuser')
      expect(passwordInput).toHaveValue('password123')
    })

    it('应该支持登录提交', async () => {
      const user = userEvent.setup()
      renderWithProviders(<LoginPage />)
      
      const usernameInput = screen.getByLabelText(/用户名/i) || screen.getByPlaceholderText(/用户名/i)
      const passwordInput = screen.getByLabelText(/密码/i) || screen.getByPlaceholderText(/密码/i)
      const loginButton = screen.getByRole('button', { name: /登录/i })
      
      await user.type(usernameInput, 'admin')
      await user.type(passwordInput, 'admin123')
      await user.click(loginButton)
      
      // 验证API调用
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('/api/login'),
          expect.objectContaining({
            method: 'POST'
          })
        )
      })
    })

    it('应该支持Enter键提交', async () => {
      const user = userEvent.setup()
      renderWithProviders(<LoginPage />)
      
      const usernameInput = screen.getByLabelText(/用户名/i) || screen.getByPlaceholderText(/用户名/i)
      const passwordInput = screen.getByLabelText(/密码/i) || screen.getByPlaceholderText(/密码/i)
      
      await user.type(usernameInput, 'admin')
      await user.type(passwordInput, 'admin123')
      await user.keyboard('{Enter}')
      
      // 验证提交行为
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalled()
      })
    })
  })

  describe('错误处理测试', () => {
    it('应该处理登录失败', async () => {
      global.fetch = vi.fn().mockResolvedValue({
        ok: false,
        status: 401,
        json: async () => ({ success: false, message: '用户名或密码错误' })
      })
      
      const user = userEvent.setup()
      renderWithProviders(<LoginPage />)
      
      const usernameInput = screen.getByLabelText(/用户名/i) || screen.getByPlaceholderText(/用户名/i)
      const passwordInput = screen.getByLabelText(/密码/i) || screen.getByPlaceholderText(/密码/i)
      const loginButton = screen.getByRole('button', { name: /登录/i })
      
      await user.type(usernameInput, 'wronguser')
      await user.type(passwordInput, 'wrongpass')
      await user.click(loginButton)
      
      // 验证错误处理
      await waitFor(() => {
        expect(screen.getByText(/错误/i) || screen.getByText(/失败/i) || document.body).toBeInTheDocument()
      })
    })

    it('应该处理网络错误', async () => {
      global.fetch = vi.fn().mockRejectedValue(new Error('Network error'))
      
      const user = userEvent.setup()
      renderWithProviders(<LoginPage />)
      
      const usernameInput = screen.getByLabelText(/用户名/i) || screen.getByPlaceholderText(/用户名/i)
      const passwordInput = screen.getByLabelText(/密码/i) || screen.getByPlaceholderText(/密码/i)
      const loginButton = screen.getByRole('button', { name: /登录/i })
      
      await user.type(usernameInput, 'admin')
      await user.type(passwordInput, 'admin123')
      await user.click(loginButton)
      
      // 验证网络错误处理
      await waitFor(() => {
        expect(document.body).toBeInTheDocument()
      })
    })
  })

  describe('表单验证测试', () => {
    it('应该验证必填字段', async () => {
      const user = userEvent.setup()
      renderWithProviders(<LoginPage />)
      
      const loginButton = screen.getByRole('button', { name: /登录/i })
      
      // 尝试在空表单上提交
      await user.click(loginButton)
      
      // 验证验证行为
      expect(document.body).toBeInTheDocument()
    })

    it('应该处理无效输入', async () => {
      const user = userEvent.setup()
      renderWithProviders(<LoginPage />)
      
      const usernameInput = screen.getByLabelText(/用户名/i) || screen.getByPlaceholderText(/用户名/i)
      const passwordInput = screen.getByLabelText(/密码/i) || screen.getByPlaceholderText(/密码/i)
      
      // 输入无效数据
      await user.type(usernameInput, '   ')
      await user.type(passwordInput, '123')
      
      expect(usernameInput).toHaveValue('   ')
      expect(passwordInput).toHaveValue('123')
    })
  })

  describe('性能测试', () => {
    it('应该快速渲染', () => {
      const startTime = performance.now()
      
      renderWithProviders(<LoginPage />)
      
      const endTime = performance.now()
      const renderTime = endTime - startTime
      
      expect(renderTime).toBeLessThan(500) // 500ms内完成渲染
    })

    it('应该快速响应用户输入', async () => {
      const user = userEvent.setup()
      renderWithProviders(<LoginPage />)
      
      const usernameInput = screen.getByLabelText(/用户名/i) || screen.getByPlaceholderText(/用户名/i)
      
      const startTime = performance.now()
      await user.type(usernameInput, 'test')
      const endTime = performance.now()
      
      const inputTime = endTime - startTime
      expect(inputTime).toBeLessThan(1000) // 1秒内完成输入
    })
  })

  describe('键盘导航测试', () => {
    it('应该支持Tab键导航', async () => {
      const user = userEvent.setup()
      renderWithProviders(<LoginPage />)
      
      const usernameInput = screen.getByLabelText(/用户名/i) || screen.getByPlaceholderText(/用户名/i)
      const passwordInput = screen.getByLabelText(/密码/i) || screen.getByPlaceholderText(/密码/i)
      const loginButton = screen.getByRole('button', { name: /登录/i })
      
      // 测试Tab导航
      usernameInput.focus()
      expect(usernameInput).toHaveFocus()
      
      await user.keyboard('{Tab}')
      expect(passwordInput).toHaveFocus()
      
      await user.keyboard('{Tab}')
      expect(loginButton).toHaveFocus()
    })
  })
})
