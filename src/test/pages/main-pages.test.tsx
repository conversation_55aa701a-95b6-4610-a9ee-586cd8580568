import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { MemoryRouter } from 'react-router-dom'
import { renderWithProviders } from '../utils'

// Import all main pages
import AgentsPage from '@/pages/agents'
import ChatPage from '@/pages/chat'
import GroupChatPage from '@/pages/group-chat'
import ProfilePage from '@/pages/profile'
import SettingsPage from '@/pages/settings'
import SuperAgentPage from '@/pages/super-agent'
import UserManagementPage from '@/pages/user-management'

describe('Main Pages Integration Tests', () => {
  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks()
    
    // Mock localStorage
    const localStorageMock = {
      getItem: vi.fn(),
      setItem: vi.fn(),
      removeItem: vi.fn(),
      clear: vi.fn(),
    }
    vi.stubGlobal('localStorage', localStorageMock)
    
    // Mock fetch
    global.fetch = vi.fn()
  })

  describe('Agents Page - 智能体商店', () => {
    it('应该正确渲染智能体商店页面', async () => {
      renderWithProviders(<AgentsPage />)
      
      // 检查页面标题
      expect(screen.getByText(/智能体商店/i) || screen.getByText(/Agent Store/i)).toBeInTheDocument()
      
      // 检查搜索功能
      const searchInput = screen.getByPlaceholderText(/搜索智能体/i) || screen.getByRole('searchbox')
      expect(searchInput).toBeInTheDocument()
      
      // 检查智能体列表容器
      const agentList = screen.getByTestId('agent-list') || screen.getByRole('list')
      expect(agentList).toBeInTheDocument()
    })

    it('应该支持智能体搜索功能', async () => {
      const user = userEvent.setup()
      renderWithProviders(<AgentsPage />)
      
      const searchInput = screen.getByPlaceholderText(/搜索智能体/i) || screen.getByRole('searchbox')
      
      await user.type(searchInput, '助手')
      expect(searchInput).toHaveValue('助手')
    })

    it('应该支持智能体分类筛选', async () => {
      renderWithProviders(<AgentsPage />)
      
      // 检查分类按钮
      const categoryButtons = screen.getAllByRole('button').filter(btn => 
        btn.textContent?.includes('分类') || btn.textContent?.includes('Category')
      )
      expect(categoryButtons.length).toBeGreaterThan(0)
    })
  })

  describe('Chat Page - 聊天页面', () => {
    it('应该正确渲染聊天页面', async () => {
      renderWithProviders(<ChatPage />)
      
      // 检查聊天界面元素
      const chatContainer = screen.getByTestId('chat-container') || screen.getByRole('main')
      expect(chatContainer).toBeInTheDocument()
      
      // 检查消息输入框
      const messageInput = screen.getByPlaceholderText(/输入消息/i) || screen.getByRole('textbox')
      expect(messageInput).toBeInTheDocument()
      
      // 检查发送按钮
      const sendButton = screen.getByRole('button', { name: /发送/i }) || screen.getByText(/发送/i)
      expect(sendButton).toBeInTheDocument()
    })

    it('应该支持消息发送功能', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ChatPage />)
      
      const messageInput = screen.getByPlaceholderText(/输入消息/i) || screen.getByRole('textbox')
      const sendButton = screen.getByRole('button', { name: /发送/i }) || screen.getByText(/发送/i)
      
      await user.type(messageInput, '你好')
      await user.click(sendButton)
      
      // 检查消息是否被发送
      expect(messageInput).toHaveValue('')
    })
  })

  describe('Group Chat Page - 群聊页面', () => {
    it('应该正确渲染群聊页面', async () => {
      renderWithProviders(<GroupChatPage />)
      
      // 检查群聊列表
      const groupList = screen.getByTestId('group-list') || screen.getByRole('list')
      expect(groupList).toBeInTheDocument()
      
      // 检查创建群聊按钮
      const createButton = screen.getByRole('button', { name: /创建群聊/i }) || screen.getByText(/创建群聊/i)
      expect(createButton).toBeInTheDocument()
    })

    it('应该支持创建新群聊', async () => {
      const user = userEvent.setup()
      renderWithProviders(<GroupChatPage />)
      
      const createButton = screen.getByRole('button', { name: /创建群聊/i }) || screen.getByText(/创建群聊/i)
      await user.click(createButton)
      
      // 检查是否打开了创建群聊对话框
      await waitFor(() => {
        expect(screen.getByText(/群聊名称/i) || screen.getByText(/Group Name/i)).toBeInTheDocument()
      })
    })
  })

  describe('Profile Page - 个人资料页面', () => {
    it('应该正确渲染个人资料页面', async () => {
      renderWithProviders(<ProfilePage />)
      
      // 检查个人信息表单
      const profileForm = screen.getByTestId('profile-form') || screen.getByRole('form')
      expect(profileForm).toBeInTheDocument()
      
      // 检查用户名输入框
      const usernameInput = screen.getByLabelText(/用户名/i) || screen.getByDisplayValue(/admin/i)
      expect(usernameInput).toBeInTheDocument()
    })

    it('应该支持个人信息编辑', async () => {
      const user = userEvent.setup()
      renderWithProviders(<ProfilePage />)
      
      const editButton = screen.getByRole('button', { name: /编辑/i }) || screen.getByText(/编辑/i)
      await user.click(editButton)
      
      // 检查是否进入编辑模式
      const saveButton = screen.getByRole('button', { name: /保存/i }) || screen.getByText(/保存/i)
      expect(saveButton).toBeInTheDocument()
    })
  })

  describe('Settings Page - 系统设置页面', () => {
    it('应该正确渲染系统设置页面', async () => {
      renderWithProviders(<SettingsPage />)
      
      // 检查设置选项
      const settingsContainer = screen.getByTestId('settings-container') || screen.getByRole('main')
      expect(settingsContainer).toBeInTheDocument()
      
      // 检查主题设置
      const themeToggle = screen.getByLabelText(/主题/i) || screen.getByText(/主题/i)
      expect(themeToggle).toBeInTheDocument()
    })

    it('应该支持主题切换', async () => {
      const user = userEvent.setup()
      renderWithProviders(<SettingsPage />)
      
      const themeToggle = screen.getByRole('button', { name: /主题/i }) || screen.getByText(/主题/i)
      await user.click(themeToggle)
      
      // 检查主题是否切换
      await waitFor(() => {
        expect(document.documentElement.className).toMatch(/light|dark/)
      })
    })
  })

  describe('Super Agent Page - 超级智能体页面', () => {
    it('应该正确渲染超级智能体页面', async () => {
      renderWithProviders(<SuperAgentPage />)
      
      // 检查超级智能体界面
      const superAgentContainer = screen.getByTestId('super-agent-container') || screen.getByRole('main')
      expect(superAgentContainer).toBeInTheDocument()
      
      // 检查任务规划功能
      const taskPlanning = screen.getByText(/任务规划/i) || screen.getByText(/Task Planning/i)
      expect(taskPlanning).toBeInTheDocument()
    })
  })

  describe('User Management Page - 用户管理页面', () => {
    it('应该正确渲染用户管理页面', async () => {
      renderWithProviders(<UserManagementPage />)
      
      // 检查用户列表
      const userList = screen.getByTestId('user-list') || screen.getByRole('table')
      expect(userList).toBeInTheDocument()
      
      // 检查添加用户按钮
      const addUserButton = screen.getByRole('button', { name: /添加用户/i }) || screen.getByText(/添加用户/i)
      expect(addUserButton).toBeInTheDocument()
    })

    it('应该支持用户搜索功能', async () => {
      const user = userEvent.setup()
      renderWithProviders(<UserManagementPage />)
      
      const searchInput = screen.getByPlaceholderText(/搜索用户/i) || screen.getByRole('searchbox')
      
      await user.type(searchInput, 'admin')
      expect(searchInput).toHaveValue('admin')
    })
  })
})
