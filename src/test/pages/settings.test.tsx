import { describe, it, expect, beforeEach, vi } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { render, mockFetch, mockFetchError, testUtils, userEvent } from '../utils'
import Settings from '../../pages/settings'

describe('Settings Page - 系统设置页面', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    localStorage.clear()
    
    // Mock user data
    localStorage.setItem('token', 'mock-token')
    localStorage.setItem('user', JSON.stringify({
      id: '1',
      username: 'admin',
      nickname: '管理员',
      role: 'admin'
    }))
  })

  describe('页面渲染测试', () => {
    it('应该正确渲染系统设置页面的所有元素', () => {
      render(<Settings />)
      
      // 检查页面标题
      expect(screen.getByText(/系统设置/i)).toBeInTheDocument()
      
      // 检查设置分类
      expect(screen.getByText(/基础设置/i)).toBeInTheDocument()
      expect(screen.getByText(/AI配置/i)).toBeInTheDocument()
      expect(screen.getByText(/钱包管理/i)).toBeInTheDocument()
      expect(screen.getByText(/用户管理/i)).toBeInTheDocument()
      expect(screen.getByText(/关于/i)).toBeInTheDocument()
    })

    it('应该默认显示基础设置选项卡', () => {
      render(<Settings />)
      
      expect(screen.getByText(/主题设置/i)).toBeInTheDocument()
      expect(screen.getByText(/语言设置/i)).toBeInTheDocument()
      expect(screen.getByText(/通知设置/i)).toBeInTheDocument()
    })

    it('应该根据用户角色显示相应的设置选项', () => {
      render(<Settings />)
      
      // 管理员应该看到所有设置选项
      expect(screen.getByText(/AI配置/i)).toBeInTheDocument()
      expect(screen.getByText(/用户管理/i)).toBeInTheDocument()
    })

    it('应该为普通用户隐藏管理员设置', () => {
      localStorage.setItem('user', JSON.stringify({
        id: '2',
        username: 'user',
        nickname: '普通用户',
        role: 'user'
      }))
      
      render(<Settings />)
      
      // 普通用户不应该看到管理员设置
      expect(screen.queryByText(/AI配置/i)).not.toBeInTheDocument()
      expect(screen.queryByText(/用户管理/i)).not.toBeInTheDocument()
    })
  })

  describe('基础设置功能测试', () => {
    it('应该能够切换主题', async () => {
      render(<Settings />)
      
      const themeToggle = screen.getByLabelText(/深色模式/i)
      await userEvent.click(themeToggle)
      
      // 检查主题是否切换
      await waitFor(() => {
        expect(document.documentElement).toHaveClass('dark')
      })
      
      // 检查设置是否保存到localStorage
      expect(localStorage.setItem).toHaveBeenCalledWith('theme', 'dark')
    })

    it('应该能够更改语言设置', async () => {
      render(<Settings />)
      
      const languageSelect = screen.getByLabelText(/语言/i)
      await userEvent.selectOptions(languageSelect, 'en')
      
      // 检查设置是否保存
      expect(localStorage.setItem).toHaveBeenCalledWith('language', 'en')
    })

    it('应该能够配置通知设置', async () => {
      render(<Settings />)
      
      const emailNotifications = screen.getByLabelText(/邮件通知/i)
      const pushNotifications = screen.getByLabelText(/推送通知/i)
      
      await userEvent.click(emailNotifications)
      await userEvent.click(pushNotifications)
      
      // 检查设置是否保存
      await waitFor(() => {
        expect(localStorage.setItem).toHaveBeenCalledWith(
          'notifications',
          expect.stringContaining('email')
        )
      })
    })
  })

  describe('AI配置功能测试', () => {
    beforeEach(() => {
      // 确保是管理员用户
      localStorage.setItem('user', JSON.stringify({
        id: '1',
        username: 'admin',
        role: 'admin'
      }))
    })

    it('应该显示AI配置选项卡', async () => {
      render(<Settings />)
      
      const aiConfigTab = screen.getByText(/AI配置/i)
      await userEvent.click(aiConfigTab)
      
      await waitFor(() => {
        expect(screen.getByText(/API密钥配置/i)).toBeInTheDocument()
        expect(screen.getByText(/模型设置/i)).toBeInTheDocument()
        expect(screen.getByText(/默认参数/i)).toBeInTheDocument()
      })
    })

    it('应该能够配置API密钥', async () => {
      const mockSaveResponse = { success: true }
      mockFetch(mockSaveResponse)
      
      render(<Settings />)
      
      const aiConfigTab = screen.getByText(/AI配置/i)
      await userEvent.click(aiConfigTab)
      
      await waitFor(async () => {
        const apiKeyInput = screen.getByLabelText(/OpenAI API Key/i)
        await userEvent.clear(apiKeyInput)
        await userEvent.type(apiKeyInput, 'sk-test-key-123')
        
        const saveButton = screen.getByRole('button', { name: /保存配置/i })
        await userEvent.click(saveButton)
      })
      
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/admin/ai-config', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer mock-token'
          },
          body: expect.stringContaining('sk-test-key-123')
        })
      })
    })

    it('应该能够配置默认模型参数', async () => {
      const mockSaveResponse = { success: true }
      mockFetch(mockSaveResponse)
      
      render(<Settings />)
      
      const aiConfigTab = screen.getByText(/AI配置/i)
      await userEvent.click(aiConfigTab)
      
      await waitFor(async () => {
        const temperatureSlider = screen.getByLabelText(/温度/i)
        const maxTokensInput = screen.getByLabelText(/最大令牌数/i)
        
        await userEvent.clear(temperatureSlider)
        await userEvent.type(temperatureSlider, '0.8')
        
        await userEvent.clear(maxTokensInput)
        await userEvent.type(maxTokensInput, '4000')
        
        const saveButton = screen.getByRole('button', { name: /保存配置/i })
        await userEvent.click(saveButton)
      })
      
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/admin/ai-config', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer mock-token'
          },
          body: expect.stringContaining('0.8')
        })
      })
    })

    it('应该验证API密钥格式', async () => {
      render(<Settings />)
      
      const aiConfigTab = screen.getByText(/AI配置/i)
      await userEvent.click(aiConfigTab)
      
      await waitFor(async () => {
        const apiKeyInput = screen.getByLabelText(/OpenAI API Key/i)
        await userEvent.clear(apiKeyInput)
        await userEvent.type(apiKeyInput, 'invalid-key')
        
        const saveButton = screen.getByRole('button', { name: /保存配置/i })
        await userEvent.click(saveButton)
      })
      
      await waitFor(() => {
        expect(screen.getByText(/API密钥格式不正确/i)).toBeInTheDocument()
      })
    })
  })

  describe('钱包管理功能测试', () => {
    it('应该显示钱包信息', async () => {
      const mockWalletInfo = {
        success: true,
        data: {
          balance: 10000,
          currency: 'credits',
          validity_period: '2024-12-31',
          transactions: []
        }
      }
      
      mockFetch(mockWalletInfo)
      
      render(<Settings />)
      
      const walletTab = screen.getByText(/钱包管理/i)
      await userEvent.click(walletTab)
      
      await waitFor(() => {
        expect(screen.getByText(/当前余额/i)).toBeInTheDocument()
        expect(screen.getByText(/10000/)).toBeInTheDocument()
        expect(screen.getByText(/积分/i)).toBeInTheDocument()
        expect(screen.getByText(/有效期至/i)).toBeInTheDocument()
      })
    })

    it('应该能够充值', async () => {
      const mockRechargeResponse = { success: true }
      mockFetch(mockRechargeResponse)
      
      render(<Settings />)
      
      const walletTab = screen.getByText(/钱包管理/i)
      await userEvent.click(walletTab)
      
      await waitFor(async () => {
        const rechargeButton = screen.getByRole('button', { name: /充值/i })
        await userEvent.click(rechargeButton)
        
        const rechargeKeyInput = screen.getByLabelText(/充值密钥/i)
        await userEvent.type(rechargeKeyInput, 'RECHARGE-KEY-123')
        
        const confirmButton = screen.getByRole('button', { name: /确认充值/i })
        await userEvent.click(confirmButton)
      })
      
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/wallet/recharge', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer mock-token'
          },
          body: JSON.stringify({
            recharge_key: 'RECHARGE-KEY-123'
          })
        })
      })
    })

    it('应该显示交易历史', async () => {
      const mockTransactions = {
        success: true,
        data: {
          transactions: [
            {
              id: '1',
              type: 'recharge',
              amount: 1000,
              description: '充值',
              created_at: '2024-01-01T10:00:00Z'
            },
            {
              id: '2',
              type: 'consume',
              amount: -50,
              description: 'AI对话消费',
              created_at: '2024-01-01T11:00:00Z'
            }
          ]
        }
      }
      
      mockFetch(mockTransactions)
      
      render(<Settings />)
      
      const walletTab = screen.getByText(/钱包管理/i)
      await userEvent.click(walletTab)
      
      await waitFor(() => {
        expect(screen.getByText(/交易历史/i)).toBeInTheDocument()
        expect(screen.getByText(/充值/i)).toBeInTheDocument()
        expect(screen.getByText(/AI对话消费/i)).toBeInTheDocument()
        expect(screen.getByText(/+1000/)).toBeInTheDocument()
        expect(screen.getByText(/-50/)).toBeInTheDocument()
      })
    })
  })

  describe('用户管理功能测试', () => {
    beforeEach(() => {
      // 确保是管理员用户
      localStorage.setItem('user', JSON.stringify({
        id: '1',
        username: 'admin',
        role: 'admin'
      }))
    })

    it('应该显示用户列表', async () => {
      const mockUsers = {
        success: true,
        data: {
          users: [
            {
              id: '1',
              username: 'admin',
              nickname: '管理员',
              role: 'admin',
              status: 'active',
              created_at: '2024-01-01T10:00:00Z'
            },
            {
              id: '2',
              username: 'user1',
              nickname: '用户1',
              role: 'user',
              status: 'active',
              created_at: '2024-01-02T10:00:00Z'
            }
          ],
          pagination: {
            current_page: 1,
            total_pages: 1,
            total_items: 2
          }
        }
      }
      
      mockFetch(mockUsers)
      
      render(<Settings />)
      
      const userManagementTab = screen.getByText(/用户管理/i)
      await userEvent.click(userManagementTab)
      
      await waitFor(() => {
        expect(screen.getByText(/用户列表/i)).toBeInTheDocument()
        expect(screen.getByText(/admin/i)).toBeInTheDocument()
        expect(screen.getByText(/user1/i)).toBeInTheDocument()
      })
    })

    it('应该能够搜索用户', async () => {
      const mockSearchResults = {
        success: true,
        data: {
          users: [
            {
              id: '2',
              username: 'user1',
              nickname: '用户1',
              role: 'user'
            }
          ]
        }
      }
      
      mockFetch(mockSearchResults)
      
      render(<Settings />)
      
      const userManagementTab = screen.getByText(/用户管理/i)
      await userEvent.click(userManagementTab)
      
      await waitFor(async () => {
        const searchInput = screen.getByPlaceholderText(/搜索用户/i)
        await userEvent.type(searchInput, 'user1')
      })
      
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(
          '/api/admin/users?search=user1',
          expect.any(Object)
        )
      })
    })

    it('应该能够重置用户密码', async () => {
      const mockResetResponse = { success: true }
      mockFetch(mockResetResponse)
      
      render(<Settings />)
      
      const userManagementTab = screen.getByText(/用户管理/i)
      await userEvent.click(userManagementTab)
      
      await waitFor(async () => {
        const resetButton = screen.getByRole('button', { name: /重置密码/i })
        await userEvent.click(resetButton)
        
        const confirmButton = screen.getByRole('button', { name: /确认重置/i })
        await userEvent.click(confirmButton)
      })
      
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/admin/users/2/reset-password', {
          method: 'POST',
          headers: {
            'Authorization': 'Bearer mock-token'
          }
        })
      })
    })

    it('应该能够删除用户', async () => {
      const mockDeleteResponse = { success: true }
      mockFetch(mockDeleteResponse)
      
      render(<Settings />)
      
      const userManagementTab = screen.getByText(/用户管理/i)
      await userEvent.click(userManagementTab)
      
      await waitFor(async () => {
        const deleteButton = screen.getByRole('button', { name: /删除用户/i })
        await userEvent.click(deleteButton)
        
        const confirmButton = screen.getByRole('button', { name: /确认删除/i })
        await userEvent.click(confirmButton)
      })
      
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/admin/users/2', {
          method: 'DELETE',
          headers: {
            'Authorization': 'Bearer mock-token'
          }
        })
      })
    })
  })

  describe('关于页面测试', () => {
    it('应该显示系统信息', async () => {
      const mockSystemInfo = {
        success: true,
        data: {
          version: '1.0.0',
          build_date: '2024-01-01',
          environment: 'production',
          database_status: 'healthy',
          api_status: 'healthy'
        }
      }
      
      mockFetch(mockSystemInfo)
      
      render(<Settings />)
      
      const aboutTab = screen.getByText(/关于/i)
      await userEvent.click(aboutTab)
      
      await waitFor(() => {
        expect(screen.getByText(/AgentGroup/i)).toBeInTheDocument()
        expect(screen.getByText(/版本 1.0.0/i)).toBeInTheDocument()
        expect(screen.getByText(/智能群聊平台/i)).toBeInTheDocument()
      })
    })

    it('应该显示系统状态', async () => {
      render(<Settings />)
      
      const aboutTab = screen.getByText(/关于/i)
      await userEvent.click(aboutTab)
      
      await waitFor(() => {
        expect(screen.getByText(/系统状态/i)).toBeInTheDocument()
        expect(screen.getByText(/数据库状态/i)).toBeInTheDocument()
        expect(screen.getByText(/API状态/i)).toBeInTheDocument()
      })
    })
  })

  describe('设置保存功能测试', () => {
    it('应该实时保存设置', async () => {
      render(<Settings />)
      
      const themeToggle = screen.getByLabelText(/深色模式/i)
      await userEvent.click(themeToggle)
      
      // 应该立即保存，不需要点击保存按钮
      await waitFor(() => {
        expect(localStorage.setItem).toHaveBeenCalledWith('theme', 'dark')
      })
    })

    it('应该显示保存成功提示', async () => {
      const mockSaveResponse = { success: true }
      mockFetch(mockSaveResponse)
      
      render(<Settings />)
      
      const aiConfigTab = screen.getByText(/AI配置/i)
      await userEvent.click(aiConfigTab)
      
      await waitFor(async () => {
        const saveButton = screen.getByRole('button', { name: /保存配置/i })
        await userEvent.click(saveButton)
      })
      
      await waitFor(() => {
        expect(screen.getByText(/设置保存成功/i)).toBeInTheDocument()
      })
    })

    it('应该处理保存失败的情况', async () => {
      mockFetch({ success: false, message: '保存失败' }, 500)
      
      render(<Settings />)
      
      const aiConfigTab = screen.getByText(/AI配置/i)
      await userEvent.click(aiConfigTab)
      
      await waitFor(async () => {
        const saveButton = screen.getByRole('button', { name: /保存配置/i })
        await userEvent.click(saveButton)
      })
      
      await waitFor(() => {
        expect(screen.getByText(/保存失败/i)).toBeInTheDocument()
      })
    })
  })

  describe('响应式设计测试', () => {
    it('应该在移动设备上正确显示', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })
      
      render(<Settings />)
      
      // 检查移动端布局
      const settingsContainer = screen.getByRole('main')
      expect(settingsContainer).toHaveClass('mobile-layout')
    })

    it('应该在移动设备上使用折叠式导航', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })
      
      render(<Settings />)
      
      // 检查是否有折叠式导航
      const mobileNav = screen.getByRole('button', { name: /设置菜单/i })
      expect(mobileNav).toBeInTheDocument()
    })
  })

  describe('无障碍性测试', () => {
    it('应该有正确的ARIA标签', () => {
      render(<Settings />)
      
      const settingsTabs = screen.getAllByRole('tab')
      settingsTabs.forEach(tab => {
        expect(tab).toHaveAttribute('aria-selected')
      })
      
      const settingsPanel = screen.getByRole('tabpanel')
      expect(settingsPanel).toBeInTheDocument()
    })

    it('应该支持键盘导航', async () => {
      render(<Settings />)
      
      const firstTab = screen.getByText(/基础设置/i)
      const secondTab = screen.getByText(/AI配置/i)
      
      firstTab.focus()
      expect(firstTab).toHaveFocus()
      
      await userEvent.keyboard('{ArrowRight}')
      expect(secondTab).toHaveFocus()
    })
  })
})
