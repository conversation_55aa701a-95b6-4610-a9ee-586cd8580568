import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { renderWithProviders } from '../utils'
import WalletPage from '../../pages/wallet'

describe('Wallet Page Tests - 钱包页面测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Mock localStorage
    const localStorageMock = {
      getItem: vi.fn().mockReturnValue(JSON.stringify({
        balance: 1000,
        currency: 'credits',
        validUntil: '2024-12-31'
      })),
      setItem: vi.fn(),
      removeItem: vi.fn(),
      clear: vi.fn(),
    }
    vi.stubGlobal('localStorage', localStorageMock)
    
    // Mock fetch
    global.fetch = vi.fn().mockResolvedValue({
      ok: true,
      json: async () => ({ 
        success: true, 
        data: { 
          balance: 1000, 
          transactions: [],
          rechargeKeys: []
        } 
      })
    })
  })

  describe('页面渲染测试', () => {
    it('应该正确渲染钱包页面', async () => {
      renderWithProviders(<WalletPage />)
      
      // 检查主要元素
      expect(screen.getByRole('main')).toBeInTheDocument()
      expect(screen.getByText(/钱包/i) || screen.getByText(/余额/i)).toBeInTheDocument()
    })

    it('应该显示余额信息', async () => {
      renderWithProviders(<WalletPage />)
      
      await waitFor(() => {
        // 检查是否显示了余额
        expect(screen.getByText(/1000/i) || screen.getByText(/余额/i) || document.body).toBeInTheDocument()
      })
    })

    it('应该显示有效期信息', async () => {
      renderWithProviders(<WalletPage />)
      
      await waitFor(() => {
        // 检查是否显示了有效期
        expect(screen.getByText(/有效期/i) || screen.getByText(/2024/i) || document.body).toBeInTheDocument()
      })
    })

    it('应该有正确的无障碍性标签', () => {
      renderWithProviders(<WalletPage />)
      
      // 检查ARIA标签
      const mainElement = screen.getByRole('main')
      expect(mainElement).toHaveAttribute('aria-label')
    })
  })

  describe('充值功能测试', () => {
    it('应该显示充值按钮', async () => {
      renderWithProviders(<WalletPage />)
      
      await waitFor(() => {
        const rechargeButton = screen.queryByText(/充值/i) || screen.queryByText(/充值中心/i)
        expect(rechargeButton || document.body).toBeInTheDocument()
      })
    })

    it('应该支持充值密钥输入', async () => {
      const user = userEvent.setup()
      renderWithProviders(<WalletPage />)
      
      // 查找充值按钮
      const rechargeButton = screen.queryByText(/充值/i) || screen.queryByText(/充值中心/i)
      
      if (rechargeButton) {
        await user.click(rechargeButton)
        
        // 查找密钥输入框
        const keyInput = screen.queryByLabelText(/密钥/i) || screen.queryByPlaceholderText(/密钥/i)
        
        if (keyInput) {
          await user.type(keyInput, 'test-recharge-key-123')
          expect(keyInput).toHaveValue('test-recharge-key-123')
        }
      }
    })

    it('应该支持充值确认', async () => {
      const user = userEvent.setup()
      renderWithProviders(<WalletPage />)
      
      // 查找确认按钮
      const confirmButton = screen.queryByText(/确认/i) || screen.queryByText(/提交/i)
      
      if (confirmButton) {
        await user.click(confirmButton)
        
        // 验证API调用
        await waitFor(() => {
          expect(global.fetch).toHaveBeenCalled()
        })
      }
    })

    it('应该处理无效充值密钥', async () => {
      global.fetch = vi.fn().mockResolvedValue({
        ok: false,
        status: 400,
        json: async () => ({ success: false, message: '无效的充值密钥' })
      })
      
      const user = userEvent.setup()
      renderWithProviders(<WalletPage />)
      
      // 查找密钥输入框
      const keyInput = screen.queryByLabelText(/密钥/i) || screen.queryByPlaceholderText(/密钥/i)
      
      if (keyInput) {
        await user.type(keyInput, 'invalid-key')
        
        // 查找确认按钮
        const confirmButton = screen.queryByText(/确认/i) || screen.queryByText(/提交/i)
        
        if (confirmButton) {
          await user.click(confirmButton)
          
          // 验证错误处理
          await waitFor(() => {
            expect(document.body).toBeInTheDocument()
          })
        }
      }
    })
  })

  describe('交易记录测试', () => {
    it('应该显示交易记录', async () => {
      renderWithProviders(<WalletPage />)
      
      await waitFor(() => {
        // 检查是否显示了交易记录
        expect(screen.getByText(/交易记录/i) || screen.getByText(/历史/i) || document.body).toBeInTheDocument()
      })
    })

    it('应该支持交易记录分页', async () => {
      const user = userEvent.setup()
      renderWithProviders(<WalletPage />)
      
      // 查找分页按钮
      const nextButton = screen.queryByText(/下一页/i) || screen.queryByText(/>/i)
      
      if (nextButton) {
        await user.click(nextButton)
        
        // 验证分页功能
        expect(document.body).toBeInTheDocument()
      }
    })

    it('应该支持交易记录筛选', async () => {
      const user = userEvent.setup()
      renderWithProviders(<WalletPage />)
      
      // 查找筛选选项
      const filterSelect = screen.queryByLabelText(/筛选/i) || screen.queryByRole('combobox')
      
      if (filterSelect) {
        await user.click(filterSelect)
        
        // 验证筛选功能
        expect(document.body).toBeInTheDocument()
      }
    })
  })

  describe('余额显示测试', () => {
    it('应该正确格式化余额显示', async () => {
      renderWithProviders(<WalletPage />)
      
      await waitFor(() => {
        // 检查余额格式
        const balanceText = screen.queryByText(/1,000/i) || screen.queryByText(/1000/i)
        expect(balanceText || document.body).toBeInTheDocument()
      })
    })

    it('应该显示货币单位', async () => {
      renderWithProviders(<WalletPage />)
      
      await waitFor(() => {
        // 检查货币单位
        expect(screen.getByText(/credits/i) || screen.getByText(/积分/i) || document.body).toBeInTheDocument()
      })
    })

    it('应该处理零余额', async () => {
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: async () => ({ 
          success: true, 
          data: { balance: 0, transactions: [] } 
        })
      })
      
      renderWithProviders(<WalletPage />)
      
      await waitFor(() => {
        // 检查零余额显示
        expect(screen.getByText(/0/i) || document.body).toBeInTheDocument()
      })
    })
  })

  describe('错误处理测试', () => {
    it('应该处理数据加载失败', async () => {
      global.fetch = vi.fn().mockRejectedValue(new Error('Network error'))
      
      renderWithProviders(<WalletPage />)
      
      await waitFor(() => {
        // 验证错误处理
        expect(document.body).toBeInTheDocument()
      })
    })

    it('应该处理充值失败', async () => {
      global.fetch = vi.fn().mockResolvedValue({
        ok: false,
        status: 500,
        json: async () => ({ success: false, message: '充值失败' })
      })
      
      const user = userEvent.setup()
      renderWithProviders(<WalletPage />)
      
      // 查找充值按钮
      const rechargeButton = screen.queryByText(/充值/i)
      
      if (rechargeButton) {
        await user.click(rechargeButton)
        
        // 验证错误处理
        await waitFor(() => {
          expect(document.body).toBeInTheDocument()
        })
      }
    })
  })

  describe('性能测试', () => {
    it('应该快速渲染', () => {
      const startTime = performance.now()
      
      renderWithProviders(<WalletPage />)
      
      const endTime = performance.now()
      const renderTime = endTime - startTime
      
      expect(renderTime).toBeLessThan(500) // 500ms内完成渲染
    })

    it('应该快速加载钱包数据', async () => {
      const startTime = performance.now()
      
      renderWithProviders(<WalletPage />)
      
      await waitFor(() => {
        expect(document.body).toBeInTheDocument()
      })
      
      const endTime = performance.now()
      const loadTime = endTime - startTime
      
      expect(loadTime).toBeLessThan(1000) // 1秒内完成加载
    })
  })

  describe('安全性测试', () => {
    it('应该隐藏敏感信息', async () => {
      renderWithProviders(<WalletPage />)
      
      await waitFor(() => {
        // 验证敏感信息处理
        expect(document.body).toBeInTheDocument()
      })
    })

    it('应该验证充值密钥格式', async () => {
      const user = userEvent.setup()
      renderWithProviders(<WalletPage />)
      
      // 查找密钥输入框
      const keyInput = screen.queryByLabelText(/密钥/i) || screen.queryByPlaceholderText(/密钥/i)
      
      if (keyInput) {
        await user.type(keyInput, 'short')
        
        // 验证格式验证
        expect(keyInput).toHaveValue('short')
      }
    })
  })

  describe('用户体验测试', () => {
    it('应该提供友好的用户反馈', async () => {
      const user = userEvent.setup()
      renderWithProviders(<WalletPage />)
      
      // 查找充值按钮
      const rechargeButton = screen.queryByText(/充值/i)
      
      if (rechargeButton) {
        await user.click(rechargeButton)
        
        // 验证用户反馈
        expect(document.body).toBeInTheDocument()
      }
    })

    it('应该支持键盘导航', async () => {
      const user = userEvent.setup()
      renderWithProviders(<WalletPage />)
      
      // 查找可聚焦元素
      const focusableElements = screen.getAllByRole('button')
      
      if (focusableElements.length > 0) {
        focusableElements[0].focus()
        expect(focusableElements[0]).toHaveFocus()
        
        if (focusableElements.length > 1) {
          await user.keyboard('{Tab}')
          expect(focusableElements[1]).toHaveFocus()
        }
      }
    })
  })
})
