import React, { useState, useEffect } from 'react';
import { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';
import { useGroupChat } from '../contexts/GroupChatContext';
import { useUserStore } from '../store/userStore';
import { NotificationCenter } from '../components/NotificationCenter';
import {
  MessageSquare,
  Users,
  Settings,
  LogOut,
  Sparkles,
  Plus,
  X,
  ChevronLeft,
  ChevronRight,
  User,
} from 'lucide-react';

interface TabItem {
  id: string;
  name: string;
  href: string;
  icon: React.ComponentType<any>;
  type: 'super-agent' | 'group-chat' | 'agents';
  closable?: boolean;
}

export default function BasicLayout() {
  const location = useLocation();
  const navigate = useNavigate();
  const { groupChats, activeGroupId, setActiveGroup } = useGroupChat();
  const { userInfo, fetchUserInfo } = useUserStore();

  // 组件加载时获取用户信息
  useEffect(() => {
    if (!userInfo) {
      fetchUserInfo();
    }
  }, []); // 只在组件挂载且无用户信息时执行，避免重复调用

  // 移除侧边栏收缩功能，始终保持展开状态
  const sidebarExpanded = true;

  // 动态生成标签页：超级智能体 + 群聊标签
  const tabs: TabItem[] = [
    {
      id: 'super-agent',
      name: '超级智能体',
      href: '/',
      icon: Sparkles,
      type: 'super-agent',
      closable: false
    },
    ...groupChats.map(group => ({
      id: group.id,
      name: group.name,
      href: `/group/${group.id}`,
      icon: MessageSquare,
      type: 'group-chat' as const,
      closable: true
    }))
  ];

  // 根据当前路径确定活跃标签
  const getActiveTabId = () => {
    if (location.pathname === '/') return 'super-agent';
    if (location.pathname.startsWith('/group/')) {
      const groupId = location.pathname.split('/')[2];
      return groupId;
    }
    return 'super-agent';
  };

  const activeTab = getActiveTabId();

  // 侧边栏功能按钮和群聊列表
  const sidebarActions = [
    { name: '智能体商店', icon: Users, action: () => navigate('/agents') },
  ];

  const createNewGroupChat = () => {
    // 检查是否已达到5个群聊的限制
    if (groupChats.length >= 5) {
      alert('最多只能创建5个群聊');
      return;
    }

    navigate('/group-chat');
  };

  const closeTab = (tabId: string) => {
    const tabToClose = tabs.find(tab => tab.id === tabId);
    if (!tabToClose?.closable) return;

    // 只关闭标签页，不删除群聊数据
    // 如果关闭的是当前活跃标签，切换到超级智能体
    if (activeTab === tabId) {
      navigate('/');
    }

    // 清除活跃群聊状态
    setActiveGroup(null);
  };

  const handleTabClick = (tab: TabItem) => {
    if (tab.type === 'group-chat') {
      setActiveGroup(tab.id);
    }
    navigate(tab.href);
  };

  const handleLogout = () => {
    localStorage.removeItem('token');
    navigate('/login');
  };

  return (
    <div className="flex h-screen theme-bg-primary theme-text-primary">
      {/* Sidebar */}
      <div className="w-64 theme-bg-secondary border-r theme-border-primary flex flex-col">
        {/* Logo */}
        <div className="flex items-center h-16 px-4 border-b theme-border-primary">
          <div
            className="flex items-center space-x-2 cursor-pointer hover:opacity-80 transition-opacity"
            onClick={() => navigate('/')}
          >
            <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">AG</span>
            </div>
            <h1 className="text-lg font-bold theme-text-primary">AgentGroup</h1>
          </div>
        </div>

        {/* Navigation Actions and Group Chats */}
        <nav className="flex-1 p-4">
          <div className="space-y-4">
            {/* 新建群聊按钮 */}
            <button
              onClick={createNewGroupChat}
              disabled={groupChats.length >= 5}
              className="flex items-center w-full px-3 py-2 text-sm font-medium theme-text-primary rounded-lg hover-theme-bg-tertiary transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Plus className="mr-3 h-5 w-5" />
              新建群聊 ({groupChats.length}/5)
            </button>

            {/* 群聊列表 */}
            {groupChats.length > 0 && (
              <div className="space-y-1">
                <h3 className="text-xs font-medium theme-text-secondary uppercase tracking-wider px-3">
                  我的群聊
                </h3>
                {groupChats.map((group) => (
                  <button
                    key={group.id}
                    onClick={() => navigate(`/group/${group.id}`)}
                    className={`flex items-center w-full px-3 py-2 text-sm rounded-lg transition-colors ${
                      activeTab === group.id
                        ? 'theme-bg-accent theme-text-accent-foreground'
                        : 'theme-text-primary hover-theme-bg-tertiary'
                    }`}
                  >
                    <MessageSquare className="mr-3 h-4 w-4" />
                    <span className="truncate">{group.name}</span>
                  </button>
                ))}
              </div>
            )}

            {/* 其他功能按钮 */}
            <div className="border-t theme-border-primary pt-4 space-y-2">
              {sidebarActions.map((item) => (
                <button
                  key={item.name}
                  onClick={item.action}
                  className="flex items-center w-full px-3 py-2 text-sm font-medium theme-text-primary rounded-lg hover-theme-bg-tertiary transition-colors"
                >
                  <item.icon className="mr-3 h-5 w-5" />
                  {item.name}
                </button>
              ))}
            </div>
          </div>
        </nav>

        {/* User Section */}
        <div className="p-4 border-t theme-border-primary">
          <div className="space-y-2">
            {/* 个人中心 */}
            <button
              onClick={() => navigate('/profile')}
              className="w-full flex items-center gap-3 px-3 py-2 theme-text-secondary hover-theme-bg-tertiary rounded-lg transition-colors text-left"
              title="个人中心"
            >
              <User className="w-4 h-4" />
              <span className="text-sm">个人中心</span>
            </button>

            {/* 系统管理（仅管理员可见） */}
            {userInfo?.role === 'admin' && (
              <button
                onClick={() => navigate('/settings')}
                className="w-full flex items-center gap-3 px-3 py-2 theme-text-secondary hover-theme-bg-tertiary rounded-lg transition-colors text-left"
                title="系统管理"
              >
                <Settings className="w-4 h-4" />
                <span className="text-sm">系统管理</span>
              </button>
            )}

            {/* 退出登录 */}
            <button
              onClick={handleLogout}
              className="w-full flex items-center gap-3 px-3 py-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors text-left"
              title="退出登录"
            >
              <LogOut className="w-4 h-4" />
              <span className="text-sm">退出登录</span>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Tab Bar */}
        <div className="flex items-center justify-between theme-bg-tertiary border-b theme-border-primary px-4 h-12">
          <div className="flex items-center space-x-1 overflow-x-auto">
            {tabs.map((tab) => (
              <div
                key={tab.id}
                className={`flex items-center space-x-2 px-3 py-1.5 rounded-lg cursor-pointer transition-colors ${
                  activeTab === tab.id
                    ? 'theme-bg-primary theme-text-primary theme-shadow-secondary'
                    : 'theme-text-secondary hover-theme-text-primary hover-theme-bg-quaternary'
                }`}
                onClick={() => handleTabClick(tab)}
              >
                <tab.icon className="w-4 h-4" />
                <span className="text-sm whitespace-nowrap">{tab.name}</span>
                {tab.closable && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      closeTab(tab.id);
                    }}
                    className="p-0.5 hover-theme-bg-quaternary rounded transition-colors"
                  >
                    <X className="w-3 h-3" />
                  </button>
                )}
              </div>
            ))}
          </div>

          {/* 右侧通知中心 */}
          <div className="flex items-center">
            <NotificationCenter />
          </div>
        </div>

        {/* Content Area */}
        <main className="flex-1 overflow-hidden">
          <Outlet />
        </main>
      </div>
    </div>
  );
}