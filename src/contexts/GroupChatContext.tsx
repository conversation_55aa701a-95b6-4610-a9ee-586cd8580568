import React, { createContext, useContext, useState } from 'react';

export interface Agent {
  id: string;
  name: string;
  description: string;
  avatar: string;
  capabilities: string[];
  isSelected: boolean;
}

export interface GroupChat {
  id: string;
  name: string;
  agents: Agent[];
  lastMessage: string;
  timestamp: Date;
  isActive: boolean;
}

interface GroupChatContextType {
  groupChats: GroupChat[];
  activeGroupId: string | null;
  createGroupChat: (name: string, agents: Agent[]) => string;
  deleteGroupChat: (id: string) => void;
  updateGroupChat: (id: string, updates: Partial<GroupChat>) => void;
  setActiveGroup: (id: string | null) => void;
  getGroupChat: (id: string) => GroupChat | undefined;
  setGroupChats: (groupChats: GroupChat[]) => void;
}

const GroupChatContext = createContext<GroupChatContextType | undefined>(undefined);

export const useGroupChat = () => {
  const context = useContext(GroupChatContext);
  if (context === undefined) {
    throw new Error('useGroupChat must be used within a GroupChatProvider');
  }
  return context;
};

interface GroupChatProviderProps {
  children: React.ReactNode;
}

export const GroupChatProvider: React.FC<GroupChatProviderProps> = ({ children }) => {
  const [groupChats, setGroupChats] = useState<GroupChat[]>([
    {
      id: 'product-dev-team',
      name: '产品开发团队',
      agents: [
        { id: 'super', name: '超级智能体', description: '任务协调', avatar: '🤖', capabilities: ['协调', '分析'], isSelected: true },
        { id: 'dev', name: '开发助手', description: '代码开发', avatar: '💻', capabilities: ['编程', '调试'], isSelected: true },
        { id: 'design', name: '设计师', description: 'UI设计', avatar: '🎨', capabilities: ['设计', '原型'], isSelected: true },
      ],
      lastMessage: '产品原型已完成，请查看',
      timestamp: new Date(),
      isActive: false,
    },
  ]);

  const [activeGroupId, setActiveGroupId] = useState<string | null>(null);

  const createGroupChat = (name: string, agents: Agent[]): string => {
    const newId = `group-${Date.now()}`;
    const newGroup: GroupChat = {
      id: newId,
      name,
      agents,
      lastMessage: '群聊已创建',
      timestamp: new Date(),
      isActive: false,
    };

    setGroupChats(prev => [...prev, newGroup]);
    return newId;
  };

  const deleteGroupChat = (id: string) => {
    setGroupChats(prev => prev.filter(group => group.id !== id));
    if (activeGroupId === id) {
      setActiveGroupId(null);
    }
  };

  const updateGroupChat = (id: string, updates: Partial<GroupChat>) => {
    setGroupChats(prev => prev.map(group => 
      group.id === id ? { ...group, ...updates } : group
    ));
  };

  const setActiveGroup = (id: string | null) => {
    setActiveGroupId(id);
    // 更新群聊的活跃状态
    setGroupChats(prev => prev.map(group => ({
      ...group,
      isActive: group.id === id
    })));
  };

  const getGroupChat = (id: string): GroupChat | undefined => {
    return groupChats.find(group => group.id === id);
  };

  return (
    <GroupChatContext.Provider value={{
      groupChats,
      activeGroupId,
      createGroupChat,
      deleteGroupChat,
      updateGroupChat,
      setActiveGroup,
      getGroupChat,
      setGroupChats,
    }}>
      {children}
    </GroupChatContext.Provider>
  );
};
