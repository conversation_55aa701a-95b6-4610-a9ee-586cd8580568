// API调用工具函数

export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
}

export interface CreateGroupRequest {
  name: string;
  description?: string;
  group_type: 'mixed' | 'ai_only' | 'human_only';
  max_members?: number;
  agent_ids?: string[];
  user_ids?: string[];
}

export interface GroupInfo {
  id: string;
  name: string;
  description: string;
  group_type: string;
  max_members: number;
  created_by: string;
  agent_ids: string[];
  user_ids: string[];
  created_at: string;
}

// 获取认证token
function getAuthToken(): string | null {
  return localStorage.getItem('token');
}

// 通用API请求函数
export async function apiRequest<T = any>(
  endpoint: string, 
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  const token = getAuthToken();
  
  const defaultHeaders: HeadersInit = {
    'Content-Type': 'application/json',
  };

  if (token) {
    defaultHeaders['Authorization'] = `Bearer ${token}`;
  }

  const config: RequestInit = {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  };

  try {
    const response = await fetch(endpoint, config);

    // 检查响应是否成功
    if (!response.ok) {
      // 尝试解析错误响应
      try {
        const errorData = await response.json();
        return {
          success: false,
          message: errorData.message || `HTTP error! status: ${response.status}`,
          data: null
        };
      } catch {
        return {
          success: false,
          message: `HTTP error! status: ${response.status}`,
          data: null
        };
      }
    }

    // 尝试解析成功响应
    try {
      const data = await response.json();
      return data;
    } catch {
      return {
        success: true,
        message: 'Success',
        data: null
      };
    }
  } catch (error) {
    console.error('API request failed:', error);
    // 返回错误响应而不是抛出异常
    return {
      success: false,
      message: error instanceof Error ? error.message : '网络请求失败',
      data: null
    };
  }
}

// 创建群聊
export async function createGroup(request: CreateGroupRequest): Promise<ApiResponse<GroupInfo>> {
  return apiRequest<GroupInfo>('/api/groups/create', {
    method: 'POST',
    body: JSON.stringify(request),
  });
}

// 获取群聊列表
export async function getGroupList(params?: {
  page?: number;
  limit?: number;
  group_type?: string;
  search?: string;
}): Promise<ApiResponse<{
  groups: any[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}>> {
  const searchParams = new URLSearchParams();
  
  if (params?.page) searchParams.set('page', params.page.toString());
  if (params?.limit) searchParams.set('limit', params.limit.toString());
  if (params?.group_type) searchParams.set('group_type', params.group_type);
  if (params?.search) searchParams.set('search', params.search);
  
  const url = `/api/groups/list${searchParams.toString() ? '?' + searchParams.toString() : ''}`;
  return apiRequest(url);
}

// 获取群聊详情
export async function getGroupDetail(groupId: string): Promise<ApiResponse<any>> {
  return apiRequest(`/api/groups/${groupId}`);
}

// 删除群聊
export async function deleteGroup(groupId: string): Promise<ApiResponse<any>> {
  return apiRequest(`/api/groups/${groupId}`, {
    method: 'DELETE',
  });
}
