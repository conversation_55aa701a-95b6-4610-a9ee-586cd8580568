import { create } from 'zustand'
import { request } from '@/utils/request'

interface UserInfo {
  id: number;
  username: string;
  email: string;
  nickname: string;
  role: 'admin' | 'department_admin' | 'user';
  department_id?: string;
  balance: number;
  created_at: string;
}

interface UserStore {
  userInfo: UserInfo | null;
  loading: boolean;
  setUserInfo: (userInfo: UserInfo) => void;
  fetchUserInfo: () => Promise<void>;
  clearUserInfo: () => void;
}

export const useUserStore = create<UserStore>((set, get) => ({
  userInfo: null,
  loading: false,

  setUserInfo: (userInfo: UserInfo) => set({ userInfo }),

  fetchUserInfo: async () => {
    const token = localStorage.getItem('token');
    if (!token) {
      set({ userInfo: null, loading: false });
      return;
    }

    try {
      set({ loading: true });
      const response = await request('/api/auth/me');
      const data = await response.json();

      if (data.success) {
        set({ userInfo: data.data, loading: false });
      } else {
        set({ userInfo: null, loading: false });
        // Token可能已过期，清除本地存储
        localStorage.removeItem('token');
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      set({ userInfo: null, loading: false });
      // 清除可能无效的token
      localStorage.removeItem('token');
    }
  },

  clearUserInfo: () => {
    set({ userInfo: null, loading: false });
    localStorage.removeItem('token');
  }
}));