import { create } from 'zustand'
import { request } from '@/utils/request'

interface UserInfo {
  id: number;
  username: string;
  email: string;
  nickname: string;
  role: 'admin' | 'department_admin' | 'user';
  department_id?: string;
  balance: number;
  created_at: string;
}

interface UserStore {
  userInfo: UserInfo | null;
  loading: boolean;
  setUserInfo: (userInfo: UserInfo) => void;
  fetchUserInfo: () => Promise<void>;
  clearUserInfo: () => void;
}

export const useUserStore = create<UserStore>((set, get) => ({
  userInfo: null,
  loading: false,

  setUserInfo: (userInfo: UserInfo) => set({ userInfo }),

  fetchUserInfo: async () => {
    const token = localStorage.getItem('token');
    if (!token) {
      // 没有token时，尝试检查是否跳过权限校验
      try {
        const response = await request('/api/auth/me');
        const data = await response.json();

        if (data.success) {
          // 如果没有token但API返回成功，说明跳过了权限校验
          set({ userInfo: data.data, loading: false });
        } else {
          // 设置默认admin用户（用于开发环境）
          const defaultAdminUser: UserInfo = {
            id: 1,
            username: 'admin',
            email: '<EMAIL>',
            nickname: '系统管理员',
            role: 'admin',
            department_id: undefined,
            balance: 10000,
            created_at: new Date().toISOString()
          };
          set({ userInfo: defaultAdminUser, loading: false });
        }
      } catch (error) {
        // 设置默认admin用户（用于开发环境）
        const defaultAdminUser: UserInfo = {
          id: 1,
          username: 'admin',
          email: '<EMAIL>',
          nickname: '系统管理员',
          role: 'admin',
          department_id: undefined,
          balance: 10000,
          created_at: new Date().toISOString()
        };
        set({ userInfo: defaultAdminUser, loading: false });
      }
      return;
    }

    try {
      set({ loading: true });
      const response = await request('/api/auth/me');
      const data = await response.json();

      if (data.success) {
        set({ userInfo: data.data, loading: false });
      } else {
        set({ userInfo: null, loading: false });
        // Token可能已过期，清除本地存储
        localStorage.removeItem('token');
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      set({ userInfo: null, loading: false });
      // 清除可能无效的token
      localStorage.removeItem('token');
    }
  },

  clearUserInfo: () => {
    set({ userInfo: null, loading: false });
    localStorage.removeItem('token');
  }
}));