import React, { useState, useEffect } from 'react';
import { Bell, X, MessageSquare, AtSign, Users } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { request } from '@/utils/request';
import { useUserStore } from '@/store/userStore';

interface Notification {
  id: string;
  type: 'mention' | 'message' | 'group_invite' | 'system';
  title: string;
  content: string;
  sender?: {
    id: string;
    name: string;
    avatar?: string;
  };
  groupName?: string;
  timestamp: string;
  read: boolean;
  actionUrl?: string;
}

interface NotificationCenterProps {
  className?: string;
}

export function NotificationCenter({ className }: NotificationCenterProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const userStore = useUserStore();

  // 获取通知列表
  const fetchNotifications = async () => {
    try {
      setLoading(true);
      const response = await request('/api/notifications', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setNotifications(data.data.notifications || []);
        setUnreadCount(data.data.unread_count || 0);
      }
    } catch (error) {
      console.error('获取通知失败:', error);
      // 使用模拟数据
      const mockNotifications: Notification[] = [
        {
          id: '1',
          type: 'mention',
          title: '@提醒',
          content: '张三在"技术讨论群"中@了您',
          sender: { id: '2', name: '张三' },
          groupName: '技术讨论群',
          timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
          read: false,
          actionUrl: '/chat?id=1'
        },
        {
          id: '2',
          type: 'message',
          title: '新消息',
          content: '您有3条新消息',
          groupName: '产品讨论群',
          timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
          read: false,
          actionUrl: '/chat?id=2'
        },
        {
          id: '3',
          type: 'group_invite',
          title: '群聊邀请',
          content: '李四邀请您加入"设计团队"群聊',
          sender: { id: '3', name: '李四' },
          groupName: '设计团队',
          timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          read: true,
          actionUrl: '/chat?invite=abc123'
        },
        {
          id: '4',
          type: 'system',
          title: '系统通知',
          content: '您的账户余额不足，请及时充值',
          timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
          read: false,
          actionUrl: '/profile?tab=wallet'
        }
      ];
      setNotifications(mockNotifications);
      setUnreadCount(mockNotifications.filter(n => !n.read).length);
    } finally {
      setLoading(false);
    }
  };

  // 标记通知为已读
  const markAsRead = async (notificationId: string) => {
    try {
      await request(`/api/notifications/${notificationId}/read`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      setNotifications(prev => 
        prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
      );
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('标记已读失败:', error);
      // 本地更新
      setNotifications(prev => 
        prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
      );
      setUnreadCount(prev => Math.max(0, prev - 1));
    }
  };

  // 清空所有通知
  const clearAllNotifications = async () => {
    try {
      await request('/api/notifications/clear', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      setNotifications([]);
      setUnreadCount(0);
    } catch (error) {
      console.error('清空通知失败:', error);
      // 本地清空
      setNotifications([]);
      setUnreadCount(0);
    }
  };

  // 处理通知点击
  const handleNotificationClick = (notification: Notification) => {
    if (!notification.read) {
      markAsRead(notification.id);
    }

    if (notification.actionUrl) {
      window.location.href = notification.actionUrl;
    }

    setIsOpen(false);
  };

  // 获取通知图标
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'mention':
        return <AtSign className="h-4 w-4 text-blue-500" />;
      case 'message':
        return <MessageSquare className="h-4 w-4 text-green-500" />;
      case 'group_invite':
        return <Users className="h-4 w-4 text-purple-500" />;
      default:
        return <Bell className="h-4 w-4 text-gray-500" />;
    }
  };

  // 格式化时间
  const formatTime = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diff = now.getTime() - time.getTime();
    
    if (diff < 60 * 1000) {
      return '刚刚';
    } else if (diff < 60 * 60 * 1000) {
      return `${Math.floor(diff / (60 * 1000))}分钟前`;
    } else if (diff < 24 * 60 * 60 * 1000) {
      return `${Math.floor(diff / (60 * 60 * 1000))}小时前`;
    } else {
      return time.toLocaleDateString();
    }
  };

  // 初始化时获取通知
  useEffect(() => {
    if (userStore.userInfo) {
      fetchNotifications();
    }
  }, [userStore.userInfo]);

  // 定期刷新通知
  useEffect(() => {
    if (!userStore.userInfo) return;

    const interval = setInterval(() => {
      fetchNotifications();
    }, 30000); // 30秒刷新一次

    return () => clearInterval(interval);
  }, [userStore.userInfo]);

  return (
    <div className={cn("relative", className)}>
      {/* 通知按钮 */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 hover:bg-gray-100 dark:hover:bg-gray-800"
      >
        <Bell className="h-5 w-5" />
        {unreadCount > 0 && (
          <Badge 
            className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center bg-red-500 text-white text-xs rounded-full min-w-[20px]"
          >
            {unreadCount > 99 ? '99+' : unreadCount}
          </Badge>
        )}
      </Button>

      {/* 通知面板 */}
      {isOpen && (
        <div className="absolute right-0 top-full mt-2 w-80 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50">
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="font-semibold text-gray-900 dark:text-white">通知中心</h3>
            <div className="flex items-center gap-2">
              {notifications.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearAllNotifications}
                  className="text-xs text-gray-500 hover:text-gray-700"
                >
                  清空
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
                className="p-1"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <ScrollArea className="max-h-96">
            {loading ? (
              <div className="p-4 text-center text-gray-500">
                加载中...
              </div>
            ) : notifications.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                暂无通知
              </div>
            ) : (
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {notifications.map((notification) => (
                  <div
                    key={notification.id}
                    onClick={() => handleNotificationClick(notification)}
                    className={cn(
                      "p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",
                      !notification.read && "bg-blue-50 dark:bg-blue-900/20"
                    )}
                  >
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 mt-1">
                        {getNotificationIcon(notification.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {notification.title}
                          </p>
                          {!notification.read && (
                            <div className="w-2 h-2 bg-red-500 rounded-full flex-shrink-0"></div>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                          {notification.content}
                        </p>
                        {notification.groupName && (
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            来自: {notification.groupName}
                          </p>
                        )}
                        <p className="text-xs text-gray-400 dark:text-gray-500 mt-2">
                          {formatTime(notification.timestamp)}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </ScrollArea>
        </div>
      )}

      {/* 点击外部关闭 */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}
