// HTTP请求工具函数
export interface RequestOptions extends RequestInit {
  timeout?: number;
}

export class RequestError extends Error {
  constructor(
    message: string,
    public status?: number,
    public response?: Response
  ) {
    super(message);
    this.name = 'RequestError';
  }
}

export async function request(
  url: string,
  options: RequestOptions = {}
): Promise<Response> {
  const { timeout = 30000, ...fetchOptions } = options;

  // 创建AbortController用于超时控制
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    // 默认请求头
    const defaultHeaders = {
      'Content-Type': 'application/json',
    };

    // 合并请求头
    const headers = {
      ...defaultHeaders,
      ...fetchOptions.headers,
    };

    // 发送请求
    const response = await fetch(url, {
      ...fetchOptions,
      headers,
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    // 检查响应状态
    if (!response.ok) {
      throw new RequestError(
        `HTTP ${response.status}: ${response.statusText}`,
        response.status,
        response
      );
    }

    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        throw new RequestError('Request timeout');
      }
      throw error;
    }
    
    throw new RequestError('Unknown error occurred');
  }
}

// 便捷方法
export const api = {
  get: (url: string, options?: RequestOptions) =>
    request(url, { ...options, method: 'GET' }),
    
  post: (url: string, data?: any, options?: RequestOptions) =>
    request(url, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    }),
    
  put: (url: string, data?: any, options?: RequestOptions) =>
    request(url, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    }),
    
  delete: (url: string, options?: RequestOptions) =>
    request(url, { ...options, method: 'DELETE' }),
};

export default request;
