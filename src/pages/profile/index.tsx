import React, { useState, useEffect } from 'react';
import { User, Wallet, CreditCard, History, Settings as SettingsIcon, Lock, Mail } from 'lucide-react';
import { cn } from "@/lib/utils";
import { useUserStore } from '@/store/userStore';
import { request } from '@/utils/request';
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface UserBalance {
  balance: number;
  account_expires_at: string;
  stats: {
    total_income: number;
    total_expense: number;
    total_transactions: number;
  };
  recent_transactions: Array<{
    id: number;
    type: string;
    amount: number;
    description: string;
    created_at: string;
  }>;
}

export default function Profile() {
  const [activeTab, setActiveTab] = useState('profile');
  const [userBalance, setUserBalance] = useState<UserBalance | null>(null);
  const [loading, setLoading] = useState(false);
  const [rechargeKey, setRechargeKey] = useState('');
  const [recharging, setRecharging] = useState(false);
  const [showRechargeForm, setShowRechargeForm] = useState(false);
  const [showPasswordDialog, setShowPasswordDialog] = useState(false);
  const [showEmailDialog, setShowEmailDialog] = useState(false);
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [emailForm, setEmailForm] = useState({
    email: '',
    verificationCode: ''
  });
  const userStore = useUserStore();

  // 获取用户余额信息
  const fetchUserBalance = async () => {
    try {
      setLoading(true);
      const response = await request('/api/billing/balance?include_transactions=true', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();
      if (data.success) {
        setUserBalance(formatBalanceData(data.data));
      }
    } catch (error) {
      console.error('获取余额失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 使用充值key充值
  const handleRecharge = async () => {
    if (!rechargeKey.trim()) {
      alert('请输入充值密钥');
      return;
    }

    try {
      setRecharging(true);
      const response = await request('/api/billing/recharge-by-key', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          recharge_key: rechargeKey.trim()
        })
      });
      const data = await response.json();

      if (data.success) {
        alert(`充值成功！获得 ${data.data.amount} 字币，账户有效期延长至 ${new Date(data.data.expires_at).toLocaleDateString()}`);
        setRechargeKey('');
        setShowRechargeForm(false);
        fetchUserBalance(); // 刷新余额
        // 更新用户信息
        userStore.fetchUserInfo();
      } else {
        alert(data.message || '充值失败，请检查密钥是否正确');
      }
    } catch (error) {
      console.error('充值失败:', error);
      alert('充值失败，请重试');
    } finally {
      setRecharging(false);
    }
  };

  // 修改密码
  const changePassword = async () => {
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      alert('新密码和确认密码不匹配');
      return;
    }

    if (passwordForm.newPassword.length < 6) {
      alert('新密码长度至少6位');
      return;
    }

    try {
      const response = await request('/api/auth/change-password', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          currentPassword: passwordForm.currentPassword,
          newPassword: passwordForm.newPassword
        })
      });

      if (response.ok) {
        alert('密码修改成功');
        setShowPasswordDialog(false);
        setPasswordForm({
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        });
      } else {
        const data = await response.json();
        alert(data.message || '密码修改失败');
      }
    } catch (error) {
      console.error('密码修改失败:', error);
      alert('密码修改失败');
    }
  };

  // 绑定邮箱
  const bindEmail = async () => {
    if (!emailForm.email) {
      alert('请输入邮箱地址');
      return;
    }

    try {
      const response = await request('/api/auth/bind-email', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: emailForm.email,
          verificationCode: emailForm.verificationCode
        })
      });

      if (response.ok) {
        alert('邮箱绑定成功');
        setShowEmailDialog(false);
        setEmailForm({
          email: '',
          verificationCode: ''
        });
        // 更新用户信息
        userStore.setUserInfo({
          ...userStore.userInfo!,
          email: emailForm.email
        });
      } else {
        const data = await response.json();
        alert(data.message || '邮箱绑定失败');
      }
    } catch (error) {
      console.error('邮箱绑定失败:', error);
      alert('邮箱绑定失败');
    }
  };

  useEffect(() => {
    if (activeTab === 'wallet' || activeTab === 'history') {
      fetchUserBalance();
    }
  }, [activeTab]);

  // 格式化余额数据以匹配后端API结构
  const formatBalanceData = (data: any): UserBalance => {
    return {
      balance: data.balance || 0,
      account_expires_at: data.account_expires_at,
      stats: {
        total_income: data.stats?.total_income || 0,
        total_expense: data.stats?.total_expense || 0,
        total_transactions: data.stats?.transaction_count || 0
      },
      recent_transactions: data.transactions?.records || []
    };
  };

  const tabs = [
    { id: 'profile', name: '个人信息', icon: User },
    { id: 'wallet', name: '我的钱包', icon: Wallet },
    { id: 'history', name: '交易记录', icon: History },
    { id: 'settings', name: '个人设置', icon: SettingsIcon },
  ];

  return (
    <div className="flex flex-col h-full theme-bg-primary theme-text-primary">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b theme-border-primary">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <User className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-semibold">个人中心</h1>
            <p className="text-sm theme-text-secondary">管理您的个人信息和账户设置</p>
          </div>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar */}
        <div className="w-64 theme-bg-secondary border-r theme-border-primary">
          <nav className="p-4 space-y-2">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={cn(
                    "w-full flex items-center gap-3 px-3 py-2 rounded-md text-left transition-colors",
                    activeTab === tab.id
                      ? "bg-blue-600 text-white"
                      : "theme-text-secondary hover-theme-bg-tertiary"
                  )}
                >
                  <Icon className="w-5 h-5" />
                  {tab.name}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Content */}
        <div className="flex-1 p-6 overflow-y-auto">
          {activeTab === 'profile' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">个人信息</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium theme-text-secondary mb-2">用户名</label>
                  <div className="theme-bg-tertiary px-3 py-2 rounded-md">
                    {userStore.userInfo?.username || '未设置'}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium theme-text-secondary mb-2">昵称</label>
                  <div className="theme-bg-tertiary px-3 py-2 rounded-md">
                    {userStore.userInfo?.nickname || '未设置'}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium theme-text-secondary mb-2">角色</label>
                  <div className="theme-bg-tertiary px-3 py-2 rounded-md">
                    {userStore.userInfo?.role === 'admin' ? '管理员' : 
                     userStore.userInfo?.role === 'department_admin' ? '部门管理员' : '普通用户'}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium theme-text-secondary mb-2">邮箱</label>
                  <div className="theme-bg-tertiary px-3 py-2 rounded-md">
                    {userStore.userInfo?.email || '未设置'}
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'wallet' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">我的钱包</h3>
                <button
                  onClick={() => setShowRechargeForm(!showRechargeForm)}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center gap-2"
                >
                  <CreditCard className="w-4 h-4" />
                  充值
                </button>
              </div>

              {loading ? (
                <div className="text-center theme-text-secondary">加载中...</div>
              ) : userBalance ? (
                <div className="space-y-6">
                  {/* 余额卡片 */}
                  <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6 rounded-lg text-white">
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="text-sm opacity-80">当前余额</div>
                        <div className="text-3xl font-bold">{userBalance.balance.toLocaleString()} 字币</div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm opacity-80">账户有效期</div>
                        <div className="text-lg font-semibold">
                          {userBalance.account_expires_at
                            ? new Date(userBalance.account_expires_at).toLocaleDateString()
                            : '永久有效'
                          }
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 充值表单 */}
                  {showRechargeForm && (
                    <div className="theme-bg-tertiary p-6 rounded-lg border-2 border-blue-200">
                      <h4 className="text-lg font-semibold mb-4">充值中心</h4>
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium theme-text-secondary mb-2">
                            充值密钥
                          </label>
                          <input
                            type="text"
                            value={rechargeKey}
                            onChange={(e) => setRechargeKey(e.target.value)}
                            placeholder="请输入管理员提供的充值密钥"
                            className="w-full px-3 py-2 theme-bg-primary theme-border-primary border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                          <div className="text-xs theme-text-secondary mt-1">
                            请联系管理员获取充值密钥，密钥包含充值金额和有效期延长信息
                          </div>
                        </div>
                        <div className="flex gap-3">
                          <button
                            onClick={handleRecharge}
                            disabled={recharging || !rechargeKey.trim()}
                            className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white py-2 px-4 rounded-md transition-colors"
                          >
                            {recharging ? '充值中...' : '确认充值'}
                          </button>
                          <button
                            onClick={() => {
                              setShowRechargeForm(false);
                              setRechargeKey('');
                            }}
                            className="px-4 py-2 theme-bg-quaternary theme-text-secondary rounded-md hover:opacity-80 transition-colors"
                          >
                            取消
                          </button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 统计信息 */}
                  <div className="grid grid-cols-3 gap-4">
                    <div className="theme-bg-tertiary p-4 rounded-lg">
                      <div className="theme-text-secondary text-sm">总收入</div>
                      <div className="text-xl font-semibold">
                        {userBalance.stats.total_income.toLocaleString()} 字币
                      </div>
                    </div>
                    <div className="theme-bg-tertiary p-4 rounded-lg">
                      <div className="theme-text-secondary text-sm">总支出</div>
                      <div className="text-xl font-semibold">
                        {userBalance.stats.total_expense.toLocaleString()} 字币
                      </div>
                    </div>
                    <div className="theme-bg-tertiary p-4 rounded-lg">
                      <div className="theme-text-secondary text-sm">交易次数</div>
                      <div className="text-xl font-semibold">
                        {userBalance.stats.total_transactions}
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center theme-text-secondary">暂无数据</div>
              )}
            </div>
          )}



          {activeTab === 'history' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">交易记录</h3>
              {loading ? (
                <div className="text-center theme-text-secondary">加载中...</div>
              ) : userBalance?.recent_transactions?.length ? (
                <div className="space-y-2">
                  {userBalance.recent_transactions.map((transaction) => (
                    <div key={transaction.id} className="theme-bg-tertiary p-4 rounded-lg">
                      <div className="flex justify-between items-center">
                        <div>
                          <div className="font-medium">{transaction.description}</div>
                          <div className="text-sm theme-text-secondary">
                            {new Date(transaction.created_at).toLocaleString()}
                          </div>
                        </div>
                        <div className={cn(
                          "font-semibold",
                          transaction.type === 'income' ? 'text-green-600' : 'text-red-600'
                        )}>
                          {transaction.type === 'income' ? '+' : '-'}{transaction.amount.toFixed(2)} 字节币
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center theme-text-secondary">暂无交易记录</div>
              )}
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">个人设置</h3>
              <div className="space-y-4">
                <div className="theme-bg-tertiary p-4 rounded-lg">
                  <div className="font-medium mb-2">主题偏好</div>
                  <div className="text-sm theme-text-secondary mb-3">
                    选择您喜欢的界面主题
                  </div>
                  <div className="flex items-center space-x-2">
                    <button className="px-3 py-1.5 bg-blue-600 text-white rounded-lg text-sm">
                      跟随系统
                    </button>
                    <button className="px-3 py-1.5 theme-bg-quaternary theme-text-secondary rounded-lg text-sm">
                      浅色模式
                    </button>
                    <button className="px-3 py-1.5 theme-bg-quaternary theme-text-secondary rounded-lg text-sm">
                      深色模式
                    </button>
                  </div>
                </div>
                <div className="theme-bg-tertiary p-4 rounded-lg">
                  <div className="font-medium mb-2">通知设置</div>
                  <div className="text-sm theme-text-secondary mb-3">
                    管理您的通知偏好
                  </div>
                  <div className="space-y-2">
                    <label className="flex items-center justify-between">
                      <span className="text-sm">新消息通知</span>
                      <input type="checkbox" defaultChecked className="rounded" />
                    </label>
                    <label className="flex items-center justify-between">
                      <span className="text-sm">群聊@提醒</span>
                      <input type="checkbox" defaultChecked className="rounded" />
                    </label>
                  </div>
                </div>
                <div className="theme-bg-tertiary p-4 rounded-lg">
                  <div className="font-medium mb-2">安全设置</div>
                  <div className="text-sm theme-text-secondary mb-3">
                    管理您的账户安全
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    <button
                      onClick={() => setShowPasswordDialog(true)}
                      className="flex items-center gap-3 px-4 py-3 theme-bg-quaternary rounded-lg text-sm hover:opacity-80 transition-opacity"
                    >
                      <Lock className="h-4 w-4 text-blue-500" />
                      <span>修改密码</span>
                    </button>
                    <button
                      onClick={() => setShowEmailDialog(true)}
                      className="flex items-center gap-3 px-4 py-3 theme-bg-quaternary rounded-lg text-sm hover:opacity-80 transition-opacity"
                    >
                      <Mail className="h-4 w-4 text-green-500" />
                      <span>绑定邮箱</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 修改密码对话框 */}
      <Dialog open={showPasswordDialog} onOpenChange={setShowPasswordDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>修改密码</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="currentPassword">当前密码</Label>
              <Input
                id="currentPassword"
                type="password"
                value={passwordForm.currentPassword}
                onChange={(e) => setPasswordForm(prev => ({ ...prev, currentPassword: e.target.value }))}
                placeholder="请输入当前密码"
              />
            </div>
            <div>
              <Label htmlFor="newPassword">新密码</Label>
              <Input
                id="newPassword"
                type="password"
                value={passwordForm.newPassword}
                onChange={(e) => setPasswordForm(prev => ({ ...prev, newPassword: e.target.value }))}
                placeholder="请输入新密码（至少6位）"
              />
            </div>
            <div>
              <Label htmlFor="confirmPassword">确认新密码</Label>
              <Input
                id="confirmPassword"
                type="password"
                value={passwordForm.confirmPassword}
                onChange={(e) => setPasswordForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                placeholder="请再次输入新密码"
              />
            </div>
            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={() => setShowPasswordDialog(false)}>
                取消
              </Button>
              <Button onClick={changePassword}>
                确认修改
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 绑定邮箱对话框 */}
      <Dialog open={showEmailDialog} onOpenChange={setShowEmailDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>绑定邮箱</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="email">邮箱地址</Label>
              <Input
                id="email"
                type="email"
                value={emailForm.email}
                onChange={(e) => setEmailForm(prev => ({ ...prev, email: e.target.value }))}
                placeholder="请输入邮箱地址"
              />
            </div>
            <div>
              <Label htmlFor="verificationCode">验证码（可选）</Label>
              <Input
                id="verificationCode"
                value={emailForm.verificationCode}
                onChange={(e) => setEmailForm(prev => ({ ...prev, verificationCode: e.target.value }))}
                placeholder="如需验证码请输入"
              />
            </div>
            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={() => setShowEmailDialog(false)}>
                取消
              </Button>
              <Button onClick={bindEmail}>
                确认绑定
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
