import React, { useState, useRef, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useGroupChat } from '../../contexts/GroupChatContext';
import { Send, Settings, Users, Paperclip, Smile, X } from 'lucide-react';

interface Message {
  id: string;
  content: string;
  sender: 'user' | string; // 'user' or agent id
  senderName: string;
  senderAvatar: string;
  timestamp: Date;
}

const GroupChatDetail: React.FC = () => {
  const { groupId } = useParams<{ groupId: string }>();
  const navigate = useNavigate();
  const { getGroupChat, setActiveGroup } = useGroupChat();
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const groupChat = groupId ? getGroupChat(groupId) : undefined;

  useEffect(() => {
    if (groupId) {
      setActiveGroup(groupId);
    }
    return () => setActiveGroup(null);
  }, [groupId, setActiveGroup]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading || !groupChat) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      sender: 'user',
      senderName: '我',
      senderAvatar: '👤',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    // 模拟智能体回复
    setTimeout(() => {
      const randomAgent = groupChat.agents[Math.floor(Math.random() * groupChat.agents.length)];
      const agentMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: `${randomAgent.name}：我理解您的需求："${inputValue}"。让我来协助您处理这个任务。`,
        sender: randomAgent.id,
        senderName: randomAgent.name,
        senderAvatar: randomAgent.avatar,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, agentMessage]);
      setIsLoading(false);
    }, 1000);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleCloseChat = () => {
    // 跳转逻辑：如果有历史记录则返回上一页，否则跳转到超级智能体页面
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate('/');
    }
  };

  if (!groupChat) {
    return (
      <div className="flex items-center justify-center h-full theme-bg-primary theme-text-primary">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">群聊不存在</h2>
          <p className="theme-text-secondary">请检查群聊ID是否正确</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full theme-bg-primary theme-text-primary">
      {/* Header */}
      <div className="flex items-center justify-between p-4 theme-border-primary border-b">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 theme-bg-accent rounded-lg flex items-center justify-center">
            <Users className="w-6 h-6 theme-text-accent-foreground" />
          </div>
          <div>
            <h1 className="text-xl font-semibold">{groupChat.name}</h1>
            <p className="text-sm theme-text-secondary">
              {groupChat.agents.length} 个智能体在线
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => navigate('/settings')}
            className="p-2 hover-theme-bg-tertiary rounded-lg transition-colors"
            title="系统设置"
          >
            <Settings className="w-5 h-5" />
          </button>
          <button
            onClick={handleCloseChat}
            className="p-2 hover-theme-bg-tertiary rounded-lg transition-colors"
            title="关闭群聊"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Agents Bar */}
      <div className="flex items-center space-x-2 p-3 theme-bg-secondary theme-border-primary border-b">
        <span className="text-sm theme-text-secondary mr-2">参与智能体:</span>
        {groupChat.agents.map((agent) => (
          <div
            key={agent.id}
            className="flex items-center space-x-1 theme-bg-primary px-2 py-1 rounded text-xs theme-border-primary border"
          >
            <span>{agent.avatar}</span>
            <span>{agent.name}</span>
          </div>
        ))}
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full space-y-4">
            <div className="w-16 h-16 theme-bg-accent rounded-2xl flex items-center justify-center">
              <Users className="w-8 h-8 theme-text-accent-foreground" />
            </div>
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">欢迎来到 {groupChat.name}</h2>
              <p className="theme-text-secondary max-w-md">
                开始与您的智能体团队协作，他们将为您提供专业的帮助和建议。
              </p>
            </div>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[70%] p-3 rounded-lg ${
                    message.sender === 'user'
                      ? 'theme-bg-accent theme-text-accent-foreground'
                      : 'theme-bg-secondary theme-text-primary theme-border-primary border'
                  }`}
                >
                  {message.sender !== 'user' && (
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-lg">{message.senderAvatar}</span>
                      <span className="text-sm font-medium">{message.senderName}</span>
                    </div>
                  )}
                  <p className="whitespace-pre-wrap">{message.content}</p>
                  <div className={`text-xs mt-1 ${
                    message.sender === 'user'
                      ? 'opacity-80'
                      : 'theme-text-tertiary'
                  }`}>
                    {message.timestamp.toLocaleTimeString()}
                  </div>
                </div>
              </div>
            ))}
            {isLoading && (
              <div className="flex justify-start">
                <div className="theme-bg-secondary p-3 rounded-lg theme-border-primary border">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-lg">🤖</span>
                    <span className="text-sm font-medium">智能体正在思考...</span>
                  </div>
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 theme-bg-tertiary rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 theme-bg-tertiary rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 theme-bg-tertiary rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Input Area */}
      <div className="p-4 theme-border-primary border-t">
        <div className="flex items-end space-x-3">
          <div className="flex items-center space-x-2">
            <button className="p-2 hover-theme-bg-tertiary rounded-lg transition-colors">
              <Paperclip className="w-5 h-5 theme-text-secondary" />
            </button>
            <button className="p-2 hover-theme-bg-tertiary rounded-lg transition-colors">
              <Smile className="w-5 h-5 theme-text-secondary" />
            </button>
          </div>
          <div className="flex-1 relative">
            <textarea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="输入消息与智能体团队交流..."
              className="w-full p-3 theme-bg-secondary theme-border-primary border rounded-lg resize-none focus:outline-none focus:theme-border-accent theme-text-primary placeholder-theme-text-tertiary"
              rows={1}
              style={{ minHeight: '44px', maxHeight: '120px' }}
            />
          </div>
          <button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading}
            className="p-3 theme-bg-accent hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg transition-opacity"
          >
            <Send className="w-5 h-5 theme-text-accent-foreground" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default GroupChatDetail;
