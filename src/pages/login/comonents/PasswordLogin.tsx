import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { request } from '@/utils/request';

interface PasswordLoginProps {
  handleLoginSuccess: (token: string) => void;
}

const PasswordLogin: React.FC<PasswordLoginProps> = ({ handleLoginSuccess }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // 用户名密码登录
  const handleLogin = async () => {
    if (!username || !password) {
      alert('请输入用户名和密码');
      return;
    }

    setIsLoading(true);
    try {
      const response = await request('/api/auth/login', {
        method: 'POST',
        body: JSON.stringify({
          username,
          password,
          loginType: 'password'
        })
      });

      console.log('Response received:', response.status, response.statusText);
      const data = await response.json();
      console.log('Response data:', data);

      if (data.success) {
        console.log('Login successful, calling handleLoginSuccess with token:', data.data.token);
        handleLoginSuccess(data.data.token);
      } else {
        console.log('Login failed:', data.message);
        alert(data.message || '登录失败');
      }
    } catch (error) {
      console.error('登录失败:', error);
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
      alert('登录失败: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleLogin();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center p-4">
      <div className="bg-gray-800 rounded-lg shadow-xl p-8 w-full max-w-md border border-gray-700">
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-xl">AG</span>
            </div>
          </div>
          <h1 className="text-2xl font-bold text-white mb-2">AgentGroup</h1>
          <p className="text-gray-400">智能体群聊平台</p>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              用户名
            </label>
            <Input
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="请输入用户名"
              className="w-full bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              密码
            </label>
            <Input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="请输入密码"
              className="w-full bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500"
            />
          </div>

          <Button
            onClick={handleLogin}
            disabled={isLoading}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors"
          >
            {isLoading ? '登录中...' : '登录'}
          </Button>
        </div>

        <div className="mt-6 text-center">
          <div className="text-sm text-gray-400">
            测试账户: admin / admin123
          </div>
        </div>

        <div className="mt-8 pt-6 border-t border-gray-700">
          <div className="text-center text-sm text-gray-400">
            <p>AgentGroup - 智能体群聊平台</p>
            <p className="mt-1">体验多智能体协作的魅力</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PasswordLogin;
