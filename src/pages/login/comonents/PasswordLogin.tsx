import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { request } from '@/utils/request';
import { useTheme } from '@/contexts/ThemeContext';

interface PasswordLoginProps {
  handleLoginSuccess: (token: string) => void;
}

const PasswordLogin: React.FC<PasswordLoginProps> = ({ handleLoginSuccess }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{username?: string; password?: string}>({});
  const [rememberMe, setRememberMe] = useState(false);

  // 安全地使用主题，如果ThemeContext不可用则使用默认值
  let theme = 'dark';
  let toggleTheme = () => {};

  try {
    const themeContext = useTheme();
    theme = themeContext.theme;
    toggleTheme = themeContext.toggleTheme;
  } catch (error) {
    console.warn('ThemeContext not available, using default theme');
  }

  // 表单验证
  const validateForm = () => {
    const newErrors: {username?: string; password?: string} = {};

    if (!username.trim()) {
      newErrors.username = '请输入用户名';
    } else if (username.trim().length < 3) {
      newErrors.username = '用户名至少需要3个字符';
    }

    if (!password) {
      newErrors.password = '请输入密码';
    } else if (password.length < 6) {
      newErrors.password = '密码至少需要6个字符';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 用户名密码登录
  const handleLogin = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await request('/api/auth/login', {
        method: 'POST',
        body: JSON.stringify({
          username,
          password,
          loginType: 'password'
        })
      });

      console.log('Response received:', response.status, response.statusText);
      const data = await response.json();
      console.log('Response data:', data);

      if (data.success) {
        console.log('Login successful, calling handleLoginSuccess with token:', data.data.token);
        handleLoginSuccess(data.data.token);
      } else {
        console.log('Login failed:', data.message);
        setErrors({ password: data.message || '登录失败' });
      }
    } catch (error) {
      console.error('登录失败:', error);
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
      setErrors({ password: '网络错误，请稍后重试' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleLogin();
    }
  };

  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center p-4">
      <div className="bg-gray-800 rounded-lg shadow-xl p-8 w-full max-w-md border border-gray-700 relative" data-testid="login-container">
        {/* 主题切换按钮 */}
        <button
          onClick={toggleTheme}
          className="absolute top-4 right-4 p-2 rounded-lg bg-gray-700 hover:bg-gray-600 transition-colors"
          aria-label="切换主题"
        >
          {theme === 'dark' ? '🌙' : '☀️'}
        </button>
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-xl">AG</span>
            </div>
          </div>
          <h1 className="text-2xl font-bold text-white mb-2">AgentGroup</h1>
          <p className="text-gray-400">智能体群聊平台</p>
        </div>

        <div className="space-y-4">
          <div>
            <label htmlFor="username" className="block text-sm font-medium text-gray-300 mb-2">
              用户名
            </label>
            <Input
              id="username"
              type="text"
              value={username}
              onChange={(e) => {
                setUsername(e.target.value);
                if (errors.username) {
                  setErrors(prev => ({ ...prev, username: undefined }));
                }
              }}
              onKeyPress={handleKeyPress}
              placeholder="请输入用户名"
              className={`w-full bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500 ${
                errors.username ? 'border-red-500' : ''
              }`}
              aria-required="true"
              aria-label="用户名"
              aria-invalid={!!errors.username}
            />
            {errors.username && (
              <p className="mt-1 text-sm text-red-400">{errors.username}</p>
            )}
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-2">
              密码
            </label>
            <Input
              id="password"
              type="password"
              value={password}
              onChange={(e) => {
                setPassword(e.target.value);
                if (errors.password) {
                  setErrors(prev => ({ ...prev, password: undefined }));
                }
              }}
              onKeyPress={handleKeyPress}
              placeholder="请输入密码"
              className={`w-full bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500 ${
                errors.password ? 'border-red-500' : ''
              }`}
              aria-required="true"
              aria-label="密码"
              aria-invalid={!!errors.password}
            />
            {errors.password && (
              <p className="mt-1 text-sm text-red-400">{errors.password}</p>
            )}
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                id="remember-me"
                type="checkbox"
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                aria-label="记住我"
              />
              <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-300">
                记住我
              </label>
            </div>
            <div className="text-sm">
              <a href="#" className="text-blue-400 hover:text-blue-300">
                忘记密码？
              </a>
            </div>
          </div>

          <Button
            onClick={handleLogin}
            disabled={isLoading}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors"
          >
            {isLoading ? '登录中...' : '登录'}
          </Button>
        </div>

        <div className="mt-6 text-center">
          <div className="text-sm text-gray-400">
            测试账户: admin / admin123
          </div>
        </div>
      </div>
    </main>
  );
};

export default PasswordLogin;
