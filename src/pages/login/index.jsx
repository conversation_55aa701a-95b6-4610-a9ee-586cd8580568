import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import PhoneLogin from './comonents/PhoneLogin';
import PasswordLogin from './comonents/PasswordLogin';

export default function Login() {
  const navigate = useNavigate();
  const [loginType, setLoginType] = useState('password'); // 默认使用密码登录

  const handleLoginSuccess = (token) => {
    localStorage.setItem('token', token);
    window.location.href = '/';  // 使用 window.location.href 进行页面跳转
  };

  React.useEffect(() => {
    const isLogin = localStorage.getItem('token');
    if (isLogin) {
      window.location.href = '/';  // 由于是 Vite 多页面，这里使用 window.location.href
    }
  }, []);

  return (
    <div className="login-container">
      {loginType === 'password' ? (
        <PasswordLogin handleLoginSuccess={handleLoginSuccess} />
      ) : (
        <PhoneLogin handleLoginSuccess={handleLoginSuccess} />
      )}
    </div>
  );
}