import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Search, Settings, Users, ChevronLeft, ChevronRight, UserPlus, Shield, User, Building, Trash2, RotateCcw } from 'lucide-react';
import { request } from '@/utils/request';
import { useUserStore } from '@/store/userStore';

interface User {
  id: number;
  username: string;
  email: string;
  nickname: string;
  role: 'admin' | 'department_admin' | 'user';
  department_id?: number;
  balance: number;
  status: number;
  created_at: string;
  updated_at: string;
  source?: 'local' | 'ldap' | 'radius';
  department?: string;
}

interface CreateUserForm {
  username: string;
  password: string;
  email: string;
  nickname: string;
  role: string;
  initial_balance: number;
}

interface CreateUserForm {
  username: string;
  password: string;
  email: string;
  nickname: string;
  role: 'department_admin' | 'user';
  department_id?: string;
  initial_balance: number;
}

interface SSOConfig {
  enabled: boolean;
  type: 'ldap' | 'radius';
  server: string;
  port: number;
  baseDN?: string;
  bindDN?: string;
  bindPassword?: string;
  userFilter?: string;
  secret?: string;
}

export default function UserManagement() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(50);
  const [totalUsers, setTotalUsers] = useState(0);
  const [showSSOConfig, setShowSSOConfig] = useState(false);
  const [showCreateUser, setShowCreateUser] = useState(false);
  const [createUserForm, setCreateUserForm] = useState<CreateUserForm>({
    username: '',
    password: '',
    email: '',
    nickname: '',
    role: 'user',
    initial_balance: 100
  });
  const [ssoConfig, setSSOConfig] = useState<SSOConfig>({
    enabled: false,
    type: 'ldap',
    server: '',
    port: 389,
    baseDN: '',
    bindDN: '',
    bindPassword: '',
    userFilter: '',
    secret: ''
  });
  const userStore = useUserStore();

  // 检查是否为管理员
  const isAdmin = userStore.userInfo?.role === 'admin';

  useEffect(() => {
    fetchUsers();
    fetchSSOConfig();
  }, [currentPage, pageSize, searchTerm]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await request('/api/admin/users', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();

      if (data.success && data.data) {
        // 模拟分页和搜索
        let filteredUsers = Array.isArray(data.data) ? data.data : [];
        if (searchTerm) {
          filteredUsers = filteredUsers.filter((user: User) =>
            user.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            user.nickname?.toLowerCase().includes(searchTerm.toLowerCase())
          );
        }

        setTotalUsers(filteredUsers.length);
        const startIndex = (currentPage - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        setUsers(filteredUsers.slice(startIndex, endIndex));
      } else {
        // API失败或无数据时，显示空列表
        setUsers([]);
        setTotalUsers(0);
      }
    } catch (error) {
      console.error('获取用户列表失败:', error);
      // 网络错误时，显示空列表
      setUsers([]);
      setTotalUsers(0);
    } finally {
      setLoading(false);
    }
  };

  const fetchSSOConfig = async () => {
    try {
      const response = await request('/api/admin/sso-config', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();
      if (data.success) {
        setSSOConfig(data.data);
      }
    } catch (error) {
      console.error('获取SSO配置失败:', error);
    }
  };

  const updateUserRole = async (userId: number, newRole: string) => {
    try {
      const response = await request('/api/admin/users/update-role', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ userId, role: newRole })
      });

      if (response.ok) {
        fetchUsers(); // 重新获取用户列表
      }
    } catch (error) {
      console.error('更新用户角色失败:', error);
    }
  };

  const saveSSOConfig = async () => {
    try {
      const response = await request('/api/admin/sso-config', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(ssoConfig)
      });

      if (response.ok) {
        alert('SSO配置保存成功');
        setShowSSOConfig(false);
      }
    } catch (error) {
      console.error('保存SSO配置失败:', error);
    }
  };

  const syncSSOUsers = async () => {
    try {
      const response = await request('/api/admin/sso-sync', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        alert('SSO用户同步成功');
        fetchUsers();
      }
    } catch (error) {
      console.error('SSO用户同步失败:', error);
    }
  };

  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'admin':
        return <Badge className="bg-red-500 text-white rounded-full min-w-[60px] text-center">管理员</Badge>;
      case 'department_admin':
        return <Badge className="bg-blue-500 text-white rounded-full min-w-[80px] text-center">部门管理员</Badge>;
      default:
        return <Badge className="bg-gray-500 text-white rounded-full min-w-[60px] text-center">普通用户</Badge>;
    }
  };

  const getSourceBadge = (source: string = 'local') => {
    switch (source) {
      case 'ldap':
        return <Badge variant="outline" className="text-green-600 border-green-600">LDAP</Badge>;
      case 'radius':
        return <Badge variant="outline" className="text-blue-600 border-blue-600">RADIUS</Badge>;
      default:
        return <Badge variant="outline" className="text-gray-600 border-gray-600">本地</Badge>;
    }
  };

  const createUser = async () => {
    try {
      const response = await request('/api/admin/users', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(createUserForm)
      });

      if (response.ok) {
        alert('用户创建成功');
        setShowCreateUser(false);
        setCreateUserForm({
          username: '',
          password: '',
          email: '',
          nickname: '',
          role: 'user',
          initial_balance: 100
        });
        fetchUsers();
      } else {
        const data = await response.json();
        alert(data.message || '用户创建失败');
      }
    } catch (error) {
      console.error('用户创建失败:', error);
      alert('用户创建失败');
    }
  };

  const deleteUser = async (userId: number) => {
    if (!confirm('确定要删除这个用户吗？')) return;

    try {
      const response = await request(`/api/admin/users/${userId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        alert('用户删除成功');
        fetchUsers();
      } else {
        const data = await response.json();
        alert(data.message || '用户删除失败');
      }
    } catch (error) {
      console.error('用户删除失败:', error);
      alert('用户删除失败');
    }
  };

  const resetPassword = async (userId: number) => {
    const newPassword = prompt('请输入新密码:');
    if (!newPassword) return;

    try {
      const response = await request(`/api/admin/users/${userId}/reset-password`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ newPassword })
      });

      if (response.ok) {
        alert('密码重置成功');
      } else {
        const data = await response.json();
        alert(data.message || '密码重置失败');
      }
    } catch (error) {
      console.error('密码重置失败:', error);
      alert('密码重置失败');
    }
  };

  const totalPages = Math.ceil(totalUsers / pageSize);

  return (
    <main className="min-h-screen theme-bg-primary" role="main" aria-label="用户管理">
      <div className="container mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-3">
            <Users className="h-8 w-8 text-green-500" />
            <h1 className="text-3xl font-bold theme-text-primary">用户管理</h1>
          </div>
          <div className="flex gap-3">
            <Button
              onClick={() => setShowCreateUser(true)}
              aria-label="添加用户"
              className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white"
            >
              <UserPlus className="h-4 w-4" />
              添加用户
            </Button>
            <Button
              variant="outline"
              onClick={() => setShowSSOConfig(true)}
              className="flex items-center gap-2"
            >
              <Settings className="h-4 w-4" />
              SSO配置
            </Button>
            {ssoConfig.enabled && (
              <Button
                onClick={syncSSOUsers}
                className="flex items-center gap-2"
              >
                <UserPlus className="h-4 w-4" />
                同步SSO用户
              </Button>
            )}
          </div>
        </div>

        {/* 搜索和筛选 */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
              <div className="flex items-center gap-2 flex-1">
                <Search className="h-4 w-4 text-gray-400" />
                <Input
                  role="searchbox"
                  aria-label="搜索用户"
                  placeholder="搜索用户名、邮箱或昵称..."
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value);
                    setCurrentPage(1);
                  }}
                  className="max-w-md"
                />
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm theme-text-secondary">每页显示:</span>
                <Select value={pageSize.toString()} onValueChange={(value) => {
                  setPageSize(Number(value));
                  setCurrentPage(1);
                }}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="15">15</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                    <SelectItem value="100">100</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 用户表格 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>用户列表 ({totalUsers} 个用户)</span>
              <div className="flex items-center gap-4 text-sm theme-text-secondary">
                <span>第 {currentPage} 页，共 {totalPages} 页</span>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              </div>
            ) : (
              <>
                <Table role="table" data-testid="user-list">
                  <TableHeader>
                    <TableRow>
                      <TableHead>ID</TableHead>
                      <TableHead>用户名</TableHead>
                      <TableHead>邮箱</TableHead>
                      <TableHead>昵称</TableHead>
                      <TableHead>角色</TableHead>
                      <TableHead>来源</TableHead>
                      <TableHead>余额</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {users.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>{user.id}</TableCell>
                        <TableCell className="font-medium">{user.username}</TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>{user.nickname}</TableCell>
                        <TableCell>{getRoleBadge(user.role)}</TableCell>
                        <TableCell>{getSourceBadge(user.source)}</TableCell>
                        <TableCell>{user.balance.toLocaleString()} 字币</TableCell>
                        <TableCell>
                          <Badge variant={user.status === 1 ? "default" : "secondary"}>
                            {user.status === 1 ? "正常" : "禁用"}
                          </Badge>
                        </TableCell>
                        <TableCell>{new Date(user.created_at).toLocaleDateString()}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {userStore.userInfo?.role === 'admin' && user.role !== 'admin' && (
                              <>
                                <Select
                                  value={user.role}
                                  onValueChange={(value) => updateUserRole(user.id, value)}
                                >
                                  <SelectTrigger className="w-32">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="user">普通用户</SelectItem>
                                    <SelectItem value="department_admin">部门管理员</SelectItem>
                                  </SelectContent>
                                </Select>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => resetPassword(user.id)}
                                  className="p-2"
                                  title="重置密码"
                                >
                                  <RotateCcw className="h-4 w-4" />
                                </Button>
                                {user.source === 'local' && (
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => deleteUser(user.id)}
                                    className="p-2 text-red-600 hover:text-red-700 hover:bg-red-50"
                                    title="删除用户"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                )}
                              </>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* 分页控件 */}
                <div className="flex items-center justify-between mt-6">
                  <div className="text-sm theme-text-secondary">
                    显示 {(currentPage - 1) * pageSize + 1} 到 {Math.min(currentPage * pageSize, totalUsers)} 条，共 {totalUsers} 条记录
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                    >
                      <ChevronLeft className="h-4 w-4" />
                      上一页
                    </Button>
                    <span className="px-3 py-1 text-sm">
                      {currentPage} / {totalPages}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                    >
                      下一页
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* SSO配置对话框 */}
        {showSSOConfig && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <Card className="w-full max-w-2xl max-h-[80vh] overflow-y-auto">
              <CardHeader>
                <CardTitle>SSO配置</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="sso-enabled"
                    checked={ssoConfig.enabled}
                    onChange={(e) => setSSOConfig(prev => ({ ...prev, enabled: e.target.checked }))}
                  />
                  <label htmlFor="sso-enabled">启用SSO</label>
                </div>

                {ssoConfig.enabled && (
                  <>
                    <div>
                      <label className="block text-sm font-medium mb-1">SSO类型</label>
                      <Select
                        value={ssoConfig.type}
                        onValueChange={(value: 'ldap' | 'radius') =>
                          setSSOConfig(prev => ({ ...prev, type: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="ldap">LDAP</SelectItem>
                          <SelectItem value="radius">RADIUS</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">服务器地址</label>
                      <Input
                        value={ssoConfig.server}
                        onChange={(e) => setSSOConfig(prev => ({ ...prev, server: e.target.value }))}
                        placeholder="ldap://example.com 或 radius.example.com"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">端口</label>
                      <Input
                        type="number"
                        value={ssoConfig.port}
                        onChange={(e) => setSSOConfig(prev => ({ ...prev, port: Number(e.target.value) }))}
                        placeholder={ssoConfig.type === 'ldap' ? '389' : '1812'}
                      />
                    </div>

                    {ssoConfig.type === 'ldap' && (
                      <>
                        <div>
                          <label className="block text-sm font-medium mb-1">Base DN</label>
                          <Input
                            value={ssoConfig.baseDN}
                            onChange={(e) => setSSOConfig(prev => ({ ...prev, baseDN: e.target.value }))}
                            placeholder="dc=example,dc=com"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium mb-1">Bind DN</label>
                          <Input
                            value={ssoConfig.bindDN}
                            onChange={(e) => setSSOConfig(prev => ({ ...prev, bindDN: e.target.value }))}
                            placeholder="cn=admin,dc=example,dc=com"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium mb-1">Bind Password</label>
                          <Input
                            type="password"
                            value={ssoConfig.bindPassword}
                            onChange={(e) => setSSOConfig(prev => ({ ...prev, bindPassword: e.target.value }))}
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium mb-1">用户过滤器</label>
                          <Input
                            value={ssoConfig.userFilter}
                            onChange={(e) => setSSOConfig(prev => ({ ...prev, userFilter: e.target.value }))}
                            placeholder="(objectClass=person)"
                          />
                        </div>
                      </>
                    )}

                    {ssoConfig.type === 'radius' && (
                      <div>
                        <label className="block text-sm font-medium mb-1">共享密钥</label>
                        <Input
                          type="password"
                          value={ssoConfig.secret}
                          onChange={(e) => setSSOConfig(prev => ({ ...prev, secret: e.target.value }))}
                          placeholder="RADIUS共享密钥"
                        />
                      </div>
                    )}
                  </>
                )}

                <div className="flex justify-end gap-2 pt-4">
                  <Button variant="outline" onClick={() => setShowSSOConfig(false)}>
                    取消
                  </Button>
                  <Button onClick={saveSSOConfig}>
                    保存配置
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* 创建用户对话框 */}
        <Dialog open={showCreateUser} onOpenChange={setShowCreateUser}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>新增用户</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="username">用户名</Label>
                <Input
                  id="username"
                  value={createUserForm.username}
                  onChange={(e) => setCreateUserForm(prev => ({ ...prev, username: e.target.value }))}
                  placeholder="请输入用户名"
                />
              </div>
              <div>
                <Label htmlFor="password">密码</Label>
                <Input
                  id="password"
                  type="password"
                  value={createUserForm.password}
                  onChange={(e) => setCreateUserForm(prev => ({ ...prev, password: e.target.value }))}
                  placeholder="请输入密码"
                />
              </div>
              <div>
                <Label htmlFor="email">邮箱</Label>
                <Input
                  id="email"
                  type="email"
                  value={createUserForm.email}
                  onChange={(e) => setCreateUserForm(prev => ({ ...prev, email: e.target.value }))}
                  placeholder="请输入邮箱"
                />
              </div>
              <div>
                <Label htmlFor="nickname">昵称</Label>
                <Input
                  id="nickname"
                  value={createUserForm.nickname}
                  onChange={(e) => setCreateUserForm(prev => ({ ...prev, nickname: e.target.value }))}
                  placeholder="请输入昵称"
                />
              </div>
              <div>
                <Label htmlFor="role">角色</Label>
                <Select value={createUserForm.role} onValueChange={(value) => setCreateUserForm(prev => ({ ...prev, role: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="user">普通用户</SelectItem>
                    <SelectItem value="department_admin">部门管理员</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="balance">初始余额</Label>
                <Input
                  id="balance"
                  type="number"
                  value={createUserForm.initial_balance}
                  onChange={(e) => setCreateUserForm(prev => ({ ...prev, initial_balance: Number(e.target.value) }))}
                  placeholder="100"
                />
              </div>
              <div className="flex justify-end gap-2 pt-4">
                <Button variant="outline" onClick={() => setShowCreateUser(false)}>
                  取消
                </Button>
                <Button onClick={createUser}>
                  创建用户
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </main>
  );
}
