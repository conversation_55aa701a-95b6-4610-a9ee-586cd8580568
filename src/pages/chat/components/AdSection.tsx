import React from 'react';
import { cn } from '@/lib/utils';

interface AdSectionProps {
  isOpen: boolean;
  closeAd?: () => void;
}

const AdSection: React.FC<AdSectionProps> = ({ isOpen }) => {
  if (!isOpen) return null;

  return (
    <div className="p-3 border-t border-border/40">
      <div className={cn(
        "rounded-lg p-4 text-center relative overflow-hidden min-h-[80px] flex flex-col justify-center",
        "transition-all duration-200 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20",
        "border border-border/50"
      )}
      >
        <div className="relative z-10">
          <div className="flex flex-col items-center gap-3">
            <div className="text-lg font-semibold text-gray-700 dark:text-gray-300">
              🤖 AgentGroup
            </div>
            <div className="text-sm text-center text-gray-600 dark:text-gray-400">
              智能体群聊平台 - 让AI协作更智能
            </div>
            <div className="text-xs text-center text-gray-500 dark:text-gray-500">
              支持多Agent协作，智能任务分配
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// 简化的Banner组件
const AdBanner: React.FC<AdSectionProps> = ({ isOpen }) => {
  if (!isOpen) return null;

  return (
    <div className="rounded-lg text-center relative overflow-hidden py-2 px-4 h-8 mr-2 flex items-center justify-center transition-all duration-200 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-border/50">
      <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
        🤖 AgentGroup
      </div>
    </div>
  );
};

export { AdSection, AdBanner };