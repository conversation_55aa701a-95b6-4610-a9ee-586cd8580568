import React, { useState, useEffect } from 'react';
import { cn } from "@/lib/utils";
import { X, User, CreditCard, Settings, History, Wallet } from 'lucide-react';
import { request } from '@/utils/request';
import { useUserStore } from '@/store/userStore';

interface PersonalCenterDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

interface UserBalance {
  balance: number;
  stats: {
    total_income: number;
    total_expense: number;
    transaction_count: number;
  };
  transactions?: {
    records: Array<{
      id: string;
      transaction_type: string;
      amount: number;
      balance_after: number;
      description: string;
      created_at: string;
    }>;
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}

export const PersonalCenterDialog: React.FC<PersonalCenterDialogProps> = ({ isOpen, onClose }) => {
  const [activeTab, setActiveTab] = useState('profile');
  const [userBalance, setUserBalance] = useState<UserBalance | null>(null);
  const [loading, setLoading] = useState(false);
  const [rechargeAmount, setRechargeAmount] = useState('');
  const [recharging, setRecharging] = useState(false);
  const userStore = useUserStore();

  // 获取用户余额信息
  const fetchUserBalance = async () => {
    try {
      setLoading(true);
      const response = await request('/api/billing/balance?include_transactions=true');
      if (response.success) {
        setUserBalance(response.data);
      }
    } catch (error) {
      console.error('获取余额失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 充值
  const handleRecharge = async () => {
    if (!rechargeAmount || parseFloat(rechargeAmount) <= 0) {
      alert('请输入有效的充值金额');
      return;
    }

    try {
      setRecharging(true);
      const response = await request('/api/billing/recharge', {
        method: 'POST',
        body: JSON.stringify({
          amount: parseFloat(rechargeAmount),
          payment_method: 'internal'
        })
      });

      if (response.success) {
        alert('充值成功！');
        setRechargeAmount('');
        fetchUserBalance(); // 刷新余额
      } else {
        alert(response.message || '充值失败');
      }
    } catch (error) {
      console.error('充值失败:', error);
      alert('充值失败，请重试');
    } finally {
      setRecharging(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchUserBalance();
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const tabs = [
    { id: 'profile', name: '个人信息', icon: User },
    { id: 'wallet', name: '我的钱包', icon: Wallet },
    { id: 'recharge', name: '充值中心', icon: CreditCard },
    { id: 'history', name: '交易记录', icon: History },
    { id: 'settings', name: '系统设置', icon: Settings },
  ];

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg w-full max-w-4xl h-[80vh] flex flex-col border border-gray-700">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <h2 className="text-xl font-semibold text-white">个人中心</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="flex flex-1 overflow-hidden">
          {/* Sidebar */}
          <div className="w-64 bg-gray-900 border-r border-gray-700">
            <nav className="p-4 space-y-2">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={cn(
                      "w-full flex items-center gap-3 px-3 py-2 rounded-md text-left transition-colors",
                      activeTab === tab.id
                        ? "bg-blue-600 text-white"
                        : "text-gray-300 hover:bg-gray-700 hover:text-white"
                    )}
                  >
                    <Icon className="w-5 h-5" />
                    {tab.name}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Content */}
          <div className="flex-1 p-6 overflow-y-auto">
            {activeTab === 'profile' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-white">个人信息</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">用户名</label>
                    <div className="bg-gray-700 px-3 py-2 rounded-md text-white">
                      {userStore.userInfo?.username || '未设置'}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">昵称</label>
                    <div className="bg-gray-700 px-3 py-2 rounded-md text-white">
                      {userStore.userInfo?.nickname || '未设置'}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">角色</label>
                    <div className="bg-gray-700 px-3 py-2 rounded-md text-white">
                      {userStore.userInfo?.role === 'admin' ? '管理员' : 
                       userStore.userInfo?.role === 'department_admin' ? '部门管理员' : '普通用户'}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">邮箱</label>
                    <div className="bg-gray-700 px-3 py-2 rounded-md text-white">
                      {userStore.userInfo?.email || '未设置'}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'wallet' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-white">我的钱包</h3>
                {loading ? (
                  <div className="text-center text-gray-400">加载中...</div>
                ) : userBalance ? (
                  <div className="space-y-4">
                    <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6 rounded-lg">
                      <div className="text-white">
                        <div className="text-sm opacity-80">当前余额</div>
                        <div className="text-3xl font-bold">{userBalance.balance.toFixed(2)} 字节币</div>
                      </div>
                    </div>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="bg-gray-700 p-4 rounded-lg">
                        <div className="text-gray-300 text-sm">总收入</div>
                        <div className="text-white text-xl font-semibold">
                          {userBalance.stats.total_income.toFixed(2)}
                        </div>
                      </div>
                      <div className="bg-gray-700 p-4 rounded-lg">
                        <div className="text-gray-300 text-sm">总支出</div>
                        <div className="text-white text-xl font-semibold">
                          {userBalance.stats.total_expense.toFixed(2)}
                        </div>
                      </div>
                      <div className="bg-gray-700 p-4 rounded-lg">
                        <div className="text-gray-300 text-sm">交易次数</div>
                        <div className="text-white text-xl font-semibold">
                          {userBalance.stats.transaction_count}
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center text-gray-400">暂无数据</div>
                )}
              </div>
            )}

            {activeTab === 'recharge' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-white">充值中心</h3>
                <div className="bg-gray-700 p-6 rounded-lg">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">充值金额</label>
                      <input
                        type="number"
                        value={rechargeAmount}
                        onChange={(e) => setRechargeAmount(e.target.value)}
                        placeholder="请输入充值金额"
                        className="w-full bg-gray-600 text-white px-3 py-2 rounded-md border border-gray-500 focus:border-blue-500 focus:outline-none"
                      />
                    </div>
                    <button
                      onClick={handleRecharge}
                      disabled={recharging}
                      className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white py-2 px-4 rounded-md transition-colors"
                    >
                      {recharging ? '充值中...' : '立即充值'}
                    </button>
                    <div className="text-sm text-gray-400">
                      * 当前仅支持管理员内部充值
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'history' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-white">交易记录</h3>
                {loading ? (
                  <div className="text-center text-gray-400">加载中...</div>
                ) : userBalance?.transactions ? (
                  <div className="space-y-2">
                    {userBalance.transactions.records.map((tx) => (
                      <div key={tx.id} className="bg-gray-700 p-4 rounded-lg flex justify-between items-center">
                        <div>
                          <div className="text-white font-medium">{tx.description}</div>
                          <div className="text-gray-400 text-sm">
                            {new Date(tx.created_at).toLocaleString()}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className={cn(
                            "font-semibold",
                            tx.amount > 0 ? "text-green-400" : "text-red-400"
                          )}>
                            {tx.amount > 0 ? '+' : ''}{tx.amount.toFixed(2)}
                          </div>
                          <div className="text-gray-400 text-sm">
                            余额: {tx.balance_after.toFixed(2)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center text-gray-400">暂无交易记录</div>
                )}
              </div>
            )}

            {activeTab === 'settings' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-white">系统设置</h3>
                <div className="text-gray-400">系统设置功能开发中...</div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
