import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Button
} from '@/components/ui/button';
import {
  Input
} from '@/components/ui/input';
import {
  Label
} from '@/components/ui/label';
import {
  Textarea
} from '@/components/ui/textarea';
import {
  Checkbox
} from '@/components/ui/checkbox';
import {
  Avatar,
  AvatarFallback,
  AvatarImage
} from '@/components/ui/avatar';
import {
  Badge
} from '@/components/ui/badge';
import {
  ScrollArea
} from '@/components/ui/scroll-area';
import { Crown, Users, Bot, Plus } from 'lucide-react';
import { toast } from 'sonner';
import { request } from '@/utils/request';

interface Agent {
  id: string;
  name: string;
  description: string;
  avatar?: string;
  is_super_agent?: boolean;
  capabilities?: string[];
  status: number;
}

interface User {
  id: string;
  username: string;
  email?: string;
  avatar?: string;
  display_name?: string;
  status: number;
}

interface CreateGroupDialogProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export const CreateGroupDialog: React.FC<CreateGroupDialogProps> = ({
  open,
  onClose,
  onSuccess
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    group_type: 'mixed', // mixed, user_only, agent_only
    max_members: 50,
    agent_ids: [] as string[],
    user_ids: [] as string[]
  });
  
  const [agents, setAgents] = useState<Agent[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingAgents, setLoadingAgents] = useState(false);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [userSearchQuery, setUserSearchQuery] = useState('');

  // 加载智能体列表
  const loadAgents = async () => {
    try {
      setLoadingAgents(true);
      const response = await request('/api/agents/list');
      const data = await response.json();
      
      if (data.success) {
        setAgents(data.data.agents || []);
        
        // 自动选择超级智能体
        const superAgent = data.data.agents?.find((agent: Agent) => agent.is_super_agent);
        if (superAgent) {
          setFormData(prev => ({
            ...prev,
            agent_ids: [superAgent.id]
          }));
        }
      } else {
        toast.error('加载智能体列表失败');
      }
    } catch (error) {
      console.error('加载智能体列表错误:', error);
      toast.error('网络错误，请稍后重试');
    } finally {
      setLoadingAgents(false);
    }
  };

  useEffect(() => {
    if (open) {
      loadAgents();
    }
  }, [open]);

  // 处理表单输入
  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 搜索用户
  const searchUsers = async (query: string) => {
    if (!query.trim()) {
      setUsers([]);
      return;
    }

    try {
      setLoadingUsers(true);
      const response = await request(`/api/users/search?q=${encodeURIComponent(query)}`);
      const data = await response.json();

      if (data.success) {
        setUsers(data.data.users || []);
      } else {
        toast.error('搜索用户失败');
      }
    } catch (error) {
      console.error('搜索用户错误:', error);
      toast.error('网络错误，请稍后重试');
    } finally {
      setLoadingUsers(false);
    }
  };

  // 处理用户搜索输入
  const handleUserSearch = (query: string) => {
    setUserSearchQuery(query);
    // 防抖搜索
    const timeoutId = setTimeout(() => {
      searchUsers(query);
    }, 300);

    return () => clearTimeout(timeoutId);
  };

  // 处理智能体选择
  const handleAgentToggle = (agentId: string, isSuper: boolean = false) => {
    if (isSuper) {
      // 超级智能体不能取消选择
      return;
    }
    
    setFormData(prev => ({
      ...prev,
      agent_ids: prev.agent_ids.includes(agentId)
        ? prev.agent_ids.filter(id => id !== agentId)
        : [...prev.agent_ids, agentId]
    }));
  };

  // 处理用户选择
  const handleUserToggle = (userId: string) => {
    setFormData(prev => ({
      ...prev,
      user_ids: prev.user_ids.includes(userId)
        ? prev.user_ids.filter(id => id !== userId)
        : [...prev.user_ids, userId]
    }));
  };

  // 提交创建群聊
  const handleSubmit = async () => {
    if (!formData.name.trim()) {
      toast.error('请输入群聊名称');
      return;
    }

    if (formData.agent_ids.length === 0) {
      toast.error('请至少选择一个智能体');
      return;
    }

    setLoading(true);
    try {
      const response = await request('/api/groups/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success('群聊创建成功！');
        onSuccess();
        onClose();
        // 重置表单
        setFormData({
          name: '',
          description: '',
          group_type: 'mixed',
          max_members: 50,
          agent_ids: [],
          user_ids: []
        });
      } else {
        toast.error(data.message || '群聊创建失败');
      }
    } catch (error) {
      console.error('创建群聊错误:', error);
      toast.error('网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 获取智能体头像
  const getAgentAvatar = (agent: Agent) => {
    if (agent.avatar) {
      return <AvatarImage src={agent.avatar} />;
    }
    return (
      <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white">
        {agent.name[0]}
      </AvatarFallback>
    );
  };

  // 获取用户头像
  const getUserAvatar = (user: User) => {
    if (user.avatar) {
      return <AvatarImage src={user.avatar} />;
    }
    return (
      <AvatarFallback className="bg-gradient-to-br from-green-500 to-blue-600 text-white">
        {(user.display_name || user.username).charAt(0).toUpperCase()}
      </AvatarFallback>
    );
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto theme-bg-primary theme-border-primary theme-text-primary">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold theme-text-primary">创建新群聊</DialogTitle>
          <DialogDescription className="theme-text-secondary">
            配置群聊信息并选择要加入的智能体
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* 基本信息 */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name" className="theme-text-primary">群聊名称 *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="输入群聊名称"
                className="theme-bg-secondary theme-border-primary theme-text-primary placeholder-theme-text-tertiary focus:outline-none focus:ring-0 focus:border-theme-primary"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description" className="theme-text-primary">群聊描述</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="描述群聊的用途和规则"
                rows={3}
                className="theme-bg-secondary theme-border-primary theme-text-primary placeholder-theme-text-tertiary"
              />
            </div>
          </div>

          {/* 智能体选择 */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Bot className="h-5 w-5 text-blue-400" />
              <Label className="text-white font-medium">选择智能体</Label>
            </div>
            
            {loadingAgents ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                <p className="mt-4 text-gray-400">加载智能体列表...</p>
              </div>
            ) : (
              <ScrollArea className="h-64 border border-gray-700 rounded-lg p-4 bg-gray-800/50">
                <div className="space-y-3">
                  {agents.map((agent) => (
                    <div
                      key={agent.id}
                      className={`flex items-center gap-3 p-3 rounded-lg border transition-all cursor-pointer ${
                        formData.agent_ids.includes(agent.id)
                          ? 'border-blue-500 bg-blue-500/10'
                          : 'border-gray-600 hover:border-gray-500 bg-gray-800/30'
                      }`}
                      onClick={() => handleAgentToggle(agent.id, agent.is_super_agent)}
                    >
                      <Checkbox
                        checked={formData.agent_ids.includes(agent.id)}
                        disabled={agent.is_super_agent}
                        className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                      />
                      
                      <Avatar className="h-10 w-10">
                        {getAgentAvatar(agent)}
                      </Avatar>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium text-white truncate">{agent.name}</h4>
                          {agent.is_super_agent && (
                            <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
                              <Crown className="h-3 w-3 mr-1" />
                              超级智能体
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-400 truncate">{agent.description}</p>
                        {agent.capabilities && (
                          <div className="flex flex-wrap gap-1 mt-1">
                            {agent.capabilities.slice(0, 3).map((cap, index) => (
                              <Badge key={index} variant="outline" className="text-xs border-gray-600 text-gray-300">
                                {cap}
                              </Badge>
                            ))}
                            {agent.capabilities.length > 3 && (
                              <Badge variant="outline" className="text-xs border-gray-600 text-gray-300">
                                +{agent.capabilities.length - 3}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            )}
            
            <p className="text-sm text-gray-400">
              * 超级智能体将自动加入群聊，负责协调和管理
            </p>
          </div>

          {/* 用户选择 */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-green-400" />
              <Label className="text-white font-medium">邀请用户</Label>
            </div>

            <div className="space-y-3">
              <Input
                value={userSearchQuery}
                onChange={(e) => handleUserSearch(e.target.value)}
                placeholder="搜索用户名或邮箱..."
                className="theme-bg-secondary theme-border-primary theme-text-primary placeholder-theme-text-tertiary focus:outline-none focus:ring-0 focus:border-theme-primary"
              />

              {loadingUsers ? (
                <div className="text-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-500 mx-auto"></div>
                  <p className="mt-2 text-gray-400 text-sm">搜索中...</p>
                </div>
              ) : users.length > 0 ? (
                <ScrollArea className="h-48 border border-gray-700 rounded-lg p-3 bg-gray-800/50">
                  <div className="space-y-2">
                    {users.map((user) => (
                      <div
                        key={user.id}
                        className={`flex items-center gap-3 p-2 rounded-lg border transition-all cursor-pointer ${
                          formData.user_ids.includes(user.id)
                            ? 'border-green-500 bg-green-500/10'
                            : 'border-gray-600 hover:border-gray-500 bg-gray-800/30'
                        }`}
                        onClick={() => handleUserToggle(user.id)}
                      >
                        <Checkbox
                          checked={formData.user_ids.includes(user.id)}
                          className="data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600"
                        />

                        <Avatar className="h-8 w-8">
                          {getUserAvatar(user)}
                        </Avatar>

                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-white truncate">
                            {user.display_name || user.username}
                          </h4>
                          {user.email && (
                            <p className="text-sm text-gray-400 truncate">{user.email}</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              ) : userSearchQuery.trim() ? (
                <div className="text-center py-4 text-gray-400 text-sm">
                  未找到匹配的用户
                </div>
              ) : (
                <div className="text-center py-4 text-gray-400 text-sm">
                  输入用户名或邮箱进行搜索
                </div>
              )}
            </div>

            <p className="text-sm text-gray-400">
              * 可选择真人用户加入群聊，创建混合群聊
            </p>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end gap-3 pt-4 border-t theme-border-primary">
            <Button
              variant="outline"
              onClick={onClose}
              className="theme-border-primary theme-text-secondary hover-theme-bg-tertiary"
            >
              取消
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={loading || !formData.name.trim() || formData.agent_ids.length === 0}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  创建中...
                </>
              ) : (
                <>
                  <Plus className="h-4 w-4 mr-2" />
                  创建群聊
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
