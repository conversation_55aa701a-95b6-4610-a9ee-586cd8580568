import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import { BarChartIcon, TrendingUpIcon, UsersIcon, MessageSquareIcon, ClockIcon, StarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import { request } from '@/lib/request';

interface UserWeight {
  user_id: string;
  username: string;
  message_count: number;
  total_chars: number;
  interaction_score: number;
  last_active: string;
  weight_score: number;
  priority_level: 'high' | 'medium' | 'low';
}

interface WeightAnalysisResult {
  group_id: number;
  total_users: number;
  analysis_time: string;
  user_weights: UserWeight[];
  recommendations: string[];
}

interface WeightAnalysisDialogProps {
  open: boolean;
  onClose: () => void;
  groupId: number;
  groupName: string;
}

export const WeightAnalysisDialog: React.FC<WeightAnalysisDialogProps> = ({
  open,
  onClose,
  groupId,
  groupName
}) => {
  const [analysisData, setAnalysisData] = useState<WeightAnalysisResult | null>(null);
  const [loading, setLoading] = useState(false);

  // 加载权重分析数据
  const loadWeightAnalysis = async () => {
    if (!open || !groupId) return;

    setLoading(true);
    try {
      const response = await request(`/api/groups/${groupId}/weights`, {
        method: 'GET',
      });

      const data = await response.json();
      if (data.success) {
        setAnalysisData(data.data);
      } else {
        toast.error(data.message || '加载权重分析失败');
      }
    } catch (error) {
      console.error('加载权重分析失败:', error);
      toast.error('加载权重分析失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadWeightAnalysis();
  }, [open, groupId]);

  // 获取优先级颜色
  const getPriorityColor = (level: string) => {
    switch (level) {
      case 'high':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'low':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  // 获取优先级显示名称
  const getPriorityName = (level: string) => {
    switch (level) {
      case 'high':
        return '高活跃';
      case 'medium':
        return '中活跃';
      case 'low':
        return '低活跃';
      default:
        return '未知';
    }
  };

  // 获取权重分数的进度条颜色
  const getScoreColor = (score: number) => {
    if (score >= 30) return 'bg-green-500';
    if (score >= 15) return 'bg-yellow-500';
    return 'bg-gray-400';
  };

  if (!analysisData) {
    return (
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl">
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-gray-500">分析中...</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  const maxScore = Math.max(...analysisData.user_weights.map(u => u.weight_score), 1);

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BarChartIcon className="h-5 w-5" />
            用户参与度权重分析 - {groupName}
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-auto space-y-6">
          {/* 概览统计 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
              <div className="flex items-center gap-2">
                <UsersIcon className="h-5 w-5 text-blue-500" />
                <span className="text-sm font-medium">总用户数</span>
              </div>
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {analysisData.total_users}
              </div>
            </div>

            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
              <div className="flex items-center gap-2">
                <TrendingUpIcon className="h-5 w-5 text-green-500" />
                <span className="text-sm font-medium">高活跃用户</span>
              </div>
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {analysisData.user_weights.filter(u => u.priority_level === 'high').length}
              </div>
            </div>

            <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
              <div className="flex items-center gap-2">
                <MessageSquareIcon className="h-5 w-5 text-yellow-500" />
                <span className="text-sm font-medium">总消息数</span>
              </div>
              <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
                {analysisData.user_weights.reduce((sum, u) => sum + u.message_count, 0)}
              </div>
            </div>

            <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
              <div className="flex items-center gap-2">
                <ClockIcon className="h-5 w-5 text-purple-500" />
                <span className="text-sm font-medium">分析时间</span>
              </div>
              <div className="text-sm font-medium text-purple-600 dark:text-purple-400">
                {new Date(analysisData.analysis_time).toLocaleString()}
              </div>
            </div>
          </div>

          {/* 用户权重列表 */}
          <div className="space-y-3">
            <h3 className="font-medium text-lg flex items-center gap-2">
              <StarIcon className="h-5 w-5 text-yellow-500" />
              用户权重排名
            </h3>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {analysisData.user_weights.map((user, index) => (
                <div
                  key={user.user_id}
                  className="border rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800"
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-medium">
                        #{index + 1}
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{user.username}</span>
                          <Badge className={cn("text-xs", getPriorityColor(user.priority_level))}>
                            {getPriorityName(user.priority_level)}
                          </Badge>
                        </div>
                        <div className="text-sm text-gray-500">
                          最后活跃: {new Date(user.last_active).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
                        {user.weight_score}
                      </div>
                      <div className="text-xs text-gray-500">权重分数</div>
                    </div>
                  </div>

                  {/* 权重分数进度条 */}
                  <div className="mb-3">
                    <div className="flex justify-between text-sm mb-1">
                      <span>权重分数</span>
                      <span>{user.weight_score}/{maxScore.toFixed(1)}</span>
                    </div>
                    <Progress 
                      value={(user.weight_score / maxScore) * 100} 
                      className="h-2"
                    />
                  </div>

                  {/* 详细指标 */}
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div className="text-center">
                      <div className="font-medium text-blue-600 dark:text-blue-400">
                        {user.message_count}
                      </div>
                      <div className="text-gray-500">消息数</div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-green-600 dark:text-green-400">
                        {user.total_chars}
                      </div>
                      <div className="text-gray-500">字符数</div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-purple-600 dark:text-purple-400">
                        {user.interaction_score.toFixed(1)}
                      </div>
                      <div className="text-gray-500">互动分</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 智能建议 */}
          <div className="space-y-3">
            <h3 className="font-medium text-lg">智能建议</h3>
            <div className="space-y-2">
              {analysisData.recommendations.map((recommendation, index) => (
                <div
                  key={index}
                  className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border-l-4 border-blue-500"
                >
                  <p className="text-sm">{recommendation}</p>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={onClose}>
            关闭
          </Button>
          <Button onClick={loadWeightAnalysis} disabled={loading}>
            {loading ? '刷新中...' : '刷新分析'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
