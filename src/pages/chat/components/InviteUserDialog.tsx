import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { SearchIcon, UserPlusIcon, XIcon, CheckIcon, AlertCircleIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import { request } from '@/lib/request';

interface User {
  id: string;
  nickname: string;
  phone?: string;
  avatar_url?: string;
  status: number;
}

interface InviteResult {
  invitee: string;
  success: boolean;
  message: string;
  invitation_id?: string;
}

interface InviteUserDialogProps {
  open: boolean;
  onClose: () => void;
  groupId: number;
  groupName: string;
  onInviteSuccess: () => void;
}

export const InviteUserDialog: React.FC<InviteUserDialogProps> = ({
  open,
  onClose,
  groupId,
  groupName,
  onInviteSuccess
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [inviteMessage, setInviteMessage] = useState('');
  const [role, setRole] = useState('member');
  const [loading, setLoading] = useState(false);
  const [searching, setSearching] = useState(false);
  const [inviteResults, setInviteResults] = useState<InviteResult[]>([]);

  // 初始化邀请消息
  useEffect(() => {
    if (open) {
      setInviteMessage(`邀请您加入群聊"${groupName}"`);
      setSelectedUsers([]);
      setSearchResults([]);
      setSearchQuery('');
      setInviteResults([]);
    }
  }, [open, groupName]);

  // 搜索用户
  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      return;
    }

    setSearching(true);
    try {
      const response = await request(`/api/users/search?q=${encodeURIComponent(searchQuery)}`, {
        method: 'GET',
      });

      const data = await response.json();
      if (data.success) {
        setSearchResults(data.data || []);
      } else {
        toast.error(data.message || '搜索用户失败');
      }
    } catch (error) {
      console.error('搜索用户失败:', error);
      toast.error('搜索用户失败，请重试');
    } finally {
      setSearching(false);
    }
  };

  // 添加用户到选择列表
  const handleSelectUser = (userId: string) => {
    if (!selectedUsers.includes(userId)) {
      setSelectedUsers([...selectedUsers, userId]);
    }
  };

  // 从选择列表移除用户
  const handleRemoveUser = (userId: string) => {
    setSelectedUsers(selectedUsers.filter(id => id !== userId));
  };

  // 发送邀请
  const handleSendInvites = async () => {
    if (selectedUsers.length === 0) {
      toast.error('请选择要邀请的用户');
      return;
    }

    setLoading(true);
    try {
      const response = await request('/api/groups/invite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          group_id: groupId,
          invitees: selectedUsers,
          role,
          message: inviteMessage
        }),
      });

      const data = await response.json();
      if (data.success) {
        setInviteResults(data.data.invite_results);
        const successCount = data.data.invite_results.filter((r: InviteResult) => r.success).length;
        const failCount = data.data.invite_results.length - successCount;
        
        if (successCount > 0) {
          toast.success(`成功发送 ${successCount} 个邀请${failCount > 0 ? `，${failCount} 个失败` : ''}`);
        }
        
        if (failCount === 0) {
          onInviteSuccess();
          onClose();
        }
      } else {
        toast.error(data.message || '发送邀请失败');
      }
    } catch (error) {
      console.error('发送邀请失败:', error);
      toast.error('发送邀请失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 获取用户显示名称
  const getUserDisplayName = (userId: string) => {
    const user = searchResults.find(u => u.id === userId);
    return user ? user.nickname || user.phone || userId : userId;
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserPlusIcon className="h-5 w-5" />
            邀请成员加入群聊
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-auto space-y-6">
          {/* 搜索用户 */}
          <div className="space-y-3">
            <Label>搜索用户</Label>
            <div className="flex gap-2">
              <Input
                placeholder="输入用户名或手机号..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
              <Button 
                onClick={handleSearch} 
                disabled={searching}
                variant="outline"
                size="icon"
              >
                <SearchIcon className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* 搜索结果 */}
          {searchResults.length > 0 && (
            <div className="space-y-3">
              <Label>搜索结果</Label>
              <div className="max-h-40 overflow-y-auto border rounded-md">
                {searchResults.map((user) => (
                  <div
                    key={user.id}
                    className="flex items-center justify-between p-3 hover:bg-gray-50 dark:hover:bg-gray-800 border-b last:border-b-0"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-medium">
                        {user.nickname?.[0] || user.phone?.[0] || '?'}
                      </div>
                      <div>
                        <div className="font-medium">{user.nickname || '未设置昵称'}</div>
                        {user.phone && (
                          <div className="text-sm text-gray-500">{user.phone}</div>
                        )}
                      </div>
                    </div>
                    <Button
                      size="sm"
                      onClick={() => handleSelectUser(user.id)}
                      disabled={selectedUsers.includes(user.id)}
                      variant={selectedUsers.includes(user.id) ? "secondary" : "default"}
                    >
                      {selectedUsers.includes(user.id) ? (
                        <>
                          <CheckIcon className="h-4 w-4 mr-1" />
                          已选择
                        </>
                      ) : (
                        '选择'
                      )}
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 已选择的用户 */}
          {selectedUsers.length > 0 && (
            <div className="space-y-3">
              <Label>已选择的用户 ({selectedUsers.length})</Label>
              <div className="flex flex-wrap gap-2">
                {selectedUsers.map((userId) => (
                  <Badge key={userId} variant="secondary" className="flex items-center gap-1">
                    {getUserDisplayName(userId)}
                    <button
                      onClick={() => handleRemoveUser(userId)}
                      className="ml-1 hover:bg-gray-300 dark:hover:bg-gray-600 rounded-full p-0.5"
                    >
                      <XIcon className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* 权限设置 */}
          <div className="space-y-3">
            <Label>成员权限</Label>
            <Select value={role} onValueChange={setRole}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="member">普通成员</SelectItem>
                <SelectItem value="admin">管理员</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 邀请消息 */}
          <div className="space-y-3">
            <Label>邀请消息</Label>
            <Textarea
              placeholder="输入邀请消息..."
              value={inviteMessage}
              onChange={(e) => setInviteMessage(e.target.value)}
              rows={3}
            />
          </div>

          {/* 邀请结果 */}
          {inviteResults.length > 0 && (
            <div className="space-y-3">
              <Label>邀请结果</Label>
              <div className="space-y-2">
                {inviteResults.map((result, index) => (
                  <div
                    key={index}
                    className={cn(
                      "flex items-center gap-2 p-2 rounded-md text-sm",
                      result.success 
                        ? "bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300"
                        : "bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300"
                    )}
                  >
                    {result.success ? (
                      <CheckIcon className="h-4 w-4" />
                    ) : (
                      <AlertCircleIcon className="h-4 w-4" />
                    )}
                    <span className="font-medium">{result.invitee}:</span>
                    <span>{result.message}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            取消
          </Button>
          <Button 
            onClick={handleSendInvites} 
            disabled={loading || selectedUsers.length === 0}
          >
            {loading ? '发送中...' : `发送邀请 (${selectedUsers.length})`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
