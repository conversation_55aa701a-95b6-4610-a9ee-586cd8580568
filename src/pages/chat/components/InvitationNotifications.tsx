import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { BellIcon, CheckIcon, XIcon, ClockIcon, UsersIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import { request } from '@/lib/request';

interface Invitation {
  id: string;
  group_id: number;
  group_name: string;
  inviter_name: string;
  invitation_message: string;
  role: string;
  status: string;
  created_at: string;
  expires_at: string;
}

interface InvitationNotificationsProps {
  open: boolean;
  onClose: () => void;
  onInvitationHandled: () => void;
}

export const InvitationNotifications: React.FC<InvitationNotificationsProps> = ({
  open,
  onClose,
  onInvitationHandled
}) => {
  const [invitations, setInvitations] = useState<Invitation[]>([]);
  const [loading, setLoading] = useState(false);
  const [processingIds, setProcessingIds] = useState<Set<string>>(new Set());

  // 加载邀请列表
  const loadInvitations = async () => {
    if (!open) return;

    setLoading(true);
    try {
      const response = await request('/api/groups/invite', {
        method: 'GET',
      });

      const data = await response.json();
      if (data.success) {
        setInvitations(data.data || []);
      } else {
        toast.error(data.message || '加载邀请列表失败');
      }
    } catch (error) {
      console.error('加载邀请列表失败:', error);
      toast.error('加载邀请列表失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadInvitations();
  }, [open]);

  // 响应邀请
  const handleInvitationResponse = async (invitationId: string, action: 'accept' | 'decline') => {
    setProcessingIds(prev => new Set(prev).add(invitationId));
    
    try {
      const response = await request(`/api/invitations/${invitationId}/respond`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action }),
      });

      const data = await response.json();
      if (data.success) {
        toast.success(action === 'accept' ? '已接受邀请' : '已拒绝邀请');
        loadInvitations(); // 重新加载列表
        onInvitationHandled();
      } else {
        toast.error(data.message || `${action === 'accept' ? '接受' : '拒绝'}邀请失败`);
      }
    } catch (error) {
      console.error('响应邀请失败:', error);
      toast.error('操作失败，请重试');
    } finally {
      setProcessingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(invitationId);
        return newSet;
      });
    }
  };

  // 获取状态显示
  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'pending':
        return { text: '待处理', color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' };
      case 'accepted':
        return { text: '已接受', color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' };
      case 'declined':
        return { text: '已拒绝', color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' };
      case 'expired':
        return { text: '已过期', color: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300' };
      default:
        return { text: status, color: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300' };
    }
  };

  // 检查邀请是否过期
  const isExpired = (expiresAt: string) => {
    return new Date(expiresAt) < new Date();
  };

  // 获取角色显示名称
  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'admin':
        return '管理员';
      case 'member':
        return '成员';
      default:
        return role;
    }
  };

  const pendingInvitations = invitations.filter(inv => inv.status === 'pending' && !isExpired(inv.expires_at));
  const processedInvitations = invitations.filter(inv => inv.status !== 'pending' || isExpired(inv.expires_at));

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BellIcon className="h-5 w-5" />
            群聊邀请通知
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-auto space-y-6">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
                <p className="text-gray-500">加载中...</p>
              </div>
            </div>
          ) : (
            <>
              {/* 待处理邀请 */}
              {pendingInvitations.length > 0 && (
                <div className="space-y-3">
                  <h3 className="font-medium text-lg flex items-center gap-2">
                    <ClockIcon className="h-5 w-5 text-yellow-500" />
                    待处理邀请 ({pendingInvitations.length})
                  </h3>
                  <div className="space-y-3">
                    {pendingInvitations.map((invitation) => (
                      <div
                        key={invitation.id}
                        className="border rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800"
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <UsersIcon className="h-4 w-4 text-blue-500" />
                              <span className="font-medium">{invitation.group_name}</span>
                              <Badge variant="outline">
                                {getRoleDisplayName(invitation.role)}
                              </Badge>
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                              {invitation.inviter_name} 邀请您加入群聊
                            </p>
                            {invitation.invitation_message && (
                              <p className="text-sm bg-gray-100 dark:bg-gray-800 p-2 rounded mb-2">
                                "{invitation.invitation_message}"
                              </p>
                            )}
                            <div className="text-xs text-gray-500">
                              邀请时间: {new Date(invitation.created_at).toLocaleString()}
                              <span className="ml-2">
                                过期时间: {new Date(invitation.expires_at).toLocaleString()}
                              </span>
                            </div>
                          </div>
                          <div className="flex gap-2 ml-4">
                            <Button
                              size="sm"
                              onClick={() => handleInvitationResponse(invitation.id, 'accept')}
                              disabled={processingIds.has(invitation.id)}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              <CheckIcon className="h-4 w-4 mr-1" />
                              接受
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleInvitationResponse(invitation.id, 'decline')}
                              disabled={processingIds.has(invitation.id)}
                              className="text-red-600 hover:text-red-700 border-red-300 hover:border-red-400"
                            >
                              <XIcon className="h-4 w-4 mr-1" />
                              拒绝
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 已处理邀请 */}
              {processedInvitations.length > 0 && (
                <div className="space-y-3">
                  <h3 className="font-medium text-lg">
                    历史邀请 ({processedInvitations.length})
                  </h3>
                  <div className="space-y-2">
                    {processedInvitations.map((invitation) => {
                      const statusDisplay = getStatusDisplay(invitation.status);
                      return (
                        <div
                          key={invitation.id}
                          className="border rounded-lg p-3 opacity-75"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <UsersIcon className="h-4 w-4 text-gray-500" />
                                <span className="font-medium">{invitation.group_name}</span>
                                <Badge className={cn("text-xs", statusDisplay.color)}>
                                  {statusDisplay.text}
                                </Badge>
                              </div>
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                {invitation.inviter_name} 的邀请
                              </p>
                              <div className="text-xs text-gray-500">
                                {new Date(invitation.created_at).toLocaleString()}
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* 无邀请状态 */}
              {invitations.length === 0 && (
                <div className="text-center py-8">
                  <BellIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">暂无群聊邀请</p>
                </div>
              )}
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
