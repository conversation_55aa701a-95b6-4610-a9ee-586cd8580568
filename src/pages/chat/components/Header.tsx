import React, { useEffect } from 'react';
import GitHubButton from 'react-github-btn';
import '@fontsource/audiowide';



const Header: React.FC = () => {
  return (
    <header className="bg-transparent fixed top-0 left-0 right-0 z-50 hidden md:block">
      <div className="w-full px-2 h-10 flex items-center" >
        {/* Logo */}
        <div className="flex-1 flex items-center">
          <a href="/" className="flex items-center">
            <img src="/img/logo.svg" alt="logo" className="h-6 w-6 mr-2" />
            <span style={{ fontFamily: 'Audiowide, system-ui', color: '#ff6600' }} className="text-2xl">
              AgentGroup
            </span>
          </a>
        </div>

        {/* Info */}
        <div className="flex items-center justify-end">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            智能体群聊平台
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header; 