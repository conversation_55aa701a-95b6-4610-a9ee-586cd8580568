import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { UsersIcon, UserPlusIcon, SettingsIcon, TrashIcon, CrownIcon, ShieldIcon, UserIcon, BotIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import { request } from '@/lib/request';
import { InviteUserDialog } from './InviteUserDialog';

interface GroupMember {
  id: string;
  member_id: string;
  member_type: 'user' | 'agent';
  member_name: string;
  avatar_url?: string;
  role: string;
  joined_at: string;
  last_active?: string;
  message_count?: number;
  interaction_score?: number;
}

interface GroupInfo {
  id: number;
  name: string;
  description: string;
  created_by: number;
  members: GroupMember[];
}

interface GroupMembersDialogProps {
  open: boolean;
  onClose: () => void;
  groupId: number;
  currentUserId: number;
  onMembersUpdated: () => void;
}

export const GroupMembersDialog: React.FC<GroupMembersDialogProps> = ({
  open,
  onClose,
  groupId,
  currentUserId,
  onMembersUpdated
}) => {
  const [groupInfo, setGroupInfo] = useState<GroupInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [showInviteDialog, setShowInviteDialog] = useState(false);
  const [currentUserRole, setCurrentUserRole] = useState<string>('member');

  // 加载群聊信息
  const loadGroupInfo = async () => {
    if (!open || !groupId) return;

    setLoading(true);
    try {
      const response = await request(`/api/groups/${groupId}`, {
        method: 'GET',
      });

      const data = await response.json();
      if (data.success) {
        setGroupInfo(data.data);
        
        // 获取当前用户角色
        const currentMember = data.data.members.find(
          (m: GroupMember) => m.member_type === 'user' && parseInt(m.member_id) === currentUserId
        );
        setCurrentUserRole(currentMember?.role || 'member');
      } else {
        toast.error(data.message || '加载群聊信息失败');
      }
    } catch (error) {
      console.error('加载群聊信息失败:', error);
      toast.error('加载群聊信息失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadGroupInfo();
  }, [open, groupId]);

  // 更新成员角色
  const handleUpdateMemberRole = async (memberId: string, memberType: string, newRole: string) => {
    try {
      const response = await request(`/api/groups/${groupId}/members/${memberId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          member_type: memberType,
          role: newRole
        }),
      });

      const data = await response.json();
      if (data.success) {
        toast.success('成员角色更新成功');
        loadGroupInfo();
        onMembersUpdated();
      } else {
        toast.error(data.message || '更新成员角色失败');
      }
    } catch (error) {
      console.error('更新成员角色失败:', error);
      toast.error('更新成员角色失败，请重试');
    }
  };

  // 移除成员
  const handleRemoveMember = async (memberId: string, memberType: string, memberName: string) => {
    if (!confirm(`确定要移除成员"${memberName}"吗？`)) {
      return;
    }

    try {
      const response = await request(`/api/groups/${groupId}/members/${memberId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          member_type: memberType
        }),
      });

      const data = await response.json();
      if (data.success) {
        toast.success('成员移除成功');
        loadGroupInfo();
        onMembersUpdated();
      } else {
        toast.error(data.message || '移除成员失败');
      }
    } catch (error) {
      console.error('移除成员失败:', error);
      toast.error('移除成员失败，请重试');
    }
  };

  // 获取角色图标
  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner':
      case 'admin':
        return <CrownIcon className="h-4 w-4 text-yellow-500" />;
      case 'admin':
        return <ShieldIcon className="h-4 w-4 text-blue-500" />;
      default:
        return <UserIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  // 获取角色显示名称
  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'owner':
        return '群主';
      case 'admin':
        return '管理员';
      default:
        return '成员';
    }
  };

  // 检查是否有管理权限
  const hasManagePermission = currentUserRole === 'owner' || currentUserRole === 'admin';
  const isOwner = currentUserRole === 'owner';

  if (!groupInfo) {
    return (
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className="max-w-2xl">
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-gray-500">加载中...</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  const userMembers = groupInfo.members.filter(m => m.member_type === 'user');
  const agentMembers = groupInfo.members.filter(m => m.member_type === 'agent');

  return (
    <>
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <UsersIcon className="h-5 w-5" />
              群聊成员管理 - {groupInfo.name}
            </DialogTitle>
          </DialogHeader>

          <div className="flex-1 overflow-auto space-y-6">
            {/* 操作按钮 */}
            {hasManagePermission && (
              <div className="flex gap-2">
                <Button
                  onClick={() => setShowInviteDialog(true)}
                  className="flex items-center gap-2"
                >
                  <UserPlusIcon className="h-4 w-4" />
                  邀请成员
                </Button>
              </div>
            )}

            {/* 用户成员 */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <UserIcon className="h-5 w-5" />
                <h3 className="font-medium">用户成员 ({userMembers.length})</h3>
              </div>
              <div className="space-y-2">
                {userMembers.map((member) => (
                  <div
                    key={`user-${member.member_id}`}
                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium">
                        {member.member_name?.[0] || '?'}
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{member.member_name}</span>
                          <Badge variant="secondary" className="flex items-center gap-1">
                            {getRoleIcon(member.role)}
                            {getRoleDisplayName(member.role)}
                          </Badge>
                        </div>
                        <div className="text-sm text-gray-500">
                          加入时间: {new Date(member.joined_at).toLocaleDateString()}
                          {member.message_count !== undefined && (
                            <span className="ml-2">消息数: {member.message_count}</span>
                          )}
                        </div>
                      </div>
                    </div>

                    {hasManagePermission && parseInt(member.member_id) !== currentUserId && (
                      <div className="flex items-center gap-2">
                        {/* 角色选择 */}
                        {(isOwner || member.role !== 'owner') && (
                          <Select
                            value={member.role}
                            onValueChange={(newRole) => handleUpdateMemberRole(member.member_id, 'user', newRole)}
                          >
                            <SelectTrigger className="w-24">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="member">成员</SelectItem>
                              <SelectItem value="admin">管理员</SelectItem>
                              {isOwner && <SelectItem value="owner">群主</SelectItem>}
                            </SelectContent>
                          </Select>
                        )}

                        {/* 移除按钮 */}
                        {(isOwner || (currentUserRole === 'admin' && member.role !== 'owner')) && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleRemoveMember(member.member_id, 'user', member.member_name)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* 智能体成员 */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <BotIcon className="h-5 w-5" />
                <h3 className="font-medium">智能体成员 ({agentMembers.length})</h3>
              </div>
              <div className="space-y-2">
                {agentMembers.map((member) => (
                  <div
                    key={`agent-${member.member_id}`}
                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-full bg-green-500 flex items-center justify-center text-white">
                        🤖
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{member.member_name}</span>
                          <Badge variant="outline">智能体</Badge>
                        </div>
                        <div className="text-sm text-gray-500">
                          加入时间: {new Date(member.joined_at).toLocaleDateString()}
                          {member.message_count !== undefined && (
                            <span className="ml-2">响应数: {member.message_count}</span>
                          )}
                        </div>
                      </div>
                    </div>

                    {hasManagePermission && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleRemoveMember(member.member_id, 'agent', member.member_name)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 邀请用户对话框 */}
      <InviteUserDialog
        open={showInviteDialog}
        onClose={() => setShowInviteDialog(false)}
        groupId={groupId}
        groupName={groupInfo.name}
        onInviteSuccess={() => {
          setShowInviteDialog(false);
          loadGroupInfo();
          onMembersUpdated();
        }}
      />
    </>
  );
};
