import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { MessageSquareIcon, PlusCircleIcon, MenuIcon, PanelLeftCloseIcon, Bot, Settings, Users, ChevronDownIcon, ChevronRightIcon, BellIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import GitHubButton from 'react-github-btn';
import '@fontsource/audiowide';
import { AdSection } from './AdSection';
import { UserSection } from './UserSection';
import { CreateGroupDialog } from './CreateGroupDialog';
import { InvitationNotifications } from './InvitationNotifications';
import { Group } from '@/config/groups';
import { request } from '@/lib/request';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// 根据群组ID生成固定的随机颜色
const getRandomColor = (index: number) => {
  const colors = ['blue', 'green', 'yellow', 'purple', 'pink', 'indigo', 'red', 'orange', 'teal'];
  //增加hash
  const hashCode = index.toString().split('').reduce((acc, char) => {
    return char.charCodeAt(0) + ((acc << 5) - acc);
  }, 0);
  return colors[hashCode % colors.length];
};

interface SidebarProps {
  isOpen: boolean;
  toggleSidebar: () => void;
  selectedGroupIndex?: number;
  onSelectGroup?: (index: number) => void;
  groups: Group[];
  publicGroups?: Group[];
  onGroupCreated?: () => void;
}

const Sidebar = ({ isOpen, toggleSidebar, selectedGroupIndex = 0, onSelectGroup, groups, publicGroups = [], onGroupCreated }: SidebarProps) => {
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [myGroupsCollapsed, setMyGroupsCollapsed] = useState(false);
  const [publicGroupsCollapsed, setPublicGroupsCollapsed] = useState(false);
  const [showInvitations, setShowInvitations] = useState(false);
  const [pendingInvitationsCount, setPendingInvitationsCount] = useState(0);

  const handleCreateGroup = () => {
    setShowCreateDialog(true);
  };

  const handleGroupCreated = () => {
    setShowCreateDialog(false);
    if (onGroupCreated) {
      onGroupCreated();
    }
  };

  // 检查待处理邀请数量
  const checkPendingInvitations = async () => {
    try {
      const response = await request('/api/groups/invite', {
        method: 'GET',
      });

      const data = await response.json();
      if (data.success) {
        const pendingCount = (data.data || []).filter((inv: any) =>
          inv.status === 'pending' && new Date(inv.expires_at) > new Date()
        ).length;
        setPendingInvitationsCount(pendingCount);
      }
    } catch (error) {
      console.error('检查邀请失败:', error);
    }
  };

  // 定期检查邀请
  useEffect(() => {
    checkPendingInvitations();
    const interval = setInterval(checkPendingInvitations, 30000); // 每30秒检查一次
    return () => clearInterval(interval);
  }, []);

  const handleInvitationHandled = () => {
    checkPendingInvitations();
    if (onGroupCreated) {
      onGroupCreated(); // 重新加载群聊列表
    }
  };
  
  return (
    <>
      {/* 侧边栏 - 在移动设备上可以隐藏，在桌面上始终显示 */}
      <div 
        className={cn(
          "transition-all duration-300 ease-in-out",
          "fixed md:relative z-20 h-full",
          isOpen ? "w-48 translate-x-0" : "w-0 md:w-14 -translate-x-full md:translate-x-0"
        )}
      >
        <div className="h-full border-r theme-border-primary theme-bg-secondary rounded-l-lg overflow-hidden flex flex-col">
          {/* 品牌标识区域 */}
          <div className="flex items-center justify-between px-3 py-3 border-b theme-border-primary">
            <div className="flex-1 flex items-center">
              <a href="/" className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">AG</span>
                </div>
                <span
                  style={{ fontFamily: 'Audiowide, system-ui' }}
                  className={cn(
                    "font-bold text-orange-400 transition-all duration-200 whitespace-nowrap overflow-hidden",
                    isOpen ? "opacity-100 max-w-full text-lg" : "opacity-0 max-w-0 md:max-w-0"
                  )}
                >
                  AgentGroup
                </span>
              </a>
              <Button
                variant="ghost"
                size="icon"
                onClick={toggleSidebar}
                className={cn(
                  "theme-text-secondary hover-theme-text-primary hover-theme-bg-tertiary",
                  isOpen ? "ml-auto" : "mx-auto md:ml-auto"
                )}
              >
                {isOpen ? <PanelLeftCloseIcon className="h-4 w-4" /> : <MenuIcon className="h-4 w-4" />}
              </Button>
            </div>
          </div>

          <div className="flex-1 overflow-auto">
            {/* 我的群聊 */}
            <div className="px-3 py-2 border-b theme-border-primary">
              <button
                onClick={() => setMyGroupsCollapsed(!myGroupsCollapsed)}
                className={cn(
                  "flex items-center gap-2 w-full text-left text-sm font-medium theme-text-secondary hover:theme-text-primary transition-all duration-200",
                  isOpen ? "opacity-100" : "opacity-0 md:opacity-0"
                )}
              >
                {isOpen && (
                  <>
                    {myGroupsCollapsed ? (
                      <ChevronRightIcon className="h-4 w-4 flex-shrink-0" />
                    ) : (
                      <ChevronDownIcon className="h-4 w-4 flex-shrink-0" />
                    )}
                    <span className="whitespace-nowrap overflow-hidden">
                      我的群聊 ({groups.length}/5)
                    </span>
                  </>
                )}
              </button>
            </div>

            {!myGroupsCollapsed && (
              <div className="p-2 max-h-64 overflow-y-auto">
                <nav className="space-y-1.5">
                  {groups.map((group, index) => (
                    <a
                      key={group.id}
                      href="#"
                      onClick={(e) => {
                        e.preventDefault();
                        onSelectGroup?.(index);
                      }}
                      className={cn(
                        "flex items-center gap-1 rounded-md px-3 py-2.5 text-sm font-medium transition-all hover-theme-bg-tertiary group",
                        !isOpen && "md:justify-center",
                        selectedGroupIndex === index && "theme-bg-tertiary"
                      )}
                    >
                      <MessageSquareIcon
                        className={`h-5 w-5 flex-shrink-0 group-hover:opacity-80 text-${getRandomColor(index)}-500 group-hover:text-${getRandomColor(index)}-600`}
                      />
                      <span className={cn(
                        "transition-all duration-200 whitespace-nowrap overflow-hidden theme-text-primary",
                        isOpen ? "opacity-100 max-w-full" : "opacity-0 max-w-0 md:max-w-0"
                      )}>{group.name}</span>
                    </a>
                  ))}
                </nav>
              </div>
            )}

            {/* 公共群聊 */}
            {publicGroups.length > 0 && (
              <>
                <div className="px-3 py-2 border-b theme-border-primary">
                  <button
                    onClick={() => setPublicGroupsCollapsed(!publicGroupsCollapsed)}
                    className={cn(
                      "flex items-center gap-2 w-full text-left text-sm font-medium theme-text-secondary hover:theme-text-primary transition-all duration-200",
                      isOpen ? "opacity-100" : "opacity-0 md:opacity-0"
                    )}
                  >
                    {isOpen && (
                      <>
                        {publicGroupsCollapsed ? (
                          <ChevronRightIcon className="h-4 w-4 flex-shrink-0" />
                        ) : (
                          <ChevronDownIcon className="h-4 w-4 flex-shrink-0" />
                        )}
                        <span className="whitespace-nowrap overflow-hidden">
                          公共群聊 ({publicGroups.length}/10)
                        </span>
                      </>
                    )}
                  </button>
                </div>

                {!publicGroupsCollapsed && (
                  <div className="p-2 max-h-64 overflow-y-auto">
                    <nav className="space-y-1.5">
                      {publicGroups.map((group, index) => (
                        <a
                          key={`public-${group.id}`}
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            onSelectGroup?.(groups.length + index);
                          }}
                          className={cn(
                            "flex items-center gap-1 rounded-md px-3 py-2.5 text-sm font-medium transition-all hover-theme-bg-tertiary group",
                            !isOpen && "md:justify-center",
                            selectedGroupIndex === groups.length + index && "theme-bg-tertiary"
                          )}
                        >
                          <MessageSquareIcon
                            className={`h-5 w-5 flex-shrink-0 group-hover:opacity-80 text-${getRandomColor(groups.length + index)}-500 group-hover:text-${getRandomColor(groups.length + index)}-600`}
                          />
                          <span className={cn(
                            "transition-all duration-200 whitespace-nowrap overflow-hidden theme-text-primary",
                            isOpen ? "opacity-100 max-w-full" : "opacity-0 max-w-0 md:max-w-0"
                          )}>{group.name}</span>
                        </a>
                      ))}
                    </nav>
                  </div>
                )}
              </>
            )}

            <div className="p-2 border-t theme-border-primary">
              
              {/* Agent管理导航 */}
              <a
                href="/agents"
                className={cn(
                  "flex items-center gap-1 rounded-md px-3 py-2.5 text-sm font-medium transition-all hover-theme-bg-tertiary group mt-3",
                  !isOpen && "md:justify-center"
                )}
              >
                <Bot className="h-5 w-5 flex-shrink-0 text-blue-400 group-hover:text-blue-300" />
                <span className={cn(
                  "transition-all duration-200 whitespace-nowrap overflow-hidden theme-text-primary",
                  isOpen ? "opacity-100 max-w-full" : "opacity-0 max-w-0 md:max-w-0"
                )}>Agent Store</span>
              </a>

              {/* 用户管理导航 */}
              <a
                href="/user-management"
                className={cn(
                  "flex items-center gap-1 rounded-md px-3 py-2.5 text-sm font-medium transition-all hover-theme-bg-tertiary group",
                  !isOpen && "md:justify-center"
                )}
              >
                <Users className="h-5 w-5 flex-shrink-0 text-green-400 group-hover:text-green-300" />
                <span className={cn(
                  "transition-all duration-200 whitespace-nowrap overflow-hidden theme-text-primary",
                  isOpen ? "opacity-100 max-w-full" : "opacity-0 max-w-0 md:max-w-0"
                )}>用户管理</span>
              </a>

              {/* 邀请通知 */}
              <a
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  setShowInvitations(true);
                }}
                className={cn(
                  "flex items-center gap-1 rounded-md px-3 py-2.5 text-sm font-medium transition-all hover-theme-bg-tertiary group relative",
                  !isOpen && "md:justify-center"
                )}
              >
                <BellIcon className="h-5 w-5 flex-shrink-0 text-blue-400 group-hover:text-blue-300" />
                {pendingInvitationsCount > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {pendingInvitationsCount > 9 ? '9+' : pendingInvitationsCount}
                  </span>
                )}
                <span className={cn(
                  "transition-all duration-200 whitespace-nowrap overflow-hidden theme-text-primary",
                  isOpen ? "opacity-100 max-w-full" : "opacity-0 max-w-0 md:max-w-0"
                )}>群聊邀请</span>
              </a>

              {/* 系统管理导航 */}
              <a
                href="/settings"
                className={cn(
                  "flex items-center gap-1 rounded-md px-3 py-2.5 text-sm font-medium transition-all hover-theme-bg-tertiary group",
                  !isOpen && "md:justify-center"
                )}
              >
                <Settings className="h-5 w-5 flex-shrink-0 text-purple-400 group-hover:text-purple-300" />
                <span className={cn(
                  "transition-all duration-200 whitespace-nowrap overflow-hidden theme-text-primary",
                  isOpen ? "opacity-100 max-w-full" : "opacity-0 max-w-0 md:max-w-0"
                )}>系统管理</span>
              </a>

              <a
                href="#"
                className={cn(
                  "flex items-center gap-1 rounded-md px-3 py-2.5 text-sm font-medium transition-all hover-theme-bg-tertiary group",
                  !isOpen && "md:justify-center"
                )}
                onClick={(e) => {
                  e.preventDefault();
                  handleCreateGroup();
                }}
              >
                <PlusCircleIcon className="h-5 w-5 flex-shrink-0 text-amber-400 group-hover:text-amber-300" />
                <span className={cn(
                  "transition-all duration-200 whitespace-nowrap overflow-hidden theme-text-primary",
                  isOpen ? "opacity-100 max-w-full" : "opacity-0 max-w-0 md:max-w-0"
                )}>创建新群聊</span>
              </a>
            </div>
          </div>

          {/* 广告位 */}
          <AdSection isOpen={isOpen} />

          {/* 用户信息模块 */}
          <UserSection isOpen={isOpen} />

          {/* 底部信息 */}
          <div className="px-3 py-2 mt-auto border-t theme-border-primary">
            {isOpen && (
              <div className="text-xs theme-text-tertiary text-center">
                智能体群聊平台
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 移动设备上的遮罩层，点击时关闭侧边栏 */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-10 md:hidden"
          onClick={toggleSidebar}
        />
      )}

      {/* 创建群聊对话框 */}
      <CreateGroupDialog
        open={showCreateDialog}
        onClose={() => setShowCreateDialog(false)}
        onSuccess={handleGroupCreated}
      />

      {/* 邀请通知对话框 */}
      <InvitationNotifications
        open={showInvitations}
        onClose={() => setShowInvitations(false)}
        onInvitationHandled={handleInvitationHandled}
      />
    </>
  );
};

export default Sidebar; 