import React, { useState } from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import { Settings as SettingsIcon, Palette, User, Bell, Shield, Info, Users, CreditCard, Plus, Copy, Check } from 'lucide-react';
import { cn } from "@/lib/utils";
import { useUserStore } from '@/store/userStore';
import UserManagement from '../user-management';
import { request } from '@/utils/request';

// 充值管理组件
const RechargeManagement: React.FC = () => {
  const [amount, setAmount] = useState('');
  const [days, setDays] = useState('');
  const [expiresAt, setExpiresAt] = useState('');
  const [generating, setGenerating] = useState(false);
  const [generatedKey, setGeneratedKey] = useState('');
  const [copied, setCopied] = useState(false);
  const [rechargeKeys, setRechargeKeys] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  // 生成充值密钥
  const generateRechargeKey = async () => {
    if (!amount || !days) {
      alert('请填写充值金额和延长天数');
      return;
    }

    try {
      setGenerating(true);
      const response = await request('/api/admin/recharge-keys/generate', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          amount: parseFloat(amount),
          extend_days: parseInt(days),
          expires_at: expiresAt || null
        })
      });
      const data = await response.json();

      if (data.success) {
        setGeneratedKey(data.data.key_code);
        setAmount('');
        setDays('');
        setExpiresAt('');
        fetchRechargeKeys(); // 刷新列表
      } else {
        alert(data.message || '生成失败');
      }
    } catch (error) {
      console.error('生成充值密钥失败:', error);
      alert('生成失败，请重试');
    } finally {
      setGenerating(false);
    }
  };

  // 复制密钥
  const copyKey = async () => {
    try {
      await navigator.clipboard.writeText(generatedKey);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('复制失败:', error);
    }
  };

  // 获取充值密钥列表
  const fetchRechargeKeys = async () => {
    try {
      setLoading(true);
      const response = await request('/api/admin/recharge-keys', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();

      if (data.success) {
        setRechargeKeys(data.data);
      }
    } catch (error) {
      console.error('获取充值密钥失败:', error);
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    fetchRechargeKeys();
  }, []);

  return (
    <div className="theme-bg-secondary rounded-lg p-6">
      <h2 className="text-lg font-medium mb-6">充值管理</h2>

      {/* 生成新密钥 */}
      <div className="mb-8">
        <h3 className="text-md font-medium mb-4">生成充值密钥</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium theme-text-secondary mb-2">
              充值金额（字币）
            </label>
            <input
              type="number"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              placeholder="例如：100"
              className="w-full px-3 py-2 theme-bg-primary theme-border-primary border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium theme-text-secondary mb-2">
              延长天数
            </label>
            <input
              type="number"
              value={days}
              onChange={(e) => setDays(e.target.value)}
              placeholder="例如：30"
              className="w-full px-3 py-2 theme-bg-primary theme-border-primary border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium theme-text-secondary mb-2">
              密钥过期时间（可选）
            </label>
            <input
              type="datetime-local"
              value={expiresAt}
              onChange={(e) => setExpiresAt(e.target.value)}
              className="w-full px-3 py-2 theme-bg-primary theme-border-primary border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
        <button
          onClick={generateRechargeKey}
          disabled={generating}
          className="bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white px-4 py-2 rounded-md transition-colors flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          {generating ? '生成中...' : '生成密钥'}
        </button>
      </div>

      {/* 生成的密钥 */}
      {generatedKey && (
        <div className="mb-8 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <h4 className="text-md font-medium text-green-800 dark:text-green-200 mb-2">
            密钥生成成功！
          </h4>
          <div className="flex items-center gap-2">
            <code className="flex-1 px-3 py-2 bg-white dark:bg-gray-800 border rounded font-mono text-sm">
              {generatedKey}
            </code>
            <button
              onClick={copyKey}
              className="bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded transition-colors flex items-center gap-1"
            >
              {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
              {copied ? '已复制' : '复制'}
            </button>
          </div>
          <p className="text-sm text-green-600 dark:text-green-400 mt-2">
            请将此密钥提供给用户进行充值，密钥只显示一次，请妥善保存。
          </p>
        </div>
      )}

      {/* 密钥列表 */}
      <div>
        <h3 className="text-md font-medium mb-4">充值密钥列表</h3>
        {loading ? (
          <div className="text-center py-4">加载中...</div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b theme-border-primary">
                  <th className="text-left py-2 px-3">密钥</th>
                  <th className="text-left py-2 px-3">金额</th>
                  <th className="text-left py-2 px-3">延长天数</th>
                  <th className="text-left py-2 px-3">状态</th>
                  <th className="text-left py-2 px-3">使用者</th>
                  <th className="text-left py-2 px-3">创建时间</th>
                </tr>
              </thead>
              <tbody>
                {rechargeKeys.map((key) => (
                  <tr key={key.id} className="border-b theme-border-primary">
                    <td className="py-2 px-3 font-mono text-sm">{key.key_code}</td>
                    <td className="py-2 px-3">{key.amount} 字币</td>
                    <td className="py-2 px-3">{key.extend_days} 天</td>
                    <td className="py-2 px-3">
                      <span className={`px-2 py-1 rounded text-xs ${
                        key.status === 0
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                          : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                      }`}>
                        {key.status === 0 ? '未使用' : '已使用'}
                      </span>
                    </td>
                    <td className="py-2 px-3">{key.used_by || '-'}</td>
                    <td className="py-2 px-3">{new Date(key.created_at).toLocaleDateString()}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

const Settings: React.FC = () => {
  const { theme, setTheme } = useTheme();
  const [activeTab, setActiveTab] = useState('general');
  const userStore = useUserStore();

  // 检查是否为管理员
  const isAdmin = userStore.userInfo?.role === 'admin';

  const tabs = [
    { id: 'general', name: '常规设置', icon: SettingsIcon },
    { id: 'appearance', name: '外观设置', icon: Palette },
    { id: 'account', name: '账户设置', icon: User },
    { id: 'notifications', name: '通知设置', icon: Bell },
    { id: 'privacy', name: '隐私安全', icon: Shield },
    ...(isAdmin ? [
      { id: 'users', name: '用户管理', icon: Users },
      { id: 'recharge', name: '充值管理', icon: CreditCard }
    ] : []),
    { id: 'about', name: '关于', icon: Info }
  ];



  return (
    <div className="flex flex-col h-full theme-bg-primary theme-text-primary">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b theme-border-primary">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-gray-500 to-gray-600 rounded-lg flex items-center justify-center">
            <SettingsIcon className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-semibold">系统管理</h1>
            <p className="text-sm theme-text-secondary">管理系统设置和用户权限</p>
          </div>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar */}
        <div className="w-64 theme-bg-secondary border-r theme-border-primary">
          <nav className="p-4 space-y-2">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={cn(
                    "w-full flex items-center gap-3 px-3 py-2 rounded-md text-left transition-colors",
                    activeTab === tab.id
                      ? "bg-blue-600 text-white"
                      : "theme-text-secondary hover-theme-bg-tertiary"
                  )}
                >
                  <Icon className="w-5 h-5" />
                  {tab.name}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {activeTab === 'users' && isAdmin ? (
            <UserManagement />
          ) : (
            <div className="p-6">
              <div className="max-w-4xl space-y-8">
                {activeTab === 'general' && (
                  <div className="theme-bg-secondary rounded-lg p-6">
                    <h2 className="text-lg font-medium mb-4">常规设置</h2>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between py-3">
                        <div>
                          <h3 className="font-medium">语言设置</h3>
                          <p className="text-sm theme-text-secondary">选择界面语言</p>
                        </div>
                        <select className="px-3 py-1.5 theme-bg-tertiary theme-border-primary border rounded-lg">
                          <option>简体中文</option>
                          <option>English</option>
                        </select>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'appearance' && (
                  <div className="theme-bg-secondary rounded-lg p-6">
                    <h2 className="text-lg font-medium mb-4">外观设置</h2>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between py-3">
                        <div>
                          <h3 className="font-medium">主题模式</h3>
                          <p className="text-sm theme-text-secondary">选择浅色或深色主题</p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => setTheme('light')}
                            className={`px-3 py-1.5 rounded-lg text-sm transition-colors ${
                              theme === 'light'
                                ? 'bg-blue-600 text-white'
                                : 'theme-bg-tertiary theme-text-secondary hover-theme-bg-quaternary'
                            }`}
                          >
                            浅色
                          </button>
                          <button
                            onClick={() => setTheme('dark')}
                            className={`px-3 py-1.5 rounded-lg text-sm transition-colors ${
                              theme === 'dark'
                                ? 'bg-blue-600 text-white'
                                : 'theme-bg-tertiary theme-text-secondary hover-theme-bg-quaternary'
                            }`}
                          >
                            深色
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'account' && (
                  <div className="theme-bg-secondary rounded-lg p-6">
                    <h2 className="text-lg font-medium mb-4">账户设置</h2>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between py-3">
                        <div>
                          <h3 className="font-medium">个人信息</h3>
                          <p className="text-sm theme-text-secondary">管理您的个人资料</p>
                        </div>
                        <button
                          onClick={() => alert('个人信息编辑功能开发中...')}
                          className="px-3 py-1.5 theme-bg-tertiary theme-text-secondary rounded-lg text-sm hover-theme-bg-quaternary transition-colors"
                        >
                          编辑
                        </button>
                      </div>
                      <div className="flex items-center justify-between py-3">
                        <div>
                          <h3 className="font-medium">密码修改</h3>
                          <p className="text-sm theme-text-secondary">更改您的登录密码</p>
                        </div>
                        <button
                          onClick={() => alert('密码修改功能开发中...')}
                          className="px-3 py-1.5 theme-bg-tertiary theme-text-secondary rounded-lg text-sm hover-theme-bg-quaternary transition-colors"
                        >
                          修改
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'notifications' && (
                  <div className="theme-bg-secondary rounded-lg p-6">
                    <h2 className="text-lg font-medium mb-4">通知设置</h2>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between py-3">
                        <div>
                          <h3 className="font-medium">消息通知</h3>
                          <p className="text-sm theme-text-secondary">接收新消息的通知</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        </label>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'privacy' && (
                  <div className="theme-bg-secondary rounded-lg p-6">
                    <h2 className="text-lg font-medium mb-4">隐私与安全</h2>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between py-3">
                        <div>
                          <h3 className="font-medium">数据隐私</h3>
                          <p className="text-sm theme-text-secondary">管理您的数据隐私设置</p>
                        </div>
                        <button
                          onClick={() => alert('数据隐私设置功能开发中...')}
                          className="px-3 py-1.5 theme-bg-tertiary theme-text-secondary rounded-lg text-sm hover-theme-bg-quaternary transition-colors"
                        >
                          查看
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'recharge' && isAdmin && (
                  <RechargeManagement />
                )}

                {activeTab === 'about' && (
                  <div className="theme-bg-secondary rounded-lg p-6">
                    <h2 className="text-lg font-medium mb-4">关于</h2>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between py-3">
                        <div>
                          <h3 className="font-medium">版本信息</h3>
                          <p className="text-sm theme-text-secondary">AgentGroup v1.0.0</p>
                        </div>
                        <span className="text-sm theme-text-tertiary">最新版本</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Settings;
