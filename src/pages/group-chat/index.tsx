import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useGroupChat } from '../../contexts/GroupChatContext';
import { Plus, Users, MessageSquare, Settings, X, Check, Edit } from 'lucide-react';
import AgentSelectionPage from './AgentSelectionPage';
import { createGroup, getGroupList, deleteGroup, CreateGroupRequest } from '../../utils/api';

interface Agent {
  id: string;
  name: string;
  description: string;
  avatar: string;
  capabilities: string[];
  isSelected: boolean;
}

interface GroupChat {
  id: string;
  name: string;
  agents: Agent[];
  lastMessage: string;
  timestamp: Date;
  isActive: boolean;
}

const GroupChat: React.FC = () => {
  const navigate = useNavigate();
  const { groupChats, setGroupChats, deleteGroupChat } = useGroupChat();

  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showAgentSelection, setShowAgentSelection] = useState(false);
  const [newGroupName, setNewGroupName] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [availableAgents] = useState<Agent[]>([
    { id: 'super', name: '超级智能体', description: '任务协调与智能分析', avatar: '🤖', capabilities: ['协调', '分析', '决策'], isSelected: true },
    { id: 'dev', name: '开发助手', description: '专业代码开发与技术支持', avatar: '💻', capabilities: ['编程', '调试', '架构'], isSelected: false },
    { id: 'design', name: 'UI设计师', description: '用户界面与体验设计', avatar: '🎨', capabilities: ['设计', '原型', 'UX'], isSelected: false },
    { id: 'writer', name: '文案助手', description: '内容创作与文档编写', avatar: '✍️', capabilities: ['写作', '编辑', '翻译'], isSelected: false },
    { id: 'analyst', name: '数据分析师', description: '数据分析与商业洞察', avatar: '📊', capabilities: ['分析', '统计', '报告'], isSelected: false },
    { id: 'marketing', name: '营销专家', description: '市场策略与推广方案', avatar: '📈', capabilities: ['营销', '策略', '推广'], isSelected: false },
  ]);

  const [selectedAgents, setSelectedAgents] = useState<Agent[]>([
    availableAgents[0] // 超级智能体默认选中
  ]);

  // 加载群聊列表
  const loadGroupChats = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await getGroupList();
      console.log('API Response:', response);

      if (response && response.success) {
        // 处理不同的响应格式
        let groups = [];

        if (response.data && response.data.groups) {
          // 标准格式：有groups数组
          groups = response.data.groups;
        } else if (response.data && Array.isArray(response.data)) {
          // 简化格式：data直接是数组
          groups = response.data;
        } else if (response.data) {
          // 其他格式：尝试从data中提取
          groups = [];
        } else {
          // 没有数据，使用空数组
          groups = [];
        }

        // 转换后端数据格式为前端格式
        const convertedGroups = groups.map((group: any) => ({
          id: group.id ? group.id.toString() : Date.now().toString(),
          name: group.name || '未命名群聊',
          agents: group.member_preview?.filter((m: any) => m.member_type === 'agent').map((m: any) => ({
            id: m.member_id,
            name: m.member_name,
            description: '',
            avatar: '🤖',
            capabilities: [],
            isSelected: false
          })) || [],
          lastMessage: group.last_message_time ? '最近活动' : '暂无消息',
          timestamp: group.last_message_time ? new Date(group.last_message_time) : new Date(group.created_at || Date.now()),
          isActive: false
        }));

        setGroupChats(convertedGroups);
      } else {
        console.error('API response failed:', response);
        // 不显示错误，而是使用空数据
        setGroupChats([]);
      }
    } catch (error) {
      console.error('加载群聊列表失败:', error);
      setError('加载群聊列表失败，请刷新重试');
    } finally {
      setLoading(false);
    }
  };

  // 组件加载时获取群聊列表
  useEffect(() => {
    loadGroupChats();
  }, []);

  const handleCreateGroup = async (name: string, agents: Agent[]) => {
    try {
      setLoading(true);
      setError(null);

      // 构建创建群聊的请求
      const request: CreateGroupRequest = {
        name,
        description: `包含 ${agents.length} 个智能体的群聊`,
        group_type: 'mixed',
        max_members: 50,
        agent_ids: agents.map(a => a.id),
        user_ids: []
      };

      const response = await createGroup(request);

      if (response.success && response.data) {
        setShowAgentSelection(false);

        // 重新加载群聊列表
        await loadGroupChats();

        // 导航到新创建的群聊
        navigate(`/group/${response.data.id}`);
      } else {
        setError(response.message || '创建群聊失败');
      }
    } catch (error) {
      console.error('创建群聊失败:', error);
      setError('创建群聊失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenAgentSelection = () => {
    setShowAgentSelection(true);
  };

  const handleEditGroup = (groupId: string) => {
    // TODO: 实现编辑群聊功能
    console.log('Edit group:', groupId);
  };

  const handleEnterGroup = (groupId: string) => {
    navigate(`/group/${groupId}`);
  };

  const handleDeleteGroup = async (groupId: string) => {
    const group = groupChats.find(g => g.id === groupId);
    const groupName = group?.name || '群聊';

    if (confirm(`确定要删除"${groupName}"吗？\n\n⚠️ 警告：删除后数据不可恢复，包括所有聊天记录和群聊配置。`)) {
      try {
        setLoading(true);
        setError(null);

        const response = await deleteGroup(groupId);

        if (response.success) {
          // 从本地状态中移除
          deleteGroupChat(groupId);
          // 重新加载群聊列表以确保同步
          await loadGroupChats();
        } else {
          setError(response.message || '删除群聊失败');
        }
      } catch (error) {
        console.error('删除群聊失败:', error);
        setError('删除群聊失败，请重试');
      } finally {
        setLoading(false);
      }
    }
  };

  const toggleAgentSelection = (agent: Agent) => {
    if (agent.id === 'super') return; // 超级智能体不能取消选择

    setSelectedAgents(prev => {
      const isSelected = prev.some(a => a.id === agent.id);
      if (isSelected) {
        return prev.filter(a => a.id !== agent.id);
      } else {
        return [...prev, agent];
      }
    });
  };

  const canCreateMoreGroups = groupChats.length < 5;

  return (
    <main className="flex flex-col h-full theme-bg-primary theme-text-primary" role="main" aria-label="群聊管理">
      {/* Header */}
      <div className="flex items-center justify-between p-4 theme-border-primary border-b">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 theme-bg-accent rounded-lg flex items-center justify-center">
            <Users className="w-6 h-6 theme-text-accent-foreground" />
          </div>
          <div>
            <h1 className="text-xl font-semibold">群聊管理</h1>
            <p className="text-sm theme-text-secondary">管理您的智能体团队 ({groupChats.length}/5)</p>
          </div>
        </div>
        <button
          onClick={handleOpenAgentSelection}
          disabled={!canCreateMoreGroups}
          aria-label="创建群聊"
          className="flex items-center space-x-2 px-4 py-2 theme-bg-accent hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg transition-opacity theme-text-accent-foreground"
        >
          <Plus className="w-4 h-4" />
          <span>创建群聊</span>
        </button>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mx-4 mt-4 p-3 bg-red-100 border border-red-300 text-red-700 rounded-lg">
          {error}
          <button
            onClick={() => setError(null)}
            className="ml-2 text-red-500 hover:text-red-700"
          >
            ✕
          </button>
        </div>
      )}

      {/* Group List */}
      <section className="flex-1 overflow-y-auto p-4" role="list" aria-label="群聊列表" data-testid="group-list">
        {loading ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
              <p className="theme-text-secondary">加载中...</p>
            </div>
          </div>
        ) : groupChats.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full space-y-4">
            <div className="w-20 h-20 theme-bg-accent rounded-2xl flex items-center justify-center">
              <Users className="w-10 h-10 theme-text-accent-foreground" />
            </div>
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">还没有群聊</h2>
              <p className="theme-text-secondary">创建您的第一个智能体群聊开始协作</p>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {groupChats.map((group) => (
              <article
                key={group.id}
                role="listitem"
                tabIndex={0}
                aria-label={`群聊: ${group.name}`}
                className={`p-4 rounded-lg border transition-colors cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500/50 ${
                  group.isActive
                    ? 'theme-bg-accent/20 theme-border-accent'
                    : 'theme-bg-secondary theme-border-primary hover-theme-bg-tertiary'
                }`}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handleEnterGroup(group.id);
                  }
                }}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-medium theme-text-primary">{group.name}</h3>
                      <span className="text-xs theme-bg-tertiary px-2 py-1 rounded">
                        {group.agents.length} 个智能体
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-2 mb-3">
                      {group.agents.map((agent) => (
                        <div
                          key={agent.id}
                          className="flex items-center space-x-1 theme-bg-tertiary px-2 py-1 rounded text-xs"
                        >
                          <span>{agent.avatar}</span>
                          <span>{agent.name}</span>
                        </div>
                      ))}
                    </div>
                    
                    <p className="text-sm theme-text-secondary mb-2">{group.lastMessage}</p>
                    <p className="text-xs theme-text-tertiary">
                      {group.timestamp.toLocaleString()}
                    </p>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleEnterGroup(group.id)}
                      className="p-2 hover-theme-bg-tertiary rounded-lg transition-colors"
                      title="进入群聊"
                    >
                      <MessageSquare className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleEditGroup(group.id)}
                      className="p-2 hover-theme-bg-tertiary rounded-lg transition-colors"
                      title="编辑群聊"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleDeleteGroup(group.id)}
                      className="p-2 hover-theme-bg-tertiary theme-text-secondary hover:text-red-500 rounded-lg transition-colors"
                      title="删除群聊"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </article>
            ))}
          </div>
        )}
      </section>

      {/* Create Group Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="theme-bg-primary rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold theme-text-primary">创建新群聊</h2>
              <button
                onClick={() => setShowCreateModal(false)}
                className="p-2 hover-theme-bg-tertiary rounded-lg transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium mb-2 theme-text-primary">群聊名称</label>
                <input
                  type="text"
                  value={newGroupName}
                  onChange={(e) => setNewGroupName(e.target.value)}
                  placeholder="输入群聊名称"
                  className="w-full p-3 theme-bg-secondary theme-border-primary rounded-lg focus:outline-none focus:border-blue-500 theme-text-primary placeholder-theme-text-tertiary"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2 theme-text-primary">
                  选择智能体 ({selectedAgents.length} 个已选择)
                </label>
                <div className="grid grid-cols-1 gap-3">
                  {availableAgents.map((agent) => {
                    const isSelected = selectedAgents.some(a => a.id === agent.id);
                    const isSuper = agent.id === 'super';
                    
                    return (
                      <div
                        key={agent.id}
                        onClick={() => !isSuper && toggleAgentSelection(agent)}
                        className={`p-4 rounded-lg border transition-colors ${
                          isSelected
                            ? 'theme-bg-accent theme-border-accent'
                            : 'theme-bg-secondary theme-border-primary hover-theme-bg-tertiary'
                        } ${isSuper ? 'cursor-default' : 'cursor-pointer'}`}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <span className="text-2xl">{agent.avatar}</span>
                            <div>
                              <h4 className="font-medium theme-text-primary">{agent.name}</h4>
                              <p className="text-sm theme-text-secondary">{agent.description}</p>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {agent.capabilities.map((cap) => (
                                  <span
                                    key={cap}
                                    className="text-xs theme-bg-tertiary px-2 py-1 rounded"
                                  >
                                    {cap}
                                  </span>
                                ))}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center">
                            {isSuper && (
                              <span className="text-xs theme-bg-accent theme-text-accent-foreground px-2 py-1 rounded mr-2">
                                必选
                              </span>
                            )}
                            <div
                              className={`w-5 h-5 rounded border-2 flex items-center justify-center ${
                                isSelected
                                  ? 'theme-bg-accent theme-border-accent'
                                  : 'theme-border-secondary'
                              }`}
                            >
                              {isSelected && <Check className="w-3 h-3 theme-text-accent-foreground" />}
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>

            <div className="flex items-center justify-end space-x-3 mt-6 pt-6 border-t theme-border-primary">
              <button
                onClick={() => setShowCreateModal(false)}
                className="px-4 py-2 theme-text-secondary hover-theme-text-primary transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleCreateGroup}
                disabled={!newGroupName.trim() || selectedAgents.length === 0}
                className="px-4 py-2 theme-bg-accent hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg transition-opacity theme-text-accent-foreground"
              >
                创建群聊
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Agent Selection Page */}
      {showAgentSelection && (
        <AgentSelectionPage
          onClose={() => setShowAgentSelection(false)}
          onCreateGroup={handleCreateGroup}
        />
      )}
    </main>
  );
};

export default GroupChat;
