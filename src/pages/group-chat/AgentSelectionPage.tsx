import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, Filter, ArrowLeft, Check, Crown } from 'lucide-react';
import { useGroupChat } from '../../contexts/GroupChatContext';

interface Agent {
  id: string;
  name: string;
  description: string;
  avatar: string;
  capabilities: string[];
  category: string;
  isSelected: boolean;
  is_super_agent?: boolean;
}

interface AgentSelectionPageProps {
  onClose: () => void;
  onCreateGroup: (name: string, agents: Agent[]) => void;
}

const AgentSelectionPage: React.FC<AgentSelectionPageProps> = ({ onClose, onCreateGroup }) => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [groupName, setGroupName] = useState('');
  const [selectedAgents, setSelectedAgents] = useState<Agent[]>([]);

  // 模拟Agent数据，实际应该从API获取
  const [availableAgents] = useState<Agent[]>([
    { 
      id: 'super', 
      name: '超级智能体', 
      description: '任务协调与智能分析，负责理解用户意图并分配任务给合适的专业智能体', 
      avatar: '🤖', 
      capabilities: ['协调', '分析', '决策', '任务分配'], 
      category: 'management',
      isSelected: true,
      is_super_agent: true
    },
    { 
      id: 'dev', 
      name: '开发助手', 
      description: '专业代码开发与技术支持，精通多种编程语言和开发框架', 
      avatar: '💻', 
      capabilities: ['编程', '调试', '架构', '代码审查'], 
      category: 'development',
      isSelected: false 
    },
    { 
      id: 'design', 
      name: 'UI设计师', 
      description: '用户界面与体验设计专家，擅长创建美观易用的界面', 
      avatar: '🎨', 
      capabilities: ['设计', '原型', 'UX', '视觉设计'], 
      category: 'design',
      isSelected: false 
    },
    { 
      id: 'writer', 
      name: '文案助手', 
      description: '内容创作与文档编写专家，擅长各类文案和技术文档', 
      avatar: '✍️', 
      capabilities: ['写作', '编辑', '翻译', '文档'], 
      category: 'content',
      isSelected: false 
    },
    { 
      id: 'analyst', 
      name: '数据分析师', 
      description: '数据分析与商业洞察专家，提供数据驱动的决策支持', 
      avatar: '📊', 
      capabilities: ['分析', '统计', '报告', '可视化'], 
      category: 'analytics',
      isSelected: false 
    },
    { 
      id: 'marketing', 
      name: '营销专家', 
      description: '市场策略与推广方案专家，精通数字营销和品牌推广', 
      avatar: '📈', 
      capabilities: ['营销', '策略', '推广', '品牌'], 
      category: 'marketing',
      isSelected: false 
    },
    { 
      id: 'support', 
      name: '客服助手', 
      description: '客户服务与支持专家，提供专业的客户咨询服务', 
      avatar: '🎧', 
      capabilities: ['客服', '咨询', '问题解决', '沟通'], 
      category: 'support',
      isSelected: false 
    },
    { 
      id: 'finance', 
      name: '财务顾问', 
      description: '财务分析与投资建议专家，提供专业的财务咨询服务', 
      avatar: '💰', 
      capabilities: ['财务', '投资', '分析', '规划'], 
      category: 'finance',
      isSelected: false 
    },
  ]);

  const categories = [
    { id: 'all', name: '全部', count: availableAgents.length },
    { id: 'management', name: '管理协调', count: availableAgents.filter(a => a.category === 'management').length },
    { id: 'development', name: '开发技术', count: availableAgents.filter(a => a.category === 'development').length },
    { id: 'design', name: '设计创意', count: availableAgents.filter(a => a.category === 'design').length },
    { id: 'content', name: '内容创作', count: availableAgents.filter(a => a.category === 'content').length },
    { id: 'analytics', name: '数据分析', count: availableAgents.filter(a => a.category === 'analytics').length },
    { id: 'marketing', name: '市场营销', count: availableAgents.filter(a => a.category === 'marketing').length },
    { id: 'support', name: '客户服务', count: availableAgents.filter(a => a.category === 'support').length },
    { id: 'finance', name: '财务金融', count: availableAgents.filter(a => a.category === 'finance').length },
  ];

  // 初始化选中超级智能体
  useEffect(() => {
    const superAgent = availableAgents.find(agent => agent.is_super_agent);
    if (superAgent) {
      setSelectedAgents([superAgent]);
    }
  }, [availableAgents]);

  // 过滤Agent
  const filteredAgents = availableAgents.filter(agent => {
    const matchesSearch = agent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         agent.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         agent.capabilities.some(cap => cap.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || agent.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const toggleAgentSelection = (agent: Agent) => {
    if (agent.is_super_agent) return; // 超级智能体不能取消选择
    
    setSelectedAgents(prev => {
      const isSelected = prev.some(a => a.id === agent.id);
      if (isSelected) {
        return prev.filter(a => a.id !== agent.id);
      } else {
        return [...prev, agent];
      }
    });
  };

  const handleCreateGroup = () => {
    if (!groupName.trim() || selectedAgents.length === 0) return;
    
    onCreateGroup(groupName, selectedAgents);
    onClose();
  };

  const isAgentSelected = (agentId: string) => {
    return selectedAgents.some(a => a.id === agentId);
  };

  return (
    <div className="fixed inset-0 theme-bg-primary z-50 overflow-hidden">
      {/* Header */}
      <div className="theme-bg-secondary theme-border-primary border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={onClose}
              className="p-2 hover-theme-bg-tertiary rounded-lg transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <div>
              <h1 className="text-xl font-semibold theme-text-primary">创建新群聊</h1>
              <p className="text-sm theme-text-secondary">选择智能体成员来创建您的专业团队</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="theme-text-secondary">
              已选择 {selectedAgents.length} 个智能体
            </div>
            <button
              onClick={handleCreateGroup}
              disabled={!groupName.trim() || selectedAgents.length === 0}
              className="px-6 py-2 theme-bg-accent text-white rounded-lg hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed transition-opacity"
            >
              创建群聊
            </button>
          </div>
        </div>
        
        {/* Group Name Input */}
        <div className="mt-6 p-4 theme-bg-secondary rounded-lg border theme-border-primary">
          <label className="block text-sm font-medium theme-text-primary mb-2">
            群聊名称 <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            value={groupName}
            onChange={(e) => setGroupName(e.target.value)}
            placeholder="请输入群聊名称，例如：产品设计团队"
            className="w-full max-w-lg p-4 text-lg theme-bg-primary theme-border-primary border-2 rounded-lg focus:outline-none focus:theme-border-accent theme-text-primary placeholder-theme-text-tertiary transition-colors"
          />
          <p className="text-xs theme-text-tertiary mt-1">
            建议使用有意义的名称，方便后续管理和识别
          </p>
        </div>
      </div>

      {/* Search and Filter Bar */}
      <div className="theme-bg-secondary theme-border-primary border-b px-6 py-4">
        <div className="flex items-center space-x-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 theme-text-tertiary" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="搜索智能体..."
              className="w-full pl-10 pr-4 py-2 theme-bg-primary theme-border-primary rounded-lg focus:outline-none focus:border-blue-500 theme-text-primary placeholder-theme-text-tertiary"
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <Filter className="w-4 h-4 theme-text-secondary" />
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="theme-bg-primary theme-border-primary theme-text-primary rounded-lg px-3 py-2 focus:outline-none focus:border-blue-500"
            >
              {categories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.name} ({category.count})
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Agent Grid */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredAgents.map((agent) => {
            const isSelected = isAgentSelected(agent.id);
            const isSuper = agent.is_super_agent;
            
            return (
              <div
                key={agent.id}
                onClick={() => toggleAgentSelection(agent)}
                className={`relative p-6 rounded-xl border-2 transition-all cursor-pointer hover:shadow-lg ${
                  isSelected
                    ? 'theme-border-accent theme-bg-accent/10'
                    : 'theme-border-primary theme-bg-secondary hover-theme-bg-tertiary'
                } ${isSuper ? 'cursor-default ring-2 ring-purple-500/50' : ''}`}
              >
                {/* Selection Indicator */}
                <div className="absolute top-4 right-4">
                  {isSuper && (
                    <div className="flex items-center space-x-1">
                      <Crown className="w-4 h-4 text-purple-500" />
                      <span className="text-xs theme-bg-accent theme-text-accent-foreground px-2 py-1 rounded">
                        必选
                      </span>
                    </div>
                  )}
                  {!isSuper && (
                    <div
                      className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                        isSelected
                          ? 'theme-bg-accent theme-border-accent'
                          : 'theme-border-secondary'
                      }`}
                    >
                      {isSelected && <Check className="w-4 h-4 theme-text-accent-foreground" />}
                    </div>
                  )}
                </div>

                {/* Agent Info */}
                <div className="space-y-4">
                  <div className="text-4xl">{agent.avatar}</div>
                  
                  <div>
                    <h3 className="text-lg font-semibold theme-text-primary">{agent.name}</h3>
                    <p className="text-sm theme-text-secondary mt-1 line-clamp-2">{agent.description}</p>
                  </div>
                  
                  <div className="flex flex-wrap gap-2">
                    {agent.capabilities.slice(0, 3).map((capability) => (
                      <span
                        key={capability}
                        className="text-xs theme-bg-tertiary theme-text-secondary px-2 py-1 rounded-full"
                      >
                        {capability}
                      </span>
                    ))}
                    {agent.capabilities.length > 3 && (
                      <span className="text-xs theme-text-tertiary">
                        +{agent.capabilities.length - 3}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
        
        {filteredAgents.length === 0 && (
          <div className="text-center py-12">
            <div className="text-4xl mb-4">🔍</div>
            <h3 className="text-lg font-medium theme-text-primary mb-2">未找到匹配的智能体</h3>
            <p className="theme-text-secondary">尝试调整搜索条件或选择不同的分类</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AgentSelectionPage;
