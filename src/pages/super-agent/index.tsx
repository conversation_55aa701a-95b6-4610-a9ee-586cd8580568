import React, { useState, useRef, useEffect } from 'react';
import { Send, Plus, Settings, User, MessageSquare, Sparkles } from 'lucide-react';

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'super-agent';
  timestamp: Date;
}

const SuperAgent: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      sender: 'user',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    // 模拟超级智能体回复
    setTimeout(() => {
      const agentMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: `我是超级智能体，我理解您的需求："${inputValue}"。我会分析您的任务并协调其他专业智能体来帮助您完成。`,
        sender: 'super-agent',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, agentMessage]);
      setIsLoading(false);
    }, 1000);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const suggestedPrompts = [
    "帮我写一份产品需求文档",
    "分析这个数据集的趋势",
    "设计一个用户界面",
    "优化代码性能",
    "制定营销策略",
    "翻译这段文字"
  ];

  return (
    <div className="flex flex-col h-full theme-bg-primary theme-text-primary">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b theme-border-primary">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
            <Sparkles className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-semibold">超级智能体</h1>
            <p className="text-sm theme-text-secondary">智能任务调度与协作</p>
          </div>
        </div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full space-y-6">
            <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-blue-600 rounded-2xl flex items-center justify-center">
              <Sparkles className="w-10 h-10 text-white" />
            </div>
            <div className="text-center">
              <h2 className="text-2xl font-semibold mb-2">欢迎使用超级智能体</h2>
              <p className="theme-text-tertiary max-w-md">
                我是您的智能助手，可以理解您的需求并协调专业智能体团队为您提供最佳解决方案。
              </p>
            </div>

            {/* Suggested Prompts */}
            <div className="grid grid-cols-2 gap-3 max-w-2xl">
              {suggestedPrompts.map((prompt, index) => (
                <button
                  key={index}
                  onClick={() => setInputValue(prompt)}
                  className="p-3 theme-bg-secondary hover-theme-bg-tertiary rounded-lg text-left transition-colors border theme-border-primary theme-text-primary"
                >
                  <span className="text-sm theme-text-primary">{prompt}</span>
                </button>
              ))}
            </div>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[70%] p-3 rounded-lg ${
                    message.sender === 'user'
                      ? 'bg-blue-600 text-white'
                      : 'theme-bg-secondary theme-text-primary border theme-border-primary'
                  }`}
                >
                  {message.sender === 'super-agent' && (
                    <div className="flex items-center space-x-2 mb-2">
                      <div className="w-6 h-6 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center">
                        <Sparkles className="w-3 h-3 text-white" />
                      </div>
                      <span className="text-sm font-medium">超级智能体</span>
                    </div>
                  )}
                  <p className="whitespace-pre-wrap">{message.content}</p>
                  <div className="text-xs theme-text-tertiary mt-1">
                    {message.timestamp.toLocaleTimeString()}
                  </div>
                </div>
              </div>
            ))}
            {isLoading && (
              <div className="flex justify-start">
                <div className="theme-bg-secondary p-3 rounded-lg border theme-border-primary">
                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-6 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center">
                      <Sparkles className="w-3 h-3 text-white" />
                    </div>
                    <span className="text-sm font-medium">超级智能体</span>
                  </div>
                  <div className="flex space-x-1 mt-2">
                    <div className="w-2 h-2 theme-bg-tertiary rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 theme-bg-tertiary rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 theme-bg-tertiary rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Input Area */}
      <div className="p-4 border-t theme-border-primary">
        <div className="flex items-end space-x-3">
          <div className="flex-1 relative">
            <textarea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="描述您的需求，我会为您协调最合适的智能体团队..."
              className="w-full p-3 theme-bg-secondary border theme-border-primary rounded-lg resize-none focus:outline-none focus:border-blue-500 theme-text-primary placeholder-theme-text-tertiary"
              rows={1}
              style={{ minHeight: '44px', maxHeight: '120px' }}
            />
          </div>
          <button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading}
            className="p-3 bg-blue-600 hover:bg-blue-700 disabled:theme-bg-tertiary disabled:cursor-not-allowed rounded-lg transition-colors text-white"
          >
            <Send className="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default SuperAgent;
