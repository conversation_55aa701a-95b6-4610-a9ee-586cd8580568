import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Search,
  Plus,
  Settings,
  Activity,
  Star,
  Clock,
  CheckCircle,
  Crown,
  Bot,
  Users,
  Zap,
  Shield,
  ArrowLeft
} from 'lucide-react';
import { cn } from "@/lib/utils";
import { request } from '@/utils/request';
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip";

interface Agent {
  id: string;
  name: string;
  description: string;
  api_type: string;
  api_key: string;
  model_name: string;
  avatar_url?: string;
  performance_score: number;
  status: 'active' | 'inactive' | 'maintenance';
  capabilities: string[];
  is_super_agent?: boolean;
  role?: 'admin' | 'member';
  created_at: string;
  updated_at: string;
}

// 模拟数据 - 实际应该从API获取
const mockAgents: Agent[] = [
  {
    id: 'super-agent-001',
    name: '超级智能体',
    description: '负责任务分配和协调的超级智能体，具有管理员权限，可以调度其他智能体参与群聊',
    api_type: 'openai',
    api_key: 'demo-key',
    model_name: 'gpt-4',
    avatar_url: '',
    performance_score: 0.95,
    status: 'active',
    capabilities: ['任务分配', '意图理解', '协调管理', '决策制定'],
    is_super_agent: true,
    role: 'admin',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'coding-agent-001',
    name: '编程助手',
    description: '专业的编程和技术问题解答Agent，擅长多种编程语言和框架',
    api_type: 'openai',
    api_key: 'demo-key',
    model_name: 'gpt-4',
    avatar_url: '',
    performance_score: 0.88,
    status: 'active',
    capabilities: ['编程', '代码审查', '技术文档', '调试'],
    role: 'member',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'writing-agent-001',
    name: '写作助手',
    description: '专业的文案写作和内容创作Agent，能够处理各种写作需求',
    api_type: 'claude',
    api_key: 'demo-key',
    model_name: 'claude-3-sonnet',
    avatar_url: '',
    performance_score: 0.92,
    status: 'active',
    capabilities: ['文案写作', '内容策划', '翻译', '校对'],
    role: 'member',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'analysis-agent-001',
    name: '数据分析师',
    description: '专业的数据分析和商业洞察Agent，擅长数据处理和可视化',
    api_type: 'qwen',
    api_key: 'demo-key',
    model_name: 'qwen-max',
    avatar_url: '',
    performance_score: 0.85,
    status: 'active',
    capabilities: ['数据分析', '统计建模', '可视化', '报告生成'],
    role: 'member',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 'design-agent-001',
    name: '设计顾问',
    description: '专业的UI/UX设计和创意指导Agent，提供设计建议和创意方案',
    api_type: 'deepseek',
    api_key: 'demo-key',
    model_name: 'deepseek-chat',
    avatar_url: '',
    performance_score: 0.90,
    status: 'inactive',
    capabilities: ['UI设计', 'UX设计', '创意策划', '品牌设计'],
    role: 'member',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }
];

const AgentsPage: React.FC = () => {
  const [agents, setAgents] = useState<Agent[]>(mockAgents);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const [showAgentDetail, setShowAgentDetail] = useState(false);
  const [showAddAgentDialog, setShowAddAgentDialog] = useState(false);

  // 初始化时设置agents数据
  useEffect(() => {
    setAgents(mockAgents);
  }, []);

  // 过滤Agent列表
  const filteredAgents = agents.filter(agent => {
    const matchesSearch = !searchTerm ||
      agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      agent.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      agent.capabilities.some(cap => cap.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesStatus = statusFilter === 'all' || agent.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchTerm(value);
  };

  // 处理筛选
  const handleStatusFilter = (status: string) => {
    setStatusFilter(status);
  };

  // 获取状态标签
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500/20 text-green-400 border-green-500/30">在线</Badge>;
      case 'inactive':
        return <Badge variant="secondary" className="theme-bg-tertiary theme-text-tertiary border theme-border-secondary">离线</Badge>;
      case 'maintenance':
        return <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">维护中</Badge>;
      default:
        return <Badge variant="secondary">未知</Badge>;
    }
  };

  // 获取API类型标签
  const getApiTypeBadge = (apiType: string) => {
    const colors = {
      openai: 'bg-blue-500/20 text-blue-400 border-blue-500/30',
      claude: 'bg-purple-500/20 text-purple-400 border-purple-500/30',
      qwen: 'bg-orange-500/20 text-orange-400 border-orange-500/30',
      deepseek: 'bg-pink-500/20 text-pink-400 border-pink-500/30'
    };
    return (
      <Badge className={colors[apiType] || 'theme-bg-tertiary theme-text-tertiary border theme-border-secondary'}>
        {apiType.toUpperCase()}
      </Badge>
    );
  };

  // 获取Agent头像
  const getAgentAvatar = (agent: Agent) => {
    if (agent.avatar_url) {
      return <AvatarImage src={agent.avatar_url} />;
    }

    // 根据Agent类型返回不同的图标
    if (agent.is_super_agent) {
      return <Crown className="h-5 w-5 text-yellow-400" />;
    }

    const iconMap = {
      'coding-agent-001': '💻',
      'writing-agent-001': '✍️',
      'analysis-agent-001': '📊',
      'design-agent-001': '🎨'
    };

    return <AvatarFallback className="bg-gradient-to-br from-blue-500/20 to-purple-500/20 theme-text-primary">
      {iconMap[agent.id] || agent.name[0]}
    </AvatarFallback>;
  };

  // 显示Agent详情
  const handleAgentClick = (agent: Agent) => {
    setSelectedAgent(agent);
    setShowAgentDetail(true);
  };

  return (
    <TooltipProvider>
      <div className="min-h-screen theme-bg-primary">
      <div className="container mx-auto p-6 space-y-6">
        {/* 页面标题和返回按钮 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => window.history.back()}
              className="theme-text-secondary hover-theme-text-primary hover-theme-bg-secondary"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold theme-text-primary">Agent Store</h1>
              <p className="theme-text-secondary mt-1">智能体商店 - 管理您的AI助手团队</p>
            </div>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setShowAddAgentDialog(true)}
              className="border theme-border-primary theme-text-secondary hover-theme-bg-tertiary hover-theme-text-primary"
            >
              <Plus className="w-4 h-4 mr-2" />
              添加智能体
            </Button>
          </div>
        </div>

        {/* 搜索和筛选 */}
        <div className="theme-bg-secondary backdrop-blur-sm rounded-lg border theme-border-primary p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 theme-text-tertiary w-4 h-4" />
                <Input
                  placeholder="搜索智能体名称、能力、描述..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10 theme-bg-tertiary border theme-border-primary theme-text-primary placeholder:theme-text-tertiary focus:border-blue-500"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                variant={statusFilter === 'all' ? "default" : "outline"}
                size="sm"
                onClick={() => handleStatusFilter('all')}
                className={statusFilter === 'all' ?
                  "bg-blue-600 hover:bg-blue-700 text-white" :
                  "border theme-border-primary theme-text-secondary hover-theme-bg-tertiary hover-theme-text-primary"
                }
              >
                全部
              </Button>
              <Button
                variant={statusFilter === 'active' ? "default" : "outline"}
                size="sm"
                onClick={() => handleStatusFilter('active')}
                className={statusFilter === 'active' ?
                  "bg-green-600 hover:bg-green-700 text-white" :
                  "border theme-border-primary theme-text-secondary hover-theme-bg-tertiary hover-theme-text-primary"
                }
              >
                在线
              </Button>
              <Button
                variant={statusFilter === 'inactive' ? "default" : "outline"}
                size="sm"
                onClick={() => handleStatusFilter('inactive')}
                className={statusFilter === 'inactive' ?
                  "bg-gray-600 hover:bg-gray-700 text-white" :
                  "border theme-border-primary theme-text-secondary hover-theme-bg-tertiary hover-theme-text-primary"
                }
              >
                离线
              </Button>
            </div>
          </div>
        </div>

        {/* Agent列表 */}
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
            <p className="mt-4 theme-text-tertiary">加载中...</p>
          </div>
        ) : (
          <div className="space-y-4">
            {/* 超级智能体 */}
            {filteredAgents.filter(agent => agent.is_super_agent).map((agent) => (
              <div
                key={agent.id}
                className="bg-gradient-to-r from-yellow-500/10 via-orange-500/10 to-red-500/10 backdrop-blur-sm rounded-lg border border-yellow-500/30 p-6 cursor-pointer hover:border-yellow-400/50 transition-all duration-200"
                onClick={() => handleAgentClick(agent)}
              >
                <div className="flex items-start gap-4">
                  <div className="relative">
                    <Avatar className="h-12 w-12 border-2 border-yellow-500/50">
                      {getAgentAvatar(agent)}
                    </Avatar>
                    <div className="absolute -top-1 -right-1 bg-yellow-500 rounded-full p-1">
                      <Crown className="h-3 w-3 text-yellow-900" />
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="text-lg font-semibold theme-text-primary">{agent.name}</h3>
                      <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
                        <Shield className="h-3 w-3 mr-1" />
                        管理员
                      </Badge>
                      {getStatusBadge(agent.status)}
                      {getApiTypeBadge(agent.api_type)}
                    </div>
                    <p className="theme-text-secondary text-sm mb-3 leading-relaxed">{agent.description}</p>
                    <div className="flex items-center justify-between">
                      <div className="flex flex-wrap gap-1">
                        {agent.capabilities.slice(0, 4).map((capability, index) => (
                          <Badge key={index} variant="secondary" className="text-xs theme-bg-tertiary theme-text-secondary border theme-border-primary">
                            {capability}
                          </Badge>
                        ))}
                      </div>
                      <div className="flex items-center gap-4 text-sm theme-text-tertiary">
                        <div className="flex items-center gap-1">
                          <Star className="w-4 h-4 text-yellow-500" />
                          <span>{agent.performance_score.toFixed(1)}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Activity className="w-4 h-4 text-blue-500" />
                          <span>超级智能体</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {/* 普通智能体 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredAgents.filter(agent => !agent.is_super_agent).map((agent) => (
                <div
                  key={agent.id}
                  className="theme-bg-secondary backdrop-blur-sm rounded-lg border theme-border-primary p-4 cursor-pointer hover-theme-border-secondary hover-theme-bg-tertiary transition-all duration-200"
                  onClick={() => handleAgentClick(agent)}
                >
                  <div className="flex items-start gap-3 mb-3">
                    <Avatar className="h-10 w-10">
                      {getAgentAvatar(agent)}
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium theme-text-primary truncate">{agent.name}</h3>
                      <div className="flex items-center gap-2 mt-1">
                        {getStatusBadge(agent.status)}
                        {getApiTypeBadge(agent.api_type)}
                      </div>
                    </div>
                  </div>
                  <p className="theme-text-secondary text-sm mb-3 line-clamp-2 leading-relaxed">{agent.description}</p>

                  {/* 能力标签 */}
                  <div className="flex flex-wrap gap-1 mb-3">
                    {agent.capabilities.slice(0, 3).map((capability, index) => (
                      <Badge key={index} variant="secondary" className="text-xs theme-bg-tertiary theme-text-secondary border theme-border-primary">
                        {capability}
                      </Badge>
                    ))}
                    {agent.capabilities.length > 3 && (
                      <Badge variant="secondary" className="text-xs theme-bg-tertiary theme-text-secondary border theme-border-primary">
                        +{agent.capabilities.length - 3}
                      </Badge>
                    )}
                  </div>

                  {/* 统计信息 */}
                  <div className="flex items-center justify-between text-sm theme-text-tertiary">
                    <div className="flex items-center gap-1">
                      <Star className="w-4 h-4 text-yellow-500" />
                      <span>{agent.performance_score.toFixed(1)}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="w-4 h-4 text-blue-500" />
                      <span>成员</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Agent详情对话框 */}
        {selectedAgent && showAgentDetail && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="theme-bg-secondary rounded-lg border theme-border-primary max-w-2xl w-full max-h-[80vh] overflow-auto">
              <div className="p-6">
                <div className="flex items-start justify-between mb-6">
                  <div className="flex items-center gap-4">
                    <Avatar className="h-16 w-16">
                      {getAgentAvatar(selectedAgent)}
                    </Avatar>
                    <div>
                      <h2 className="text-2xl font-bold theme-text-primary">{selectedAgent.name}</h2>
                      <div className="flex items-center gap-2 mt-2">
                        {selectedAgent.is_super_agent && (
                          <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
                            <Crown className="h-3 w-3 mr-1" />
                            超级智能体
                          </Badge>
                        )}
                        {getStatusBadge(selectedAgent.status)}
                        {getApiTypeBadge(selectedAgent.api_type)}
                      </div>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setShowAgentDetail(false)}
                    className="theme-text-tertiary hover-theme-text-primary"
                  >
                    ×
                  </Button>
                </div>

                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold theme-text-primary mb-2">描述</h3>
                    <p className="theme-text-secondary leading-relaxed">{selectedAgent.description}</p>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold theme-text-primary mb-3">能力标签</h3>
                    <div className="flex flex-wrap gap-2">
                      {selectedAgent.capabilities.map((capability, index) => (
                        <Badge key={index} className="bg-blue-500/20 text-blue-400 border-blue-500/30">
                          <Zap className="h-3 w-3 mr-1" />
                          {capability}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold theme-text-primary mb-3">技术信息</h3>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="theme-bg-tertiary rounded-lg p-3">
                        <div className="theme-text-tertiary mb-1">API类型</div>
                        <div className="theme-text-primary font-medium">{selectedAgent.api_type.toUpperCase()}</div>
                      </div>
                      <div className="theme-bg-tertiary rounded-lg p-3">
                        <div className="theme-text-tertiary mb-1">模型</div>
                        <div className="theme-text-primary font-medium">{selectedAgent.model_name}</div>
                      </div>
                      <div className="theme-bg-tertiary rounded-lg p-3">
                        <div className="theme-text-tertiary mb-1">性能评分</div>
                        <div className="theme-text-primary font-medium flex items-center gap-1">
                          <Star className="h-4 w-4 text-yellow-500" />
                          {selectedAgent.performance_score.toFixed(1)}
                        </div>
                      </div>
                      <div className="theme-bg-tertiary rounded-lg p-3">
                        <div className="theme-text-tertiary mb-1">角色</div>
                        <div className="theme-text-primary font-medium">
                          {selectedAgent.is_super_agent ? '管理员' : '成员'}
                        </div>
                      </div>
                    </div>
                  </div>

                  {selectedAgent.is_super_agent && (
                    <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4">
                      <h4 className="text-yellow-400 font-semibold mb-2 flex items-center gap-2">
                        <Shield className="h-4 w-4" />
                        超级智能体权限
                      </h4>
                      <ul className="theme-text-secondary text-sm space-y-1">
                        <li>• 负责任务分配和智能体调度</li>
                        <li>• 分析用户意图并选择合适的智能体</li>
                        <li>• 协调多个智能体协作完成复杂任务</li>
                        <li>• 监控群聊质量和智能体表现</li>
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 添加智能体对话框 */}
        {showAddAgentDialog && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="theme-bg-primary rounded-lg border theme-border-primary p-6 w-full max-w-md mx-4">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold theme-text-primary">添加智能体</h2>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowAddAgentDialog(false)}
                  className="theme-text-secondary hover-theme-text-primary"
                >
                  <Plus className="h-4 w-4 rotate-45" />
                </Button>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium theme-text-primary mb-2 block">
                    智能体名称
                  </label>
                  <Input
                    placeholder="输入智能体名称"
                    className="theme-bg-secondary theme-border-primary theme-text-primary"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium theme-text-primary mb-2 block">
                    描述
                  </label>
                  <Input
                    placeholder="输入智能体描述"
                    className="theme-bg-secondary theme-border-primary theme-text-primary"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium theme-text-primary mb-2 block">
                    API类型
                  </label>
                  <select className="w-full p-2 rounded-md theme-bg-secondary theme-border-primary theme-text-primary border">
                    <option value="openai">OpenAI</option>
                    <option value="claude">Claude</option>
                    <option value="qwen">Qwen</option>
                    <option value="deepseek">DeepSeek</option>
                  </select>
                </div>
                <div className="flex gap-2 pt-4">
                  <Button
                    variant="outline"
                    onClick={() => setShowAddAgentDialog(false)}
                    className="flex-1 theme-border-primary theme-text-secondary hover-theme-bg-tertiary"
                  >
                    取消
                  </Button>
                  <Button
                    onClick={() => {
                      // TODO: 实现添加智能体逻辑
                      setShowAddAgentDialog(false);
                    }}
                    className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    添加
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
    </TooltipProvider>
  );
};

export default AgentsPage;
