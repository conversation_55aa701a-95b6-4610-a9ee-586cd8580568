import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, Di<PERSON>Footer, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  Settings, 
  Brain, 
  Zap, 
  Target, 
  Clock,
  Users,
  Save,
  RefreshCw,
  AlertTriangle
} from 'lucide-react';
import { request } from '@/utils/request';
import { toast } from 'sonner';

interface SchedulingRule {
  id: string;
  name: string;
  condition: string;
  action: string;
  priority: number;
  enabled: boolean;
}

interface SchedulingConfig {
  max_concurrent_tasks: number;
  task_timeout_seconds: number;
  retry_failed_tasks: boolean;
  max_retries: number;
  load_balancing_strategy: 'round_robin' | 'capability_based' | 'performance_based';
  enable_auto_scaling: boolean;
  priority_weights: {
    capability_match: number;
    performance_score: number;
    current_load: number;
    response_time: number;
  };
  scheduling_rules: SchedulingRule[];
}

interface SchedulingConfigProps {
  open: boolean;
  onClose: () => void;
}

export const SchedulingConfig: React.FC<SchedulingConfigProps> = ({
  open,
  onClose
}) => {
  const [config, setConfig] = useState<SchedulingConfig>({
    max_concurrent_tasks: 10,
    task_timeout_seconds: 300,
    retry_failed_tasks: true,
    max_retries: 3,
    load_balancing_strategy: 'capability_based',
    enable_auto_scaling: true,
    priority_weights: {
      capability_match: 0.4,
      performance_score: 0.3,
      current_load: 0.2,
      response_time: 0.1
    },
    scheduling_rules: []
  });

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [newRule, setNewRule] = useState({
    name: '',
    condition: '',
    action: '',
    priority: 5,
    enabled: true
  });

  // 加载配置
  const loadConfig = async () => {
    try {
      setLoading(true);
      // 这里应该调用实际的配置API
      // const response = await request('/api/scheduling/config');
      // const data = await response.json();
      // if (data.success) {
      //   setConfig(data.data);
      // }
      
      // 模拟加载配置
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.error('加载配置失败:', error);
      toast.error('加载配置失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open) {
      loadConfig();
    }
  }, [open]);

  // 保存配置
  const saveConfig = async () => {
    try {
      setSaving(true);
      
      // 验证权重总和
      const totalWeight = Object.values(config.priority_weights).reduce((sum, weight) => sum + weight, 0);
      if (Math.abs(totalWeight - 1.0) > 0.01) {
        toast.error('优先级权重总和必须等于1.0');
        return;
      }

      // 这里应该调用实际的保存API
      // const response = await request('/api/scheduling/config', {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(config)
      // });

      // 模拟保存
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success('配置保存成功');
    } catch (error) {
      console.error('保存配置失败:', error);
      toast.error('保存配置失败');
    } finally {
      setSaving(false);
    }
  };

  // 添加调度规则
  const addRule = () => {
    if (!newRule.name || !newRule.condition || !newRule.action) {
      toast.error('请填写完整的规则信息');
      return;
    }

    const rule: SchedulingRule = {
      id: Date.now().toString(),
      ...newRule
    };

    setConfig(prev => ({
      ...prev,
      scheduling_rules: [...prev.scheduling_rules, rule]
    }));

    setNewRule({
      name: '',
      condition: '',
      action: '',
      priority: 5,
      enabled: true
    });

    toast.success('规则添加成功');
  };

  // 删除调度规则
  const deleteRule = (ruleId: string) => {
    setConfig(prev => ({
      ...prev,
      scheduling_rules: prev.scheduling_rules.filter(rule => rule.id !== ruleId)
    }));
    toast.success('规则删除成功');
  };

  // 更新权重
  const updateWeight = (key: keyof typeof config.priority_weights, value: number) => {
    setConfig(prev => ({
      ...prev,
      priority_weights: {
        ...prev.priority_weights,
        [key]: value
      }
    }));
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            调度策略配置
          </DialogTitle>
          <DialogDescription>
            配置智能任务分配和调度策略
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-2 text-gray-600">加载配置中...</p>
          </div>
        ) : (
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="basic">基础配置</TabsTrigger>
              <TabsTrigger value="weights">权重设置</TabsTrigger>
              <TabsTrigger value="rules">调度规则</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="w-5 h-5" />
                    基础参数
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="max-concurrent">最大并发任务数</Label>
                      <Input
                        id="max-concurrent"
                        type="number"
                        value={config.max_concurrent_tasks}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          max_concurrent_tasks: parseInt(e.target.value) || 10
                        }))}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="timeout">任务超时时间(秒)</Label>
                      <Input
                        id="timeout"
                        type="number"
                        value={config.task_timeout_seconds}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          task_timeout_seconds: parseInt(e.target.value) || 300
                        }))}
                      />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="retry-failed">重试失败任务</Label>
                        <p className="text-sm text-gray-600">自动重试失败的任务</p>
                      </div>
                      <Switch
                        id="retry-failed"
                        checked={config.retry_failed_tasks}
                        onCheckedChange={(checked) => setConfig(prev => ({
                          ...prev,
                          retry_failed_tasks: checked
                        }))}
                      />
                    </div>

                    {config.retry_failed_tasks && (
                      <div className="space-y-2">
                        <Label htmlFor="max-retries">最大重试次数</Label>
                        <Input
                          id="max-retries"
                          type="number"
                          value={config.max_retries}
                          onChange={(e) => setConfig(prev => ({
                            ...prev,
                            max_retries: parseInt(e.target.value) || 3
                          }))}
                        />
                      </div>
                    )}

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="auto-scaling">启用自动扩缩容</Label>
                        <p className="text-sm text-gray-600">根据负载自动调整Agent数量</p>
                      </div>
                      <Switch
                        id="auto-scaling"
                        checked={config.enable_auto_scaling}
                        onCheckedChange={(checked) => setConfig(prev => ({
                          ...prev,
                          enable_auto_scaling: checked
                        }))}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="w-5 h-5" />
                    负载均衡策略
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Label htmlFor="load-balancing">策略类型</Label>
                    <Select
                      value={config.load_balancing_strategy}
                      onValueChange={(value: any) => setConfig(prev => ({
                        ...prev,
                        load_balancing_strategy: value
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="round_robin">轮询分配</SelectItem>
                        <SelectItem value="capability_based">能力匹配</SelectItem>
                        <SelectItem value="performance_based">性能优先</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-sm text-gray-600">
                      {config.load_balancing_strategy === 'round_robin' && '按顺序轮流分配任务'}
                      {config.load_balancing_strategy === 'capability_based' && '根据Agent能力匹配分配'}
                      {config.load_balancing_strategy === 'performance_based' && '优先分配给高性能Agent'}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="weights" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Brain className="w-5 h-5" />
                    优先级权重设置
                  </CardTitle>
                  <CardDescription>
                    调整不同因素在Agent选择中的权重（总和必须为1.0）
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <Label htmlFor="capability-weight">能力匹配度</Label>
                        <span className="text-sm text-gray-600">
                          {(config.priority_weights.capability_match * 100).toFixed(0)}%
                        </span>
                      </div>
                      <Input
                        id="capability-weight"
                        type="range"
                        min="0"
                        max="1"
                        step="0.1"
                        value={config.priority_weights.capability_match}
                        onChange={(e) => updateWeight('capability_match', parseFloat(e.target.value))}
                      />
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <Label htmlFor="performance-weight">历史性能</Label>
                        <span className="text-sm text-gray-600">
                          {(config.priority_weights.performance_score * 100).toFixed(0)}%
                        </span>
                      </div>
                      <Input
                        id="performance-weight"
                        type="range"
                        min="0"
                        max="1"
                        step="0.1"
                        value={config.priority_weights.performance_score}
                        onChange={(e) => updateWeight('performance_score', parseFloat(e.target.value))}
                      />
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <Label htmlFor="load-weight">当前负载</Label>
                        <span className="text-sm text-gray-600">
                          {(config.priority_weights.current_load * 100).toFixed(0)}%
                        </span>
                      </div>
                      <Input
                        id="load-weight"
                        type="range"
                        min="0"
                        max="1"
                        step="0.1"
                        value={config.priority_weights.current_load}
                        onChange={(e) => updateWeight('current_load', parseFloat(e.target.value))}
                      />
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <Label htmlFor="response-weight">响应时间</Label>
                        <span className="text-sm text-gray-600">
                          {(config.priority_weights.response_time * 100).toFixed(0)}%
                        </span>
                      </div>
                      <Input
                        id="response-weight"
                        type="range"
                        min="0"
                        max="1"
                        step="0.1"
                        value={config.priority_weights.response_time}
                        onChange={(e) => updateWeight('response_time', parseFloat(e.target.value))}
                      />
                    </div>
                  </div>

                  <div className="p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">权重总和:</span>
                      <span className={`text-sm font-bold ${
                        Math.abs(Object.values(config.priority_weights).reduce((sum, weight) => sum + weight, 0) - 1.0) > 0.01
                          ? 'text-red-600' 
                          : 'text-green-600'
                      }`}>
                        {Object.values(config.priority_weights).reduce((sum, weight) => sum + weight, 0).toFixed(2)}
                      </span>
                      {Math.abs(Object.values(config.priority_weights).reduce((sum, weight) => sum + weight, 0) - 1.0) > 0.01 && (
                        <AlertTriangle className="w-4 h-4 text-red-600" />
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="rules" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="w-5 h-5" />
                    自定义调度规则
                  </CardTitle>
                  <CardDescription>
                    创建自定义的任务分配规则
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* 添加新规则 */}
                  <div className="border rounded-lg p-4 space-y-4">
                    <h4 className="font-medium">添加新规则</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="rule-name">规则名称</Label>
                        <Input
                          id="rule-name"
                          placeholder="如：高优先级任务优先"
                          value={newRule.name}
                          onChange={(e) => setNewRule(prev => ({ ...prev, name: e.target.value }))}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="rule-priority">优先级</Label>
                        <Input
                          id="rule-priority"
                          type="number"
                          min="1"
                          max="10"
                          value={newRule.priority}
                          onChange={(e) => setNewRule(prev => ({ ...prev, priority: parseInt(e.target.value) || 5 }))}
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="rule-condition">触发条件</Label>
                      <Textarea
                        id="rule-condition"
                        placeholder="如：task.priority >= 8"
                        value={newRule.condition}
                        onChange={(e) => setNewRule(prev => ({ ...prev, condition: e.target.value }))}
                        rows={2}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="rule-action">执行动作</Label>
                      <Textarea
                        id="rule-action"
                        placeholder="如：assign_to_best_agent()"
                        value={newRule.action}
                        onChange={(e) => setNewRule(prev => ({ ...prev, action: e.target.value }))}
                        rows={2}
                      />
                    </div>
                    <Button onClick={addRule} className="w-full">
                      添加规则
                    </Button>
                  </div>

                  {/* 现有规则列表 */}
                  <div className="space-y-3">
                    {config.scheduling_rules.map((rule) => (
                      <div key={rule.id} className="border rounded-lg p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <h4 className="font-medium">{rule.name}</h4>
                              <Badge variant={rule.enabled ? 'default' : 'secondary'}>
                                {rule.enabled ? '启用' : '禁用'}
                              </Badge>
                              <Badge variant="outline">优先级: {rule.priority}</Badge>
                            </div>
                            <div className="text-sm space-y-1">
                              <div><span className="font-medium">条件:</span> {rule.condition}</div>
                              <div><span className="font-medium">动作:</span> {rule.action}</div>
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deleteRule(rule.id)}
                            className="text-red-500 hover:text-red-700"
                          >
                            删除
                          </Button>
                        </div>
                      </div>
                    ))}
                    {config.scheduling_rules.length === 0 && (
                      <div className="text-center py-8 text-gray-500">
                        暂无自定义规则
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            取消
          </Button>
          <Button onClick={saveConfig} disabled={saving}>
            {saving ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                保存中...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                保存配置
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
