import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Activity,
  RefreshCw,
  Filter,
  BarChart3,
  Users,
  Zap
} from 'lucide-react';
import { request } from '@/utils/request';
import { toast } from 'sonner';

interface Task {
  id: number;
  task_type: string;
  task_description: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  priority: number;
  assigned_at: string;
  completed_at?: string;
  result?: string;
  agent_name: string;
  agent_avatar?: string;
  group_name: string;
  trigger_message: string;
  trigger_user_name: string;
  duration?: number;
  is_overdue: boolean;
}

interface TaskStats {
  total_tasks: number;
  pending_tasks: number;
  in_progress_tasks: number;
  completed_tasks: number;
  failed_tasks: number;
  overdue_tasks: number;
  success_rate: number;
  avg_completion_time: number;
}

interface TaskMonitorProps {
  open: boolean;
  onClose: () => void;
  agentId?: number;
  groupId?: number;
}

export const TaskMonitor: React.FC<TaskMonitorProps> = ({
  open,
  onClose,
  agentId,
  groupId
}) => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [stats, setStats] = useState<TaskStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  });

  // 加载任务列表
  const loadTasks = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
      });

      if (agentId) params.append('agent_id', agentId.toString());
      if (groupId) params.append('group_id', groupId.toString());
      if (statusFilter !== 'all') params.append('status', statusFilter);

      const response = await request(`/api/tasks/list?${params.toString()}`);
      const data = await response.json();

      if (data.success) {
        setTasks(data.data.tasks);
        setStats(data.data.stats);
        setPagination(data.data.pagination);
      } else {
        toast.error('加载任务列表失败');
      }
    } catch (error) {
      console.error('加载任务列表错误:', error);
      toast.error('网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open) {
      loadTasks();
    }
  }, [open, agentId, groupId, statusFilter, pagination.page]);

  // 获取状态标签
  const getStatusBadge = (status: string, isOverdue: boolean = false) => {
    if (isOverdue) {
      return <Badge variant="destructive" className="flex items-center gap-1">
        <AlertTriangle className="w-3 h-3" />
        超时
      </Badge>;
    }

    switch (status) {
      case 'pending':
        return <Badge variant="secondary" className="flex items-center gap-1">
          <Clock className="w-3 h-3" />
          等待中
        </Badge>;
      case 'in_progress':
        return <Badge variant="default" className="flex items-center gap-1">
          <Activity className="w-3 h-3" />
          进行中
        </Badge>;
      case 'completed':
        return <Badge variant="default" className="bg-green-500 flex items-center gap-1">
          <CheckCircle className="w-3 h-3" />
          已完成
        </Badge>;
      case 'failed':
        return <Badge variant="destructive" className="flex items-center gap-1">
          <XCircle className="w-3 h-3" />
          失败
        </Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // 获取优先级颜色
  const getPriorityColor = (priority: number) => {
    if (priority >= 8) return 'text-red-600';
    if (priority >= 6) return 'text-orange-600';
    if (priority >= 4) return 'text-yellow-600';
    return 'text-gray-600';
  };

  // 格式化持续时间
  const formatDuration = (duration: number) => {
    if (duration < 1000) return `${duration}ms`;
    if (duration < 60000) return `${Math.round(duration / 1000)}s`;
    return `${Math.round(duration / 60000)}m`;
  };

  // 格式化时间
  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            任务监控
            {agentId && ' - Agent任务'}
            {groupId && ' - 群组任务'}
          </DialogTitle>
          <DialogDescription>
            实时监控任务执行状态和性能指标
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* 统计概览 */}
          {stats && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Activity className="w-5 h-5 text-blue-500" />
                    <div>
                      <div className="text-2xl font-bold">{stats.total_tasks}</div>
                      <div className="text-sm text-gray-600">总任务数</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-5 h-5 text-green-500" />
                    <div>
                      <div className="text-2xl font-bold">{stats.success_rate}%</div>
                      <div className="text-sm text-gray-600">成功率</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Clock className="w-5 h-5 text-purple-500" />
                    <div>
                      <div className="text-2xl font-bold">{formatDuration(stats.avg_completion_time)}</div>
                      <div className="text-sm text-gray-600">平均耗时</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="w-5 h-5 text-red-500" />
                    <div>
                      <div className="text-2xl font-bold">{stats.overdue_tasks}</div>
                      <div className="text-sm text-gray-600">超时任务</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* 筛选和操作 */}
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Filter className="w-4 h-4" />
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    <SelectItem value="pending">等待中</SelectItem>
                    <SelectItem value="in_progress">进行中</SelectItem>
                    <SelectItem value="completed">已完成</SelectItem>
                    <SelectItem value="failed">失败</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={loadTasks}
              disabled={loading}
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
          </div>

          {/* 任务列表 */}
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
              <p className="mt-2 text-gray-600">加载中...</p>
            </div>
          ) : tasks.length > 0 ? (
            <div className="space-y-3">
              {tasks.map((task) => (
                <Card key={task.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            {task.task_type}
                          </Badge>
                          {getStatusBadge(task.status, task.is_overdue)}
                          <span className={`text-xs font-medium ${getPriorityColor(task.priority)}`}>
                            优先级: {task.priority}
                          </span>
                        </div>
                        
                        <div>
                          <p className="font-medium text-sm">{task.task_description}</p>
                          <p className="text-xs text-gray-600 mt-1">
                            触发消息: "{task.trigger_message?.substring(0, 50)}..."
                          </p>
                        </div>

                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <div className="flex items-center gap-1">
                            <Users className="w-3 h-3" />
                            <span>{task.agent_name}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            <span>{formatTime(task.assigned_at)}</span>
                          </div>
                          {task.duration && (
                            <div className="flex items-center gap-1">
                              <Zap className="w-3 h-3" />
                              <span>{formatDuration(task.duration)}</span>
                            </div>
                          )}
                        </div>

                        {task.result && (
                          <div className="bg-gray-50 p-2 rounded text-xs">
                            <span className="font-medium">结果: </span>
                            {task.result.substring(0, 100)}
                            {task.result.length > 100 && '...'}
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              暂无任务数据
            </div>
          )}

          {/* 分页 */}
          {pagination.pages > 1 && (
            <div className="flex justify-center gap-2">
              <Button
                variant="outline"
                size="sm"
                disabled={pagination.page <= 1}
                onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
              >
                上一页
              </Button>
              <span className="flex items-center px-4 text-sm">
                第 {pagination.page} 页，共 {pagination.pages} 页
              </span>
              <Button
                variant="outline"
                size="sm"
                disabled={pagination.page >= pagination.pages}
                onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
              >
                下一页
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
