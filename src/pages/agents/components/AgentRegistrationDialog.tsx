import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { X, Plus, TestTube } from 'lucide-react';
import { request } from '@/utils/request';
import { toast } from 'sonner';

interface AgentRegistrationDialogProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface AgentFormData {
  name: string;
  display_name: string;
  description: string;
  api_url: string;
  api_key: string;
  api_type: 'dify' | 'fastgpt' | 'custom';
  avatar_url: string;
  capabilities: string[];
}

export const AgentRegistrationDialog: React.FC<AgentRegistrationDialogProps> = ({
  open,
  onClose,
  onSuccess
}) => {
  const [formData, setFormData] = useState<AgentFormData>({
    name: '',
    display_name: '',
    description: '',
    api_url: '',
    api_key: '',
    api_type: 'dify',
    avatar_url: '',
    capabilities: []
  });

  const [newCapability, setNewCapability] = useState('');
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);

  // 重置表单
  const resetForm = () => {
    setFormData({
      name: '',
      display_name: '',
      description: '',
      api_url: '',
      api_key: '',
      api_type: 'dify',
      avatar_url: '',
      capabilities: []
    });
    setNewCapability('');
  };

  // 处理表单字段变化
  const handleInputChange = (field: keyof AgentFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 添加能力标签
  const addCapability = () => {
    if (newCapability.trim() && !formData.capabilities.includes(newCapability.trim())) {
      setFormData(prev => ({
        ...prev,
        capabilities: [...prev.capabilities, newCapability.trim()]
      }));
      setNewCapability('');
    }
  };

  // 删除能力标签
  const removeCapability = (capability: string) => {
    setFormData(prev => ({
      ...prev,
      capabilities: prev.capabilities.filter(c => c !== capability)
    }));
  };

  // 测试Agent连接
  const testConnection = async () => {
    if (!formData.api_url) {
      toast.error('请先填写API地址');
      return;
    }

    setTesting(true);
    try {
      // 这里可以调用测试API
      // 暂时模拟测试
      await new Promise(resolve => setTimeout(resolve, 2000));
      toast.success('连接测试成功！');
    } catch (error) {
      toast.error('连接测试失败，请检查API配置');
    } finally {
      setTesting(false);
    }
  };

  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // 表单验证
    if (!formData.name || !formData.display_name || !formData.api_url) {
      toast.error('请填写必填字段');
      return;
    }

    setLoading(true);
    try {
      const response = await request('/api/agents/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success('Agent注册成功！');
        resetForm();
        onSuccess();
      } else {
        toast.error(data.message || 'Agent注册失败');
      }
    } catch (error) {
      console.error('Agent注册错误:', error);
      toast.error('网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 处理对话框关闭
  const handleClose = () => {
    if (!loading) {
      resetForm();
      onClose();
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>注册新Agent</DialogTitle>
          <DialogDescription>
            添加一个新的智能体到您的Agent Store中
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 基本信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">基本信息</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Agent名称 *</Label>
                <Input
                  id="name"
                  placeholder="唯一标识符，如：my-assistant"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  required
                />
                <p className="text-xs text-gray-500">用作唯一标识，不可重复</p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="display_name">显示名称 *</Label>
                <Input
                  id="display_name"
                  placeholder="如：我的AI助手"
                  value={formData.display_name}
                  onChange={(e) => handleInputChange('display_name', e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">描述</Label>
              <Textarea
                id="description"
                placeholder="描述这个Agent的功能和特点..."
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="avatar_url">头像URL</Label>
              <Input
                id="avatar_url"
                placeholder="https://example.com/avatar.png"
                value={formData.avatar_url}
                onChange={(e) => handleInputChange('avatar_url', e.target.value)}
              />
            </div>
          </div>

          {/* API配置 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">API配置</h3>
            
            <div className="space-y-2">
              <Label htmlFor="api_type">API类型 *</Label>
              <Select
                value={formData.api_type}
                onValueChange={(value: 'dify' | 'fastgpt' | 'custom') => 
                  handleInputChange('api_type', value)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="dify">Dify</SelectItem>
                  <SelectItem value="fastgpt">FastGPT</SelectItem>
                  <SelectItem value="custom">自定义API</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="api_url">API地址 *</Label>
              <div className="flex gap-2">
                <Input
                  id="api_url"
                  placeholder="https://api.example.com/v1"
                  value={formData.api_url}
                  onChange={(e) => handleInputChange('api_url', e.target.value)}
                  required
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={testConnection}
                  disabled={testing || !formData.api_url}
                  className="flex items-center gap-2"
                >
                  <TestTube className="w-4 h-4" />
                  {testing ? '测试中...' : '测试'}
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="api_key">API密钥</Label>
              <Input
                id="api_key"
                type="password"
                placeholder="输入API密钥..."
                value={formData.api_key}
                onChange={(e) => handleInputChange('api_key', e.target.value)}
              />
            </div>
          </div>

          {/* 能力标签 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">能力标签</h3>
            
            <div className="flex gap-2">
              <Input
                placeholder="添加能力标签，如：文本生成"
                value={newCapability}
                onChange={(e) => setNewCapability(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addCapability())}
              />
              <Button
                type="button"
                variant="outline"
                onClick={addCapability}
                disabled={!newCapability.trim()}
              >
                <Plus className="w-4 h-4" />
              </Button>
            </div>

            {formData.capabilities.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {formData.capabilities.map((capability, index) => (
                  <Badge key={index} variant="secondary" className="flex items-center gap-1">
                    {capability}
                    <X
                      className="w-3 h-3 cursor-pointer hover:text-red-500"
                      onClick={() => removeCapability(capability)}
                    />
                  </Badge>
                ))}
              </div>
            )}
          </div>
        </form>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={loading}>
            取消
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading ? '注册中...' : '注册Agent'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
