import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { X, Plus, Save, TestTube, Trash2 } from 'lucide-react';
import { request } from '@/utils/request';
import { toast } from 'sonner';

interface Agent {
  id: number;
  name: string;
  display_name: string;
  description: string;
  api_url: string;
  api_type: string;
  avatar_url?: string;
  status: number;
  capabilities: string[];
  avg_rating: number;
  total_tasks: number;
  completed_tasks: number;
  success_rate: number;
  created_at: string;
  last_used_at?: string;
}

interface AgentDetailDialogProps {
  open: boolean;
  onClose: () => void;
  agent: Agent;
  onUpdate: () => void;
}

export const AgentDetailDialog: React.FC<AgentDetailDialogProps> = ({
  open,
  onClose,
  agent,
  onUpdate
}) => {
  const [formData, setFormData] = useState({
    display_name: '',
    description: '',
    api_url: '',
    api_key: '',
    avatar_url: '',
    status: 1,
    capabilities: [] as string[]
  });

  const [newCapability, setNewCapability] = useState('');
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState<any>(null);

  // 初始化表单数据
  useEffect(() => {
    if (agent) {
      setFormData({
        display_name: agent.display_name,
        description: agent.description,
        api_url: agent.api_url,
        api_key: '', // 不显示现有密钥
        avatar_url: agent.avatar_url || '',
        status: agent.status,
        capabilities: [...agent.capabilities]
      });
    }
  }, [agent]);

  // 处理表单字段变化
  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 添加能力标签
  const addCapability = () => {
    if (newCapability.trim() && !formData.capabilities.includes(newCapability.trim())) {
      setFormData(prev => ({
        ...prev,
        capabilities: [...prev.capabilities, newCapability.trim()]
      }));
      setNewCapability('');
    }
  };

  // 删除能力标签
  const removeCapability = (capability: string) => {
    setFormData(prev => ({
      ...prev,
      capabilities: prev.capabilities.filter(c => c !== capability)
    }));
  };

  // 测试Agent连接
  const testAgent = async () => {
    setTesting(true);
    setTestResult(null);
    
    try {
      const response = await request(`/api/agents/${agent.id}/test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: '你好，这是一个连接测试。',
          test_type: 'capability'
        }),
      });

      const data = await response.json();
      setTestResult(data.data);
      
      if (data.success && data.data.success) {
        toast.success('Agent测试成功！');
      } else {
        toast.error('Agent测试失败：' + (data.data.error_message || '未知错误'));
      }
    } catch (error) {
      console.error('Agent测试错误:', error);
      toast.error('测试请求失败');
      setTestResult({ success: false, error_message: '网络错误' });
    } finally {
      setTesting(false);
    }
  };

  // 保存更改
  const handleSave = async () => {
    setLoading(true);
    try {
      const updateData = { ...formData };
      // 如果API密钥为空，不更新它
      if (!updateData.api_key) {
        delete updateData.api_key;
      }

      const response = await request(`/api/agents/${agent.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success('Agent更新成功！');
        onUpdate();
        onClose();
      } else {
        toast.error(data.message || 'Agent更新失败');
      }
    } catch (error) {
      console.error('Agent更新错误:', error);
      toast.error('网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 删除Agent
  const handleDelete = async () => {
    if (!confirm('确定要删除这个Agent吗？此操作不可恢复。')) {
      return;
    }

    setLoading(true);
    try {
      const response = await request(`/api/agents/${agent.id}`, {
        method: 'DELETE',
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success('Agent删除成功！');
        onUpdate();
        onClose();
      } else {
        toast.error(data.message || 'Agent删除失败');
      }
    } catch (error) {
      console.error('Agent删除错误:', error);
      toast.error('网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Agent配置 - {agent.display_name}</DialogTitle>
          <DialogDescription>
            管理Agent的配置、能力和状态
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="config" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="config">基本配置</TabsTrigger>
            <TabsTrigger value="test">连接测试</TabsTrigger>
            <TabsTrigger value="info">详细信息</TabsTrigger>
          </TabsList>

          <TabsContent value="config" className="space-y-6">
            {/* 基本信息 */}
            <Card>
              <CardHeader>
                <CardTitle>基本信息</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="display_name">显示名称</Label>
                    <Input
                      id="display_name"
                      value={formData.display_name}
                      onChange={(e) => handleInputChange('display_name', e.target.value)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="status">状态</Label>
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={formData.status === 1}
                        onCheckedChange={(checked) => 
                          handleInputChange('status', checked ? 1 : 0)
                        }
                      />
                      <span>{formData.status === 1 ? '启用' : '禁用'}</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">描述</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="avatar_url">头像URL</Label>
                  <Input
                    id="avatar_url"
                    value={formData.avatar_url}
                    onChange={(e) => handleInputChange('avatar_url', e.target.value)}
                  />
                </div>
              </CardContent>
            </Card>

            {/* API配置 */}
            <Card>
              <CardHeader>
                <CardTitle>API配置</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="api_url">API地址</Label>
                  <Input
                    id="api_url"
                    value={formData.api_url}
                    onChange={(e) => handleInputChange('api_url', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="api_key">API密钥</Label>
                  <Input
                    id="api_key"
                    type="password"
                    placeholder="留空则不更改现有密钥"
                    value={formData.api_key}
                    onChange={(e) => handleInputChange('api_key', e.target.value)}
                  />
                </div>
              </CardContent>
            </Card>

            {/* 能力管理 */}
            <Card>
              <CardHeader>
                <CardTitle>能力标签</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <Input
                    placeholder="添加能力标签"
                    value={newCapability}
                    onChange={(e) => setNewCapability(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addCapability())}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={addCapability}
                    disabled={!newCapability.trim()}
                  >
                    <Plus className="w-4 h-4" />
                  </Button>
                </div>

                {formData.capabilities.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {formData.capabilities.map((capability, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center gap-1">
                        {capability}
                        <X
                          className="w-3 h-3 cursor-pointer hover:text-red-500"
                          onClick={() => removeCapability(capability)}
                        />
                      </Badge>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="test" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>连接测试</CardTitle>
                <CardDescription>
                  测试Agent的API连接和响应能力
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button
                  onClick={testAgent}
                  disabled={testing}
                  className="flex items-center gap-2"
                >
                  <TestTube className="w-4 h-4" />
                  {testing ? '测试中...' : '开始测试'}
                </Button>

                {testResult && (
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <span className={`w-3 h-3 rounded-full ${
                        testResult.success ? 'bg-green-500' : 'bg-red-500'
                      }`}></span>
                      <span className="font-medium">
                        {testResult.success ? '测试成功' : '测试失败'}
                      </span>
                    </div>
                    
                    <div className="text-sm space-y-2">
                      <div>响应时间: {testResult.response_time}ms</div>
                      
                      {testResult.response_content && (
                        <div>
                          <div className="font-medium">响应内容:</div>
                          <div className="bg-gray-50 p-2 rounded text-xs">
                            {testResult.response_content}
                          </div>
                        </div>
                      )}
                      
                      {testResult.error_message && (
                        <div>
                          <div className="font-medium text-red-600">错误信息:</div>
                          <div className="text-red-600">{testResult.error_message}</div>
                        </div>
                      )}
                      
                      {testResult.capabilities_detected && testResult.capabilities_detected.length > 0 && (
                        <div>
                          <div className="font-medium">检测到的能力:</div>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {testResult.capabilities_detected.map((cap, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {cap}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="info" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Agent信息</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Agent ID:</span> {agent.id}
                  </div>
                  <div>
                    <span className="font-medium">唯一名称:</span> {agent.name}
                  </div>
                  <div>
                    <span className="font-medium">API类型:</span> {agent.api_type}
                  </div>
                  <div>
                    <span className="font-medium">创建时间:</span> {new Date(agent.created_at).toLocaleString()}
                  </div>
                  <div>
                    <span className="font-medium">最后使用:</span> {
                      agent.last_used_at ? new Date(agent.last_used_at).toLocaleString() : '从未使用'
                    }
                  </div>
                  <div>
                    <span className="font-medium">平均评分:</span> {agent.avg_rating.toFixed(1)}/5.0
                  </div>
                  <div>
                    <span className="font-medium">总任务数:</span> {agent.total_tasks}
                  </div>
                  <div>
                    <span className="font-medium">成功率:</span> {agent.success_rate}%
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-red-600">危险操作</CardTitle>
                <CardDescription>
                  以下操作不可恢复，请谨慎操作
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  variant="destructive"
                  onClick={handleDelete}
                  disabled={loading}
                  className="flex items-center gap-2"
                >
                  <Trash2 className="w-4 h-4" />
                  删除Agent
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            取消
          </Button>
          <Button onClick={handleSave} disabled={loading} className="flex items-center gap-2">
            <Save className="w-4 h-4" />
            {loading ? '保存中...' : '保存更改'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
