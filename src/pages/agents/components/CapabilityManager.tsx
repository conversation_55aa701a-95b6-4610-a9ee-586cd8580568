import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Plus, 
  X, 
  Brain, 
  User, 
  Trash2, 
  RefreshCw,
  Zap,
  Target
} from 'lucide-react';
import { request } from '@/utils/request';
import { toast } from 'sonner';

interface Capability {
  id: number;
  name: string;
  type: 'manual' | 'auto';
  description: string;
  confidence_score: number;
  created_at: string;
  updated_at: string;
}

interface CapabilityManagerProps {
  agentId: number;
  agentName: string;
  open: boolean;
  onClose: () => void;
}

export const CapabilityManager: React.FC<CapabilityManagerProps> = ({
  agentId,
  agentName,
  open,
  onClose
}) => {
  const [capabilities, setCapabilities] = useState<Capability[]>([]);
  const [loading, setLoading] = useState(false);
  const [analyzing, setAnalyzing] = useState(false);
  const [newCapability, setNewCapability] = useState({
    name: '',
    type: 'manual' as 'manual' | 'auto',
    description: '',
    confidence_score: 1.0
  });
  const [sampleResponses, setSampleResponses] = useState('');

  // 加载能力列表
  const loadCapabilities = async () => {
    try {
      setLoading(true);
      const response = await request(`/api/agents/capabilities?agent_id=${agentId}`);
      const data = await response.json();
      
      if (data.success) {
        setCapabilities(data.data);
      } else {
        toast.error('加载能力列表失败');
      }
    } catch (error) {
      console.error('加载能力列表错误:', error);
      toast.error('网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open && agentId) {
      loadCapabilities();
    }
  }, [open, agentId]);

  // 添加手动能力
  const addManualCapability = async () => {
    if (!newCapability.name.trim()) {
      toast.error('请输入能力名称');
      return;
    }

    try {
      const response = await request('/api/agents/capabilities', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          agent_id: agentId,
          capabilities: [newCapability]
        })
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success('能力添加成功');
        setCapabilities(data.data.capabilities);
        setNewCapability({
          name: '',
          type: 'manual',
          description: '',
          confidence_score: 1.0
        });
      } else {
        toast.error(data.message || '能力添加失败');
      }
    } catch (error) {
      console.error('添加能力错误:', error);
      toast.error('网络错误，请稍后重试');
    }
  };

  // 自动分析能力
  const analyzeCapabilities = async () => {
    if (!sampleResponses.trim()) {
      toast.error('请输入样本响应内容');
      return;
    }

    try {
      setAnalyzing(true);
      const responses = sampleResponses.split('\n').filter(r => r.trim());
      
      const response = await request('/api/agents/capabilities', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          agent_id: agentId,
          sample_responses: responses,
          force_update: false
        })
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success(`检测到 ${data.data.detected_capabilities.length} 个新能力`);
        setCapabilities(data.data.capabilities);
        setSampleResponses('');
      } else {
        toast.error(data.message || '能力分析失败');
      }
    } catch (error) {
      console.error('能力分析错误:', error);
      toast.error('网络错误，请稍后重试');
    } finally {
      setAnalyzing(false);
    }
  };

  // 删除能力
  const deleteCapability = async (capabilityName: string) => {
    if (!confirm(`确定要删除能力"${capabilityName}"吗？`)) {
      return;
    }

    try {
      const response = await request(
        `/api/agents/capabilities?agent_id=${agentId}&capability_name=${encodeURIComponent(capabilityName)}`,
        { method: 'DELETE' }
      );

      const data = await response.json();
      
      if (data.success) {
        toast.success('能力删除成功');
        setCapabilities(prev => prev.filter(cap => cap.name !== capabilityName));
      } else {
        toast.error(data.message || '能力删除失败');
      }
    } catch (error) {
      console.error('删除能力错误:', error);
      toast.error('网络错误，请稍后重试');
    }
  };

  // 获取能力类型图标
  const getCapabilityIcon = (type: string) => {
    return type === 'auto' ? (
      <Brain className="w-4 h-4 text-blue-500" />
    ) : (
      <User className="w-4 h-4 text-green-500" />
    );
  };

  // 获取置信度颜色
  const getConfidenceColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600';
    if (score >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Target className="w-5 h-5" />
            能力管理 - {agentName}
          </DialogTitle>
          <DialogDescription>
            管理Agent的能力标签，支持手动添加和自动检测
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* 当前能力列表 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>当前能力</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={loadCapabilities}
                  disabled={loading}
                >
                  <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900 mx-auto"></div>
                  <p className="mt-2 text-sm text-gray-600">加载中...</p>
                </div>
              ) : capabilities.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {capabilities.map((capability) => (
                    <div
                      key={capability.id}
                      className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
                    >
                      <div className="flex items-center gap-2 flex-1">
                        {getCapabilityIcon(capability.type)}
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{capability.name}</span>
                            <Badge variant={capability.type === 'auto' ? 'default' : 'secondary'} className="text-xs">
                              {capability.type === 'auto' ? '自动' : '手动'}
                            </Badge>
                          </div>
                          {capability.description && (
                            <p className="text-xs text-gray-600 mt-1">{capability.description}</p>
                          )}
                          <div className="flex items-center gap-2 mt-1">
                            <span className="text-xs text-gray-500">置信度:</span>
                            <span className={`text-xs font-medium ${getConfidenceColor(capability.confidence_score)}`}>
                              {(capability.confidence_score * 100).toFixed(0)}%
                            </span>
                          </div>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => deleteCapability(capability.name)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  暂无能力标签，请添加或自动检测
                </div>
              )}
            </CardContent>
          </Card>

          {/* 手动添加能力 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Plus className="w-5 h-5" />
                手动添加能力
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="capability-name">能力名称</Label>
                  <Input
                    id="capability-name"
                    placeholder="如：文本生成"
                    value={newCapability.name}
                    onChange={(e) => setNewCapability(prev => ({ ...prev, name: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="confidence-score">置信度</Label>
                  <Select
                    value={newCapability.confidence_score.toString()}
                    onValueChange={(value) => setNewCapability(prev => ({ ...prev, confidence_score: parseFloat(value) }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1.0">100% - 非常确定</SelectItem>
                      <SelectItem value="0.8">80% - 比较确定</SelectItem>
                      <SelectItem value="0.6">60% - 一般确定</SelectItem>
                      <SelectItem value="0.4">40% - 不太确定</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="capability-description">能力描述</Label>
                <Textarea
                  id="capability-description"
                  placeholder="描述这个能力的具体功能..."
                  value={newCapability.description}
                  onChange={(e) => setNewCapability(prev => ({ ...prev, description: e.target.value }))}
                  rows={2}
                />
              </div>
              <Button onClick={addManualCapability} className="w-full">
                <Plus className="w-4 h-4 mr-2" />
                添加能力
              </Button>
            </CardContent>
          </Card>

          {/* 自动检测能力 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="w-5 h-5" />
                自动检测能力
              </CardTitle>
              <CardDescription>
                输入Agent的样本响应内容，系统将自动分析并检测可能的能力
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="sample-responses">样本响应内容</Label>
                <Textarea
                  id="sample-responses"
                  placeholder="请输入Agent的多个响应示例，每行一个..."
                  value={sampleResponses}
                  onChange={(e) => setSampleResponses(e.target.value)}
                  rows={6}
                />
                <p className="text-xs text-gray-500">
                  提示：输入更多样本可以提高检测准确性
                </p>
              </div>
              <Button 
                onClick={analyzeCapabilities} 
                disabled={analyzing || !sampleResponses.trim()}
                className="w-full"
              >
                {analyzing ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    分析中...
                  </>
                ) : (
                  <>
                    <Brain className="w-4 h-4 mr-2" />
                    开始分析
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
