import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Activity, 
  Star, 
  Clock, 
  CheckCircle, 
  XCircle, 
  TrendingUp,
  Calendar,
  MessageSquare
} from 'lucide-react';
import { request } from '@/utils/request';

interface Agent {
  id: number;
  name: string;
  display_name: string;
}

interface AgentStatsDialogProps {
  open: boolean;
  onClose: () => void;
  agent: Agent;
}

interface AgentStats {
  agent_info: {
    id: number;
    name: string;
    display_name: string;
    capabilities: string[];
  };
  summary: {
    total_tasks: number;
    completed_tasks: number;
    failed_tasks: number;
    success_rate: number;
    avg_response_time: number;
    total_ratings: number;
    avg_rating: number;
  };
  daily_performance: Array<{
    date: string;
    total_tasks: number;
    completed_tasks: number;
    failed_tasks: number;
    avg_rating: number;
    avg_response_time: number;
  }>;
  task_type_distribution: Array<{
    task_type: string;
    count: number;
    avg_rating: number;
  }>;
  recent_ratings: Array<{
    id: number;
    rating: number;
    feedback: string;
    task_type: string;
    user_name: string;
    created_at: string;
  }>;
}

export const AgentStatsDialog: React.FC<AgentStatsDialogProps> = ({
  open,
  onClose,
  agent
}) => {
  const [stats, setStats] = useState<AgentStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [timeRange, setTimeRange] = useState('30');

  // 加载统计数据
  const loadStats = async () => {
    setLoading(true);
    try {
      const response = await request(`/api/agents/stats?agent_id=${agent.id}&days=${timeRange}`);
      const data = await response.json();
      
      if (data.success) {
        setStats(data.data);
      } else {
        console.error('加载统计数据失败');
      }
    } catch (error) {
      console.error('加载统计数据错误:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open && agent) {
      loadStats();
    }
  }, [open, agent, timeRange]);

  // 获取评分星级
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${
          i < Math.floor(rating) 
            ? 'text-yellow-500 fill-current' 
            : 'text-gray-300'
        }`}
      />
    ));
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric'
    });
  };

  if (!stats && !loading) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Agent统计 - {agent.display_name}</DialogTitle>
          <DialogDescription>
            查看Agent的详细性能统计和使用情况
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* 时间范围选择 */}
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">统计概览</h3>
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7">最近7天</SelectItem>
                <SelectItem value="30">最近30天</SelectItem>
                <SelectItem value="90">最近90天</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
              <p className="mt-2 text-gray-600">加载统计数据中...</p>
            </div>
          ) : stats ? (
            <>
              {/* 核心指标 */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2">
                      <Activity className="w-5 h-5 text-blue-500" />
                      <div>
                        <div className="text-2xl font-bold">{stats.summary.total_tasks}</div>
                        <div className="text-sm text-gray-600">总任务数</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-5 h-5 text-green-500" />
                      <div>
                        <div className="text-2xl font-bold">{stats.summary.success_rate}%</div>
                        <div className="text-sm text-gray-600">成功率</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2">
                      <Star className="w-5 h-5 text-yellow-500" />
                      <div>
                        <div className="text-2xl font-bold">{stats.summary.avg_rating.toFixed(1)}</div>
                        <div className="text-sm text-gray-600">平均评分</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2">
                      <Clock className="w-5 h-5 text-purple-500" />
                      <div>
                        <div className="text-2xl font-bold">{stats.summary.avg_response_time}ms</div>
                        <div className="text-sm text-gray-600">平均响应</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* 任务类型分布 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="w-5 h-5" />
                    任务类型分布
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {stats.task_type_distribution.map((task, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <Badge variant="outline">{task.task_type || '未分类'}</Badge>
                          <span className="text-sm text-gray-600">{task.count} 次任务</span>
                        </div>
                        <div className="flex items-center gap-2">
                          {renderStars(task.avg_rating || 0)}
                          <span className="text-sm font-medium">
                            {(task.avg_rating || 0).toFixed(1)}
                          </span>
                        </div>
                      </div>
                    ))}
                    {stats.task_type_distribution.length === 0 && (
                      <div className="text-center py-4 text-gray-500">
                        暂无任务数据
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* 每日性能趋势 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="w-5 h-5" />
                    每日性能趋势
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {stats.daily_performance.slice(0, 10).map((day, index) => (
                      <div key={index} className="flex items-center justify-between p-2 hover:bg-gray-50 rounded">
                        <div className="flex items-center gap-4">
                          <span className="text-sm font-medium w-16">
                            {formatDate(day.date)}
                          </span>
                          <div className="flex items-center gap-2 text-sm">
                            <span className="text-blue-600">{day.total_tasks} 任务</span>
                            <span className="text-green-600">{day.completed_tasks} 完成</span>
                            {day.failed_tasks > 0 && (
                              <span className="text-red-600">{day.failed_tasks} 失败</span>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="flex items-center gap-1">
                            {renderStars(day.avg_rating)}
                          </div>
                          <span className="text-sm text-gray-600">
                            {day.avg_response_time}ms
                          </span>
                        </div>
                      </div>
                    ))}
                    {stats.daily_performance.length === 0 && (
                      <div className="text-center py-4 text-gray-500">
                        暂无性能数据
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* 最近评价 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MessageSquare className="w-5 h-5" />
                    最近评价
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {stats.recent_ratings.slice(0, 5).map((rating, index) => (
                      <div key={index} className="border-l-4 border-blue-200 pl-4 py-2">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{rating.user_name || '匿名用户'}</span>
                            <Badge variant="outline" className="text-xs">
                              {rating.task_type || '未分类'}
                            </Badge>
                          </div>
                          <div className="flex items-center gap-2">
                            {renderStars(rating.rating)}
                            <span className="text-sm text-gray-500">
                              {formatDate(rating.created_at)}
                            </span>
                          </div>
                        </div>
                        {rating.feedback && (
                          <p className="text-sm text-gray-600 italic">
                            "{rating.feedback}"
                          </p>
                        )}
                      </div>
                    ))}
                    {stats.recent_ratings.length === 0 && (
                      <div className="text-center py-4 text-gray-500">
                        暂无评价数据
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </>
          ) : (
            <div className="text-center py-8 text-gray-500">
              无法加载统计数据
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
