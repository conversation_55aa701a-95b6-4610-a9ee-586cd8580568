import React, { useState, useEffect } from 'react';
import { Save, Eye, EyeOff, TestTube, Settings, AlertCircle, CheckCircle } from 'lucide-react';

interface SuperAgentConfig {
  apiProvider: 'openai' | 'claude' | 'qwen' | 'deepseek';
  apiKey: string;
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
  enabled: boolean;
}

const SuperAgentConfigPage: React.FC = () => {
  const [config, setConfig] = useState<SuperAgentConfig>({
    apiProvider: 'openai',
    apiKey: '',
    model: 'gpt-4',
    temperature: 0.7,
    maxTokens: 2000,
    systemPrompt: `你是AgentGroup平台的超级智能体，负责协调和管理其他专业智能体。

你的主要职责：
1. 理解用户需求并分析任务复杂度
2. 选择合适的专业智能体来处理特定任务
3. 协调多个智能体之间的协作
4. 整合各智能体的输出，提供统一的解决方案
5. 在多用户场景下，根据用户参与度权重理解综合意图

工作原则：
- 始终以用户需求为中心
- 选择最适合的智能体处理任务
- 确保输出的准确性和实用性
- 保持友好、专业的沟通风格
- 在不确定时主动询问澄清`,
    enabled: true
  });

  const [showApiKey, setShowApiKey] = useState(false);
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);
  const [saving, setSaving] = useState(false);
  const [saveResult, setSaveResult] = useState<{ success: boolean; message: string } | null>(null);

  const modelOptions = {
    openai: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
    claude: ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku'],
    qwen: ['qwen-turbo', 'qwen-plus', 'qwen-max'],
    deepseek: ['deepseek-chat', 'deepseek-coder']
  };

  // 加载配置
  useEffect(() => {
    loadConfig();
  }, []);

  const loadConfig = async () => {
    try {
      // 从环境变量或API加载配置
      const envConfig = {
        apiProvider: (process.env.SUPER_AGENT_PROVIDER as any) || 'openai',
        apiKey: process.env.SUPER_AGENT_API_KEY || '',
        model: process.env.SUPER_AGENT_MODEL || 'gpt-4',
        temperature: parseFloat(process.env.SUPER_AGENT_TEMPERATURE || '0.7'),
        maxTokens: parseInt(process.env.SUPER_AGENT_MAX_TOKENS || '2000'),
        systemPrompt: process.env.SUPER_AGENT_SYSTEM_PROMPT || config.systemPrompt,
        enabled: process.env.SUPER_AGENT_ENABLED !== 'false'
      };
      
      setConfig(envConfig);
    } catch (error) {
      console.error('加载配置失败:', error);
    }
  };

  const handleConfigChange = (field: keyof SuperAgentConfig, value: any) => {
    setConfig(prev => ({
      ...prev,
      [field]: value
    }));
    
    // 清除之前的保存结果
    setSaveResult(null);
  };

  const testConnection = async () => {
    setTesting(true);
    setTestResult(null);

    try {
      // 模拟API测试
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      if (!config.apiKey) {
        throw new Error('API密钥不能为空');
      }

      // 这里应该调用实际的API测试接口
      setTestResult({
        success: true,
        message: '连接测试成功！超级智能体配置正常。'
      });
    } catch (error) {
      setTestResult({
        success: false,
        message: error instanceof Error ? error.message : '连接测试失败'
      });
    } finally {
      setTesting(false);
    }
  };

  const saveConfig = async () => {
    setSaving(true);
    setSaveResult(null);

    try {
      // 这里应该调用API保存配置
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSaveResult({
        success: true,
        message: '配置保存成功！'
      });
    } catch (error) {
      setSaveResult({
        success: false,
        message: error instanceof Error ? error.message : '保存配置失败'
      });
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 theme-bg-primary theme-text-primary">
      <div className="mb-8">
        <div className="flex items-center space-x-3 mb-2">
          <div className="w-10 h-10 theme-bg-accent rounded-lg flex items-center justify-center">
            <Settings className="w-6 h-6 theme-text-accent-foreground" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">超级智能体配置</h1>
            <p className="theme-text-secondary">配置超级智能体的API接入和行为参数</p>
          </div>
        </div>
      </div>

      <div className="space-y-6">
        {/* 基础配置 */}
        <div className="theme-bg-secondary rounded-lg p-6">
          <h2 className="text-lg font-semibold mb-4">基础配置</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium mb-2">API提供商</label>
              <select
                value={config.apiProvider}
                onChange={(e) => handleConfigChange('apiProvider', e.target.value)}
                className="w-full p-3 theme-bg-primary theme-border-primary border rounded-lg focus:outline-none focus:theme-border-accent"
              >
                <option value="openai">OpenAI</option>
                <option value="claude">Claude (Anthropic)</option>
                <option value="qwen">通义千问</option>
                <option value="deepseek">DeepSeek</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">模型</label>
              <select
                value={config.model}
                onChange={(e) => handleConfigChange('model', e.target.value)}
                className="w-full p-3 theme-bg-primary theme-border-primary border rounded-lg focus:outline-none focus:theme-border-accent"
              >
                {modelOptions[config.apiProvider].map(model => (
                  <option key={model} value={model}>{model}</option>
                ))}
              </select>
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium mb-2">API密钥</label>
              <div className="relative">
                <input
                  type={showApiKey ? 'text' : 'password'}
                  value={config.apiKey}
                  onChange={(e) => handleConfigChange('apiKey', e.target.value)}
                  placeholder="请输入API密钥"
                  className="w-full p-3 pr-12 theme-bg-primary theme-border-primary border rounded-lg focus:outline-none focus:theme-border-accent"
                />
                <button
                  type="button"
                  onClick={() => setShowApiKey(!showApiKey)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 theme-text-secondary hover-theme-text-primary"
                >
                  {showApiKey ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 模型参数 */}
        <div className="theme-bg-secondary rounded-lg p-6">
          <h2 className="text-lg font-semibold mb-4">模型参数</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium mb-2">
                温度 (Temperature): {config.temperature}
              </label>
              <input
                type="range"
                min="0"
                max="2"
                step="0.1"
                value={config.temperature}
                onChange={(e) => handleConfigChange('temperature', parseFloat(e.target.value))}
                className="w-full"
              />
              <div className="flex justify-between text-xs theme-text-tertiary mt-1">
                <span>保守 (0)</span>
                <span>创新 (2)</span>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">最大令牌数</label>
              <input
                type="number"
                min="100"
                max="8000"
                value={config.maxTokens}
                onChange={(e) => handleConfigChange('maxTokens', parseInt(e.target.value))}
                className="w-full p-3 theme-bg-primary theme-border-primary border rounded-lg focus:outline-none focus:theme-border-accent"
              />
            </div>
          </div>
        </div>

        {/* 系统提示词 */}
        <div className="theme-bg-secondary rounded-lg p-6">
          <h2 className="text-lg font-semibold mb-4">系统提示词</h2>
          <textarea
            value={config.systemPrompt}
            onChange={(e) => handleConfigChange('systemPrompt', e.target.value)}
            rows={12}
            className="w-full p-3 theme-bg-primary theme-border-primary border rounded-lg focus:outline-none focus:theme-border-accent resize-none"
            placeholder="定义超级智能体的角色和行为..."
          />
        </div>

        {/* 状态控制 */}
        <div className="theme-bg-secondary rounded-lg p-6">
          <h2 className="text-lg font-semibold mb-4">状态控制</h2>
          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={config.enabled}
              onChange={(e) => handleConfigChange('enabled', e.target.checked)}
              className="w-5 h-5 theme-accent"
            />
            <span>启用超级智能体</span>
          </label>
        </div>

        {/* 测试结果 */}
        {testResult && (
          <div className={`p-4 rounded-lg flex items-center space-x-3 ${
            testResult.success 
              ? 'bg-green-100 text-green-800 border border-green-300' 
              : 'bg-red-100 text-red-800 border border-red-300'
          }`}>
            {testResult.success ? (
              <CheckCircle className="w-5 h-5" />
            ) : (
              <AlertCircle className="w-5 h-5" />
            )}
            <span>{testResult.message}</span>
          </div>
        )}

        {/* 保存结果 */}
        {saveResult && (
          <div className={`p-4 rounded-lg flex items-center space-x-3 ${
            saveResult.success 
              ? 'bg-green-100 text-green-800 border border-green-300' 
              : 'bg-red-100 text-red-800 border border-red-300'
          }`}>
            {saveResult.success ? (
              <CheckCircle className="w-5 h-5" />
            ) : (
              <AlertCircle className="w-5 h-5" />
            )}
            <span>{saveResult.message}</span>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex space-x-4">
          <button
            onClick={testConnection}
            disabled={testing || !config.apiKey}
            className="flex items-center space-x-2 px-6 py-3 theme-bg-primary theme-border-primary border rounded-lg hover-theme-bg-tertiary disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <TestTube className="w-5 h-5" />
            <span>{testing ? '测试中...' : '测试连接'}</span>
          </button>

          <button
            onClick={saveConfig}
            disabled={saving}
            className="flex items-center space-x-2 px-6 py-3 theme-bg-accent theme-text-accent-foreground rounded-lg hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed transition-opacity"
          >
            <Save className="w-5 h-5" />
            <span>{saving ? '保存中...' : '保存配置'}</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default SuperAgentConfigPage;
