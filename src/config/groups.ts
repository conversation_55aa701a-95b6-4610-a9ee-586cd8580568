//这里配置群聊的信息
export interface Group {
  id: string;
  name: string;
  description: string;
  members: string[];
  isGroupDiscussionMode: boolean;
}

export const groups: Group[] = [
  {
    id: 'group1',
    name: '🔥硅碳生命体交流群',
    description: '群消息关注度权重：“user”的最新消息>其他成员最新消息>“user”的历史消息>其他成员历史消息>',
    members: ['super-agent-001', 'coding-agent-001', 'writing-agent-001', 'analysis-agent-001', 'design-agent-001'],
    isGroupDiscussionMode: false
  },
  {
    id: 'group2',
    name: '🎯编程开发群',
    description: '专业的编程开发讨论群，包含编程助手、数据分析师等技术专家，由超级智能体协调任务分配',
    isGroupDiscussionMode: false,
    members: ['super-agent-001', 'coding-agent-001', 'analysis-agent-001'],
  },
  {
    id: 'group3',
    name: '💡创意设计群',
    description: '创意设计和内容创作群，汇聚写作助手、设计顾问等创意专家，激发无限创意灵感',
    isGroupDiscussionMode: false,
    members: ['super-agent-001', 'writing-agent-001', 'design-agent-001'],
  },

];
