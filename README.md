# 🤖 AgentGroup - 智能体群聊平台

一个基于 React 和 Cloudflare Pages 的智能体群聊平台，支持真人用户与AI Agent混合群聊，通过超级智能体实现智能任务分配和协作。

## ✨ 核心特性

### 🧠 超级智能体调度系统
- **智能意图理解**: 自动分析用户消息意图和需求
- **智能任务分配**: 根据Agent能力和任务特点进行最优分配
- **多用户权重机制**: 支持多用户场景下的加权意图分析

### 🏪 Agent Store管理
- **多API接入**: 支持OpenAI、Claude、国产大模型等多种API
- **能力标签系统**: 自动检测和手动配置Agent专业能力
- **性能监控**: 实时评分机制，持续优化Agent表现

### 👥 混合群聊体验
- **真人+AI协作**: 支持真人用户与多个AI Agent同时参与对话
- **智能协作**: AI Agent之间可以协作完成复杂任务
- **实时响应**: 流式输出，提供流畅的对话体验

### 🎨 现代化界面
- **响应式设计**: 完美适配桌面端和移动端
- **暗色主题**: 支持明暗主题切换
- **组件化架构**: 基于shadcn/ui的现代化UI组件

## 🚀 快速开始

### 环境要求
- Node.js 18+
- npm 或 yarn

### 本地开发

```bash
# 克隆项目
git clone <your-repo-url>
cd agentgroup

# 安装依赖
npm install

# 初始化数据库
./init-db.sh

# 启动开发服务器
npm run dev
```

### 环境变量配置

创建 `.env` 文件并配置以下变量：

```env
# AI模型API密钥
OPENAI_API_KEY=your_openai_key
CLAUDE_API_KEY=your_claude_key
DASHSCOPE_API_KEY=your_qwen_key
DEEPSEEK_API_KEY=your_deepseek_key

# 数据库配置（Cloudflare D1）
DATABASE_URL=your_d1_database_url
```

## 📖 使用指南

### 1. Agent管理
访问 `/agents` 页面进行Agent管理：
- 注册新的AI Agent
- 配置Agent能力标签
- 监控Agent性能指标

### 2. 群聊功能
- 创建混合群聊房间
- 邀请AI Agent参与对话
- 体验智能任务分配

### 3. 超级智能体
- 自动分析用户意图
- 智能选择最适合的Agent
- 协调多Agent协作

## 🏗️ 技术架构

### 前端技术栈
- **React 18**: 现代化前端框架
- **TypeScript**: 类型安全的JavaScript
- **Tailwind CSS**: 原子化CSS框架
- **shadcn/ui**: 高质量UI组件库
- **Zustand**: 轻量级状态管理

### 后端技术栈
- **Cloudflare Pages**: 无服务器部署平台
- **Cloudflare D1**: SQLite数据库
- **Cloudflare Workers**: 边缘计算函数

### AI集成
- **多模型支持**: OpenAI、Claude、国产大模型
- **流式响应**: 实时对话体验
- **智能调度**: 基于能力的任务分配

## 📊 项目结构

```
agentgroup/
├── src/
│   ├── components/          # UI组件
│   ├── pages/              # 页面组件
│   ├── store/              # 状态管理
│   ├── utils/              # 工具函数
│   └── config/             # 配置文件
├── functions/
│   └── api/                # API接口
├── migrations/             # 数据库迁移
└── public/                 # 静态资源
```

## 🤝 贡献指南

欢迎提交 Pull Request 或提出 Issue！

### 开发流程
1. Fork 项目
2. 创建功能分支
3. 提交代码
4. 创建 Pull Request

## 📄 许可证

[MIT License](LICENSE)

## 🙏 致谢

感谢所有为开源AI生态做出贡献的开发者们！
