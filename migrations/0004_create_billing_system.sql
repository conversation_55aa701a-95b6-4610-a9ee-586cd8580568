-- AgentGroup 计费系统数据库架构
-- 创建时间: 2025-01-29
-- 实现三层计费体系：算力计费、部门Agent计费、个人Agent计费

-- 1. 用户表扩展 (添加角色和权限)
CREATE TABLE IF NOT EXISTS users (
    id TEXT PRIMARY KEY,
    username TEXT NOT NULL UNIQUE,
    password_hash TEXT NOT NULL,
    email TEXT,
    nickname TEXT,
    avatar_url TEXT,
    role TEXT DEFAULT 'user', -- 'admin', 'department_admin', 'user'
    department_id TEXT, -- 部门ID
    balance REAL DEFAULT 0.0, -- 字节币余额
    status TEXT DEFAULT 'active', -- 'active', 'inactive', 'suspended'
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL
);

-- 2. 部门表
CREATE TABLE IF NOT EXISTS departments (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    parent_id TEXT, -- 上级部门ID，支持层级结构
    budget REAL DEFAULT 0.0, -- 部门预算(字节币)
    spent REAL DEFAULT 0.0, -- 已花费金额
    admin_user_id TEXT, -- 部门管理员用户ID
    status TEXT DEFAULT 'active',
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    FOREIGN KEY (parent_id) REFERENCES departments(id),
    FOREIGN KEY (admin_user_id) REFERENCES users(id)
);

-- 3. 数字货币(字节币)交易记录表
CREATE TABLE IF NOT EXISTS currency_transactions (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    department_id TEXT, -- 部门交易时填写
    transaction_type TEXT NOT NULL, -- 'recharge', 'consume', 'transfer', 'reward'
    amount REAL NOT NULL, -- 正数为收入，负数为支出
    balance_after REAL NOT NULL, -- 交易后余额
    description TEXT,
    reference_id TEXT, -- 关联的订单或消费记录ID
    reference_type TEXT, -- 'billing_record', 'recharge_order', 'transfer'
    created_at TEXT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (department_id) REFERENCES departments(id)
);

-- 4. 算力资源定价表
CREATE TABLE IF NOT EXISTS compute_pricing (
    id TEXT PRIMARY KEY,
    resource_type TEXT NOT NULL, -- 'gpu_token', 'cpu_time', 'sandbox_monthly'
    model_name TEXT, -- 模型名称(GPU资源时使用)
    unit TEXT NOT NULL, -- 'token', 'minute', 'month'
    price_per_unit REAL NOT NULL, -- 每单位价格(字节币)
    description TEXT,
    status TEXT DEFAULT 'active',
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL
);

-- 5. Agent定价表
CREATE TABLE IF NOT EXISTS agent_pricing (
    id TEXT PRIMARY KEY,
    agent_id TEXT NOT NULL,
    pricing_type TEXT NOT NULL, -- 'per_call', 'per_collaboration', 'monthly'
    price REAL NOT NULL, -- 价格(字节币)
    creator_share REAL DEFAULT 0.7, -- 创作者分成比例(0-1)
    platform_share REAL DEFAULT 0.3, -- 平台分成比例(0-1)
    status TEXT DEFAULT 'active',
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE
);

-- 6. 计费记录表
CREATE TABLE IF NOT EXISTS billing_records (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    department_id TEXT, -- 部门消费时填写
    billing_type TEXT NOT NULL, -- 'compute', 'agent_call', 'agent_collaboration'
    resource_id TEXT, -- 资源ID(agent_id, compute_resource_id等)
    resource_type TEXT NOT NULL, -- 'gpu_token', 'cpu_time', 'agent', 'sandbox'
    quantity REAL NOT NULL, -- 消费数量
    unit_price REAL NOT NULL, -- 单价
    total_amount REAL NOT NULL, -- 总金额
    creator_id TEXT, -- Agent创作者ID(Agent消费时使用)
    creator_amount REAL DEFAULT 0, -- 创作者收入
    platform_amount REAL DEFAULT 0, -- 平台收入
    conversation_id TEXT, -- 关联的对话ID
    message_id TEXT, -- 关联的消息ID
    metadata TEXT, -- JSON格式的额外信息
    status TEXT DEFAULT 'completed', -- 'pending', 'completed', 'failed', 'refunded'
    created_at TEXT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (department_id) REFERENCES departments(id),
    FOREIGN KEY (creator_id) REFERENCES users(id),
    FOREIGN KEY (conversation_id) REFERENCES conversations(id),
    FOREIGN KEY (message_id) REFERENCES messages(id)
);

-- 7. 充值订单表
CREATE TABLE IF NOT EXISTS recharge_orders (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    amount REAL NOT NULL, -- 充值金额(字节币)
    payment_method TEXT, -- 'internal', 'alipay', 'wechat', 'bank'
    payment_status TEXT DEFAULT 'pending', -- 'pending', 'paid', 'failed', 'cancelled'
    payment_reference TEXT, -- 第三方支付单号
    admin_approved_by TEXT, -- 管理员审批人ID
    approved_at TEXT, -- 审批时间
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (admin_approved_by) REFERENCES users(id)
);

-- 8. Agent权限表(扩展现有agents表的权限控制)
CREATE TABLE IF NOT EXISTS agent_permissions (
    id TEXT PRIMARY KEY,
    agent_id TEXT NOT NULL,
    owner_type TEXT NOT NULL, -- 'admin', 'department', 'user'
    owner_id TEXT NOT NULL, -- 所有者ID(user_id或department_id)
    permission_level TEXT DEFAULT 'read', -- 'read', 'write', 'admin'
    can_edit BOOLEAN DEFAULT FALSE,
    can_delete BOOLEAN DEFAULT FALSE,
    can_publish BOOLEAN DEFAULT FALSE,
    created_at TEXT NOT NULL,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_department ON users(department_id);
CREATE INDEX IF NOT EXISTS idx_currency_transactions_user ON currency_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_currency_transactions_type ON currency_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_billing_records_user ON billing_records(user_id);
CREATE INDEX IF NOT EXISTS idx_billing_records_type ON billing_records(billing_type);
CREATE INDEX IF NOT EXISTS idx_billing_records_created ON billing_records(created_at);
CREATE INDEX IF NOT EXISTS idx_agent_permissions_agent ON agent_permissions(agent_id);
CREATE INDEX IF NOT EXISTS idx_agent_permissions_owner ON agent_permissions(owner_type, owner_id);

-- 插入默认数据
-- 创建默认管理员用户
INSERT OR IGNORE INTO users (id, username, password_hash, email, nickname, role, balance, created_at, updated_at) VALUES
('admin-001', 'admin', '$2b$10$rQZ8kHp.TB.It.NvHq/T4.5J5J5J5J5J5J5J5J5J5J5J5J5J5J5J5', '<EMAIL>', '系统管理员', 'admin', 10000.0, datetime('now'), datetime('now'));

-- 创建默认部门
INSERT OR IGNORE INTO departments (id, name, description, budget, admin_user_id, created_at, updated_at) VALUES
('dept-001', '信息中心', '负责算力资源和基础设施', 50000.0, 'admin-001', datetime('now'), datetime('now')),
('dept-002', '产品部', '产品设计和用户体验', 20000.0, 'admin-001', datetime('now'), datetime('now')),
('dept-003', '技术部', '软件开发和技术支持', 30000.0, 'admin-001', datetime('now'), datetime('now'));

-- 插入算力资源定价
INSERT OR IGNORE INTO compute_pricing (id, resource_type, model_name, unit, price_per_unit, description, created_at, updated_at) VALUES
('price-001', 'gpu_token', 'gpt-4', 'token', 0.01, 'GPT-4模型Token计费', datetime('now'), datetime('now')),
('price-002', 'gpu_token', 'gpt-3.5-turbo', 'token', 0.005, 'GPT-3.5模型Token计费', datetime('now'), datetime('now')),
('price-003', 'gpu_token', 'claude-3-sonnet', 'token', 0.008, 'Claude-3模型Token计费', datetime('now'), datetime('now')),
('price-004', 'cpu_time', 'sandbox', 'minute', 0.1, 'CPU沙盒环境按分钟计费', datetime('now'), datetime('now')),
('price-005', 'sandbox_monthly', 'premium', 'month', 100.0, '高级沙盒环境包月', datetime('now'), datetime('now'));

-- 为默认Agent设置权限
INSERT OR IGNORE INTO agent_permissions (id, agent_id, owner_type, owner_id, permission_level, can_edit, can_delete, can_publish, created_at) VALUES
('perm-001', 'super-agent-001', 'admin', 'admin-001', 'admin', TRUE, TRUE, TRUE, datetime('now')),
('perm-002', 'coding-agent-001', 'department', 'dept-003', 'admin', TRUE, TRUE, TRUE, datetime('now')),
('perm-003', 'writing-agent-001', 'department', 'dept-002', 'admin', TRUE, TRUE, TRUE, datetime('now'));

-- 为默认Agent设置定价
INSERT OR IGNORE INTO agent_pricing (id, agent_id, pricing_type, price, creator_share, platform_share, created_at, updated_at) VALUES
('ap-001', 'coding-agent-001', 'per_call', 1.0, 0.7, 0.3, datetime('now'), datetime('now')),
('ap-002', 'writing-agent-001', 'per_call', 0.8, 0.7, 0.3, datetime('now'), datetime('now'));
