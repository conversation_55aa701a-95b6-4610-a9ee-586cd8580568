-- Migration number: 0003 	 2025-06-29T00:00:00.000Z
-- 创建Agent管理系统相关表

-- 1. agents表 - 存储Agent基本信息
CREATE TABLE IF NOT EXISTS agents (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name VARCHAR(100) NOT NULL UNIQUE, -- Agent唯一名称
  display_name VARCHAR(100) NOT NULL, -- 显示名称
  description TEXT, -- Agent描述
  api_url TEXT NOT NULL, -- API接入地址
  api_key TEXT, -- API密钥（加密存储）
  api_type VARCHAR(50) DEFAULT 'dify', -- API类型：dify, fastgpt, custom等
  avatar_url TEXT, -- 头像URL
  status INTEGER DEFAULT 1, -- 状态：0-禁用，1-启用，2-维护中
  is_super_agent BOOLEAN DEFAULT FALSE, -- 是否为超级智能体
  created_by INTEGER, -- 创建者用户ID
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_used_at TIMESTAMP, -- 最后使用时间
  FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 2. agent_capabilities表 - Agent能力标签
CREATE TABLE IF NOT EXISTS agent_capabilities (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  agent_id INTEGER NOT NULL,
  capability_name VARCHAR(100) NOT NULL, -- 能力名称
  capability_type VARCHAR(50) DEFAULT 'manual', -- 标签类型：manual-手动，auto-自动
  confidence_score REAL DEFAULT 1.0, -- 置信度分数(0-1)
  description TEXT, -- 能力描述
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
  UNIQUE(agent_id, capability_name)
);

-- 3. agent_ratings表 - Agent评分记录
CREATE TABLE IF NOT EXISTS agent_ratings (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  agent_id INTEGER NOT NULL,
  user_id INTEGER NOT NULL,
  task_type VARCHAR(100), -- 任务类型
  rating INTEGER CHECK(rating >= 1 AND rating <= 5), -- 评分1-5
  feedback TEXT, -- 用户反馈
  task_completed BOOLEAN DEFAULT FALSE, -- 任务是否完成
  response_time INTEGER, -- 响应时间(毫秒)
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 4. groups表扩展 - 支持混合群聊
CREATE TABLE IF NOT EXISTS groups_new (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name VARCHAR(200) NOT NULL,
  description TEXT,
  group_type VARCHAR(50) DEFAULT 'mixed', -- 群组类型：ai_only, mixed, human_only
  max_members INTEGER DEFAULT 50,
  created_by INTEGER,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 5. group_members表 - 群组成员关系
CREATE TABLE IF NOT EXISTS group_members (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  group_id INTEGER NOT NULL,
  member_id INTEGER NOT NULL,
  member_type VARCHAR(20) NOT NULL CHECK(member_type IN ('user', 'agent')), -- 成员类型
  role VARCHAR(50) DEFAULT 'member', -- 角色：admin, member, observer
  is_muted BOOLEAN DEFAULT FALSE, -- 是否被禁言
  joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (group_id) REFERENCES groups_new(id) ON DELETE CASCADE,
  UNIQUE(group_id, member_id, member_type)
);

-- 6. messages表 - 消息记录
CREATE TABLE IF NOT EXISTS messages (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  group_id INTEGER NOT NULL,
  sender_id INTEGER NOT NULL,
  sender_type VARCHAR(20) NOT NULL CHECK(sender_type IN ('user', 'agent')), -- 发送者类型
  content TEXT NOT NULL,
  message_type VARCHAR(50) DEFAULT 'text', -- 消息类型：text, file, image等
  reply_to INTEGER, -- 回复的消息ID
  metadata TEXT, -- JSON格式的元数据
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (group_id) REFERENCES groups_new(id) ON DELETE CASCADE,
  FOREIGN KEY (reply_to) REFERENCES messages(id)
);

-- 7. task_assignments表 - 任务分配记录
CREATE TABLE IF NOT EXISTS task_assignments (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  group_id INTEGER NOT NULL,
  message_id INTEGER NOT NULL, -- 触发任务的消息ID
  assigned_agent_id INTEGER NOT NULL,
  task_type VARCHAR(100),
  task_description TEXT,
  status VARCHAR(50) DEFAULT 'pending', -- pending, in_progress, completed, failed
  priority INTEGER DEFAULT 5, -- 优先级1-10
  assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  completed_at TIMESTAMP,
  result TEXT, -- 任务结果
  FOREIGN KEY (group_id) REFERENCES groups_new(id) ON DELETE CASCADE,
  FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE,
  FOREIGN KEY (assigned_agent_id) REFERENCES agents(id) ON DELETE CASCADE
);

-- 8. agent_performance表 - Agent性能统计
CREATE TABLE IF NOT EXISTS agent_performance (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  agent_id INTEGER NOT NULL,
  date DATE NOT NULL,
  total_tasks INTEGER DEFAULT 0,
  completed_tasks INTEGER DEFAULT 0,
  failed_tasks INTEGER DEFAULT 0,
  avg_response_time REAL DEFAULT 0,
  avg_rating REAL DEFAULT 0,
  total_ratings INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
  UNIQUE(agent_id, date)
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_agents_name ON agents(name);
CREATE INDEX IF NOT EXISTS idx_agents_status ON agents(status);
CREATE INDEX IF NOT EXISTS idx_agent_capabilities_agent_id ON agent_capabilities(agent_id);
CREATE INDEX IF NOT EXISTS idx_agent_capabilities_name ON agent_capabilities(capability_name);
CREATE INDEX IF NOT EXISTS idx_agent_ratings_agent_id ON agent_ratings(agent_id);
CREATE INDEX IF NOT EXISTS idx_agent_ratings_user_id ON agent_ratings(user_id);
CREATE INDEX IF NOT EXISTS idx_group_members_group_id ON group_members(group_id);
CREATE INDEX IF NOT EXISTS idx_group_members_member ON group_members(member_id, member_type);
CREATE INDEX IF NOT EXISTS idx_messages_group_id ON messages(group_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);
CREATE INDEX IF NOT EXISTS idx_task_assignments_group_id ON task_assignments(group_id);
CREATE INDEX IF NOT EXISTS idx_task_assignments_agent_id ON task_assignments(assigned_agent_id);
CREATE INDEX IF NOT EXISTS idx_task_assignments_status ON task_assignments(status);
CREATE INDEX IF NOT EXISTS idx_agent_performance_agent_date ON agent_performance(agent_id, date);
