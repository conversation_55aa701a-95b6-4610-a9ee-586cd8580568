-- AgentGroup 智能体群聊平台数据库架构
-- 创建时间: 2025-01-29

-- 1. Agent管理表
CREATE TABLE IF NOT EXISTS agents (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    api_type TEXT NOT NULL, -- 'openai', 'claude', 'qwen', 'deepseek', etc.
    api_key TEXT NOT NULL,
    model_name TEXT NOT NULL,
    performance_score REAL DEFAULT 0,
    status TEXT DEFAULT 'active', -- 'active', 'inactive', 'maintenance'
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL
);

-- 2. Agent能力标签表
CREATE TABLE IF NOT EXISTS agent_capabilities (
    id TEXT PRIMARY KEY,
    agent_id TEXT NOT NULL,
    capability_name TEXT NOT NULL, -- '编程', '写作', '分析', '翻译', etc.
    confidence_score REAL DEFAULT 0.8, -- 0-1之间的置信度
    is_primary BOOLEAN DEFAULT FALSE, -- 是否为主要能力
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE
);

-- 3. 对话会话表
CREATE TABLE IF NOT EXISTS conversations (
    id TEXT PRIMARY KEY,
    title TEXT,
    type TEXT DEFAULT 'mixed', -- 'mixed', 'agent_only', 'user_only'
    participants TEXT, -- JSON数组，包含用户ID和Agent ID
    super_agent_id TEXT, -- 负责调度的超级智能体ID
    status TEXT DEFAULT 'active', -- 'active', 'archived', 'deleted'
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    FOREIGN KEY (super_agent_id) REFERENCES agents(id)
);

-- 4. 消息表
CREATE TABLE IF NOT EXISTS messages (
    id TEXT PRIMARY KEY,
    conversation_id TEXT NOT NULL,
    sender_id TEXT NOT NULL, -- 用户ID或Agent ID
    sender_type TEXT NOT NULL, -- 'user', 'agent', 'system'
    content TEXT NOT NULL,
    message_type TEXT DEFAULT 'text', -- 'text', 'image', 'file', 'system'
    metadata TEXT, -- JSON格式的元数据
    parent_message_id TEXT, -- 回复的消息ID
    created_at TEXT NOT NULL,
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE
);

-- 5. Agent性能统计表
CREATE TABLE IF NOT EXISTS agent_performance (
    id TEXT PRIMARY KEY,
    agent_id TEXT NOT NULL,
    conversation_id TEXT NOT NULL,
    task_type TEXT, -- 任务类型
    response_time_ms INTEGER, -- 响应时间(毫秒)
    user_rating INTEGER, -- 用户评分 1-5
    task_completion BOOLEAN DEFAULT FALSE, -- 任务是否完成
    error_count INTEGER DEFAULT 0, -- 错误次数
    created_at TEXT NOT NULL,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE
);

-- 6. 任务分配表
CREATE TABLE IF NOT EXISTS task_assignments (
    id TEXT PRIMARY KEY,
    conversation_id TEXT NOT NULL,
    message_id TEXT NOT NULL,
    assigned_agent_id TEXT NOT NULL,
    task_description TEXT,
    intent_analysis TEXT, -- JSON格式的意图分析结果
    assignment_reason TEXT, -- 分配原因
    status TEXT DEFAULT 'pending', -- 'pending', 'in_progress', 'completed', 'failed'
    assigned_at TEXT NOT NULL,
    completed_at TEXT,
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_agent_id) REFERENCES agents(id) ON DELETE CASCADE
);

-- 7. 用户会话表
CREATE TABLE IF NOT EXISTS user_sessions (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    session_token TEXT NOT NULL UNIQUE,
    user_name TEXT,
    user_avatar TEXT,
    preferences TEXT, -- JSON格式的用户偏好
    last_active_at TEXT NOT NULL,
    created_at TEXT NOT NULL,
    expires_at TEXT NOT NULL
);

-- 8. 用户表
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    nickname TEXT,
    role TEXT DEFAULT 'user', -- 'admin', 'department_admin', 'user'
    department_id TEXT,
    balance REAL DEFAULT 100.0, -- 字节币余额
    status INTEGER DEFAULT 1, -- 1: 启用, 0: 禁用
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 9. 计费交易记录表
CREATE TABLE IF NOT EXISTS billing_transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    type TEXT NOT NULL, -- 'income', 'expense'
    amount REAL NOT NULL,
    description TEXT NOT NULL,
    agent_id TEXT, -- 如果是Agent相关的交易
    conversation_id TEXT, -- 如果是对话相关的交易
    billing_tier TEXT, -- 'infrastructure', 'department', 'personal'
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE SET NULL,
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE SET NULL
);

-- 10. Agent计费配置表
CREATE TABLE IF NOT EXISTS agent_billing_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    agent_id TEXT NOT NULL,
    billing_type TEXT NOT NULL, -- 'token', 'time', 'call'
    price_per_unit REAL NOT NULL, -- 每单位价格（字节币）
    tier TEXT NOT NULL, -- 'infrastructure', 'department', 'personal'
    creator_user_id INTEGER, -- Agent创建者（用于个人和部门Agent）
    revenue_share REAL DEFAULT 0.7, -- 创建者收益分成比例
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    FOREIGN KEY (creator_user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- 11. 系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    description TEXT,
    updated_at TEXT NOT NULL
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_agents_status ON agents(status);
CREATE INDEX IF NOT EXISTS idx_agent_capabilities_agent_id ON agent_capabilities(agent_id);
CREATE INDEX IF NOT EXISTS idx_conversations_status ON conversations(status);
CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);
CREATE INDEX IF NOT EXISTS idx_agent_performance_agent_id ON agent_performance(agent_id);
CREATE INDEX IF NOT EXISTS idx_task_assignments_conversation_id ON task_assignments(conversation_id);
CREATE INDEX IF NOT EXISTS idx_task_assignments_status ON task_assignments(status);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_billing_transactions_user_id ON billing_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_billing_transactions_created_at ON billing_transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_agent_billing_config_agent_id ON agent_billing_config(agent_id);

-- 插入默认用户数据
INSERT OR IGNORE INTO users (id, username, password_hash, email, nickname, role, balance, status) VALUES
(1, 'admin', 'admin123', '<EMAIL>', '系统管理员', 'admin', 10000.0, 1);

-- 插入默认系统配置
INSERT OR IGNORE INTO system_config (key, value, description, updated_at) VALUES
('platform_name', 'AgentGroup', '平台名称', datetime('now')),
('max_agents_per_conversation', '10', '每个对话最大Agent数量', datetime('now')),
('default_super_agent_model', 'gpt-4', '默认超级智能体模型', datetime('now')),
('intent_analysis_threshold', '0.7', '意图分析置信度阈值', datetime('now')),
('agent_response_timeout', '30000', 'Agent响应超时时间(毫秒)', datetime('now')),
('digital_currency_name', '字节币', '数字货币名称', datetime('now')),
('infrastructure_billing_rate', '0.01', '基础设施计费费率（每token）', datetime('now')),
('department_agent_call_rate', '1.0', '部门Agent调用费率（每次）', datetime('now')),
('personal_agent_call_rate', '0.5', '个人Agent调用费率（每次）', datetime('now'));

-- 插入示例Agent数据
INSERT OR IGNORE INTO agents (id, name, description, api_type, api_key, model_name, performance_score, status, created_at, updated_at) VALUES
('super-agent-001', '超级智能体', '负责任务分配和协调的超级智能体', 'openai', 'demo-key', 'gpt-4', 0.95, 'active', datetime('now'), datetime('now')),
('coding-agent-001', '编程助手', '专业的编程和技术问题解答Agent', 'openai', 'demo-key', 'gpt-4', 0.88, 'active', datetime('now'), datetime('now')),
('writing-agent-001', '写作助手', '专业的文案写作和内容创作Agent', 'claude', 'demo-key', 'claude-3-sonnet', 0.92, 'active', datetime('now'), datetime('now'));

-- 插入示例能力数据
INSERT OR IGNORE INTO agent_capabilities (id, agent_id, capability_name, confidence_score, is_primary) VALUES
('cap-001', 'super-agent-001', '任务分配', 0.95, TRUE),
('cap-002', 'super-agent-001', '意图理解', 0.90, TRUE),
('cap-003', 'coding-agent-001', '编程', 0.95, TRUE),
('cap-004', 'coding-agent-001', '代码审查', 0.88, FALSE),
('cap-005', 'coding-agent-001', '技术文档', 0.85, FALSE),
('cap-006', 'writing-agent-001', '文案写作', 0.95, TRUE),
('cap-007', 'writing-agent-001', '内容策划', 0.90, FALSE),
('cap-008', 'writing-agent-001', '翻译', 0.85, FALSE);

-- 插入Agent计费配置数据
INSERT OR IGNORE INTO agent_billing_config (agent_id, billing_type, price_per_unit, tier, creator_user_id, revenue_share) VALUES
('super-agent-001', 'token', 0.01, 'infrastructure', 1, 0.0),
('coding-agent-001', 'call', 1.0, 'department', 1, 0.7),
('writing-agent-001', 'call', 0.8, 'department', 1, 0.7);
