{"name": "agentgroup-server", "version": "1.0.0", "description": "AgentGroup Backend Server", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "start:pm2": "pm2 start ecosystem.config.js", "stop:pm2": "pm2 stop agentgroup-server", "restart:pm2": "pm2 restart agentgroup-server", "logs": "pm2 logs agentgroup-server", "test": "jest", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "joi": "^17.11.0", "pg": "^8.11.3", "redis": "^4.6.10", "winston": "^3.11.0", "rate-limiter-flexible": "^2.4.2", "multer": "^1.4.5-lts.1", "uuid": "^9.0.1", "node-cron": "^3.0.3"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/pg": "^8.10.9", "@types/multer": "^1.4.11", "@types/uuid": "^9.0.7", "@types/node": "^20.10.4", "@types/node-cron": "^3.0.11", "typescript": "^5.3.3", "tsx": "^4.6.2", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "prettier": "^3.1.1", "jest": "^29.7.0", "@types/jest": "^29.5.8", "ts-jest": "^29.1.1", "pm2": "^5.3.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["agentgroup", "ai", "chatbot", "nodejs", "express", "typescript"], "author": "AgentGroup Team", "license": "MIT"}