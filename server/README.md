# AgentGroup Server

高性能的 AgentGroup 后端服务器，基于 Node.js + Express + TypeScript 构建。

## 技术栈

- **运行时**: Node.js 18+
- **框架**: Express.js
- **语言**: TypeScript
- **数据库**: PostgreSQL
- **缓存**: Redis
- **进程管理**: PM2
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx
- **监控**: Prometheus + Grafana

## 功能特性

- ✅ 高性能 RESTful API
- ✅ JWT 身份认证
- ✅ 角色权限管理
- ✅ 请求速率限制
- ✅ 数据验证
- ✅ 错误处理
- ✅ 日志记录
- ✅ 健康检查
- ✅ 容器化部署
- ✅ 负载均衡
- ✅ 监控告警

## 快速开始

### 开发环境

1. **安装依赖**
```bash
npm install
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库和其他服务
```

3. **启动开发服务器**
```bash
npm run dev
```

### 生产环境

#### 使用 Docker Compose（推荐）

1. **启动所有服务**
```bash
docker-compose up -d
```

2. **查看服务状态**
```bash
docker-compose ps
```

3. **查看日志**
```bash
docker-compose logs -f agentgroup-server
```

#### 使用 PM2

1. **构建项目**
```bash
npm run build
```

2. **启动服务**
```bash
npm run start:pm2
```

3. **查看状态**
```bash
npm run logs
```

## API 文档

### 认证接口

- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `POST /api/auth/refresh` - 刷新令牌
- `GET /api/auth/me` - 获取当前用户信息

### 用户管理

- `GET /api/users` - 获取用户列表（管理员）
- `GET /api/users/:id` - 获取用户详情
- `PUT /api/users/:id` - 更新用户信息
- `PATCH /api/users/:id/role` - 更新用户角色（管理员）
- `DELETE /api/users/:id` - 删除用户（管理员）

### 智能体管理

- `GET /api/agents` - 获取智能体列表
- `POST /api/agents` - 创建智能体
- `GET /api/agents/:id` - 获取智能体详情
- `PUT /api/agents/:id` - 更新智能体
- `DELETE /api/agents/:id` - 删除智能体

### 群组管理

- `GET /api/groups` - 获取群组列表
- `POST /api/groups` - 创建群组
- `GET /api/groups/:id` - 获取群组详情
- `PUT /api/groups/:id` - 更新群组
- `DELETE /api/groups/:id` - 删除群组

### 计费系统

- `GET /api/billing/balance` - 获取账户余额
- `POST /api/billing/recharge` - 账户充值
- `POST /api/billing/recharge-by-key` - 密钥充值
- `GET /api/billing/transactions` - 获取交易记录

### 管理员接口

- `GET /api/admin/users` - 管理用户
- `POST /api/admin/users` - 创建用户
- `GET /api/admin/recharge-keys` - 获取充值密钥
- `POST /api/admin/recharge-keys/generate` - 生成充值密钥
- `GET /api/admin/system/stats` - 系统统计

## 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `NODE_ENV` | 运行环境 | `development` |
| `PORT` | 服务端口 | `8000` |
| `DB_HOST` | 数据库主机 | `localhost` |
| `DB_PORT` | 数据库端口 | `5432` |
| `DB_NAME` | 数据库名称 | `agentgroup` |
| `REDIS_HOST` | Redis 主机 | `localhost` |
| `REDIS_PORT` | Redis 端口 | `6379` |
| `JWT_SECRET` | JWT 密钥 | - |

### 数据库配置

支持 PostgreSQL 数据库，需要创建相应的数据库和用户：

```sql
CREATE DATABASE agentgroup;
CREATE USER agentgroup WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE agentgroup TO agentgroup;
```

### Redis 配置

用于缓存和会话存储，建议在生产环境中启用密码认证。

## 性能优化

### 集群模式

使用 PM2 集群模式，自动利用所有 CPU 核心：

```bash
pm2 start ecosystem.config.js
```

### 负载均衡

使用 Nginx 进行负载均衡和反向代理：

```nginx
upstream agentgroup_backend {
    least_conn;
    server 127.0.0.1:8000;
    server 127.0.0.1:8001;
    server 127.0.0.1:8002;
    server 127.0.0.1:8003;
}
```

### 缓存策略

- Redis 缓存热点数据
- Nginx 缓存静态资源
- 数据库查询优化

## 监控和日志

### 健康检查

访问 `/health` 端点获取服务状态：

```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 3600,
  "environment": "production",
  "version": "1.0.0"
}
```

### 日志管理

- 应用日志：`logs/app.log`
- 错误日志：`logs/error.log`
- 访问日志：`logs/access.log`

### Prometheus 监控

访问 `http://localhost:9090` 查看 Prometheus 监控面板。

### Grafana 仪表板

访问 `http://localhost:3001` 查看 Grafana 仪表板（admin/admin123）。

## 安全配置

### HTTPS

在生产环境中启用 HTTPS：

1. 获取 SSL 证书
2. 配置 Nginx SSL
3. 强制 HTTPS 重定向

### 安全头

自动添加安全响应头：

- `X-Frame-Options: DENY`
- `X-Content-Type-Options: nosniff`
- `X-XSS-Protection: 1; mode=block`
- `Strict-Transport-Security`

### 速率限制

- API 接口：10 请求/秒
- 认证接口：5 请求/分钟
- 管理接口：更严格的限制

## 部署指南

### Docker 部署

1. **构建镜像**
```bash
docker build -t agentgroup-server .
```

2. **运行容器**
```bash
docker run -d \
  --name agentgroup-server \
  -p 8000:8000 \
  -e NODE_ENV=production \
  agentgroup-server
```

### 传统部署

1. **安装 Node.js 18+**
2. **安装 PostgreSQL 和 Redis**
3. **克隆代码并安装依赖**
4. **配置环境变量**
5. **构建和启动服务**

## 故障排除

### 常见问题

1. **端口被占用**
   - 检查端口使用情况：`lsof -i :8000`
   - 修改配置文件中的端口

2. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接参数
   - 检查防火墙设置

3. **Redis 连接失败**
   - 检查 Redis 服务状态
   - 验证连接参数
   - 检查密码配置

### 日志分析

查看详细错误信息：

```bash
# PM2 日志
pm2 logs agentgroup-server

# Docker 日志
docker logs agentgroup-server

# 系统日志
tail -f logs/error.log
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
