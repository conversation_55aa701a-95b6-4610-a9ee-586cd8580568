import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { config } from '@/config/index';
import { AppError } from '@/middleware/errorHandler';
import { logger } from '@/utils/logger';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        username: string;
        role: string;
        email?: string;
      };
    }
  }
}

interface JWTPayload {
  userId: string;
  username: string;
  role: string;
  email?: string;
  iat: number;
  exp: number;
}

// Verify JWT token
const verifyToken = (token: string): Promise<JWTPayload> => {
  return new Promise((resolve, reject) => {
    jwt.verify(token, config.jwt.secret, (err, decoded) => {
      if (err) {
        reject(err);
      } else {
        resolve(decoded as JWTPayload);
      }
    });
  });
};

// Authentication middleware
const authMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new AppError('Access token is required', 401);
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    if (!token) {
      throw new AppError('Access token is required', 401);
    }

    // Verify token
    const decoded = await verifyToken(token);

    // Check if token is expired
    if (decoded.exp < Math.floor(Date.now() / 1000)) {
      throw new AppError('Token has expired', 401);
    }

    // Add user info to request
    req.user = {
      id: decoded.userId,
      username: decoded.username,
      role: decoded.role,
      email: decoded.email,
    };

    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      next(new AppError('Invalid token', 401));
    } else if (error instanceof jwt.TokenExpiredError) {
      next(new AppError('Token has expired', 401));
    } else {
      next(error);
    }
  }
};

// Admin role middleware
const adminMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  if (!req.user) {
    next(new AppError('Authentication required', 401));
    return;
  }

  if (req.user.role !== 'admin') {
    next(new AppError('Admin access required', 403));
    return;
  }

  next();
};

// Department admin or admin role middleware
const departmentAdminMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  if (!req.user) {
    next(new AppError('Authentication required', 401));
    return;
  }

  if (!['admin', 'department_admin'].includes(req.user.role)) {
    next(new AppError('Department admin or admin access required', 403));
    return;
  }

  next();
};

// Optional auth middleware (doesn't throw error if no token)
const optionalAuthMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      
      if (token) {
        const decoded = await verifyToken(token);
        
        // Only add user if token is valid and not expired
        if (decoded.exp >= Math.floor(Date.now() / 1000)) {
          req.user = {
            id: decoded.userId,
            username: decoded.username,
            role: decoded.role,
            email: decoded.email,
          };
        }
      }
    }
    
    next();
  } catch (error) {
    // Log error but don't block request
    logger.warn('Optional auth middleware error:', error);
    next();
  }
};

// Generate JWT token
const generateToken = (payload: {
  userId: string;
  username: string;
  role: string;
  email?: string;
}): string => {
  return jwt.sign(payload, config.jwt.secret, {
    expiresIn: config.jwt.expiresIn,
  });
};

// Generate refresh token
const generateRefreshToken = (payload: {
  userId: string;
  username: string;
}): string => {
  return jwt.sign(payload, config.jwt.secret, {
    expiresIn: config.jwt.refreshExpiresIn,
  });
};

export {
  authMiddleware,
  adminMiddleware,
  departmentAdminMiddleware,
  optionalAuthMiddleware,
  generateToken,
  generateRefreshToken,
  verifyToken,
};
