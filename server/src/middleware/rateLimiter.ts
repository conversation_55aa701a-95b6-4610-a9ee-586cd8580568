import { Request, Response, NextFunction } from 'express';
import { RateLimiterMemory, RateLimiterRedis } from 'rate-limiter-flexible';
import { createClient } from 'redis';
import { config } from '@/config/index';
import { logger } from '@/utils/logger';

// Create Redis client for rate limiting (optional)
let redisClient: any = null;

// Only try to connect to Redis if host is configured and not commented out
if (config.redis.host && process.env.REDIS_HOST) {
  try {
    redisClient = createClient({
      socket: {
        host: config.redis.host,
        port: config.redis.port,
      },
      password: config.redis.password,
      database: config.redis.db,
    });

    redisClient.on('error', (err: Error) => {
      logger.error('Redis Client Error for Rate Limiter:', err);
      redisClient = null; // Fallback to memory-based rate limiting
    });

    redisClient.connect().catch((err: Error) => {
      logger.error('Failed to connect to Redis for Rate Limiter:', err);
      redisClient = null;
    });
  } catch (error) {
    logger.error('Error setting up Redis for Rate Limiter:', error);
    redisClient = null;
  }
}

// Rate limiter configuration
const rateLimiterOptions = {
  keyPrefix: 'agentgroup_rl',
  points: config.security.rateLimitMaxRequests, // Number of requests
  duration: Math.floor(config.security.rateLimitWindowMs / 1000), // Per duration in seconds
  blockDuration: 60, // Block for 60 seconds if limit exceeded
};

// Create rate limiter instance
const rateLimiter = redisClient
  ? new RateLimiterRedis({
      storeClient: redisClient,
      ...rateLimiterOptions,
    })
  : new RateLimiterMemory(rateLimiterOptions);

// Different rate limits for different endpoints
const strictRateLimiter = redisClient
  ? new RateLimiterRedis({
      storeClient: redisClient,
      keyPrefix: 'agentgroup_strict_rl',
      points: 5, // 5 requests
      duration: 60, // per 60 seconds
      blockDuration: 300, // block for 5 minutes
    })
  : new RateLimiterMemory({
      keyPrefix: 'agentgroup_strict_rl',
      points: 5,
      duration: 60,
      blockDuration: 300,
    });

// Auth rate limiter (for login attempts)
const authRateLimiter = redisClient
  ? new RateLimiterRedis({
      storeClient: redisClient,
      keyPrefix: 'agentgroup_auth_rl',
      points: 5, // 5 login attempts
      duration: 900, // per 15 minutes
      blockDuration: 900, // block for 15 minutes
    })
  : new RateLimiterMemory({
      keyPrefix: 'agentgroup_auth_rl',
      points: 5,
      duration: 900,
      blockDuration: 900,
    });

// Get client IP address
const getClientIP = (req: Request): string => {
  return (
    (req.headers['x-forwarded-for'] as string)?.split(',')[0] ||
    req.connection.remoteAddress ||
    req.socket.remoteAddress ||
    req.ip ||
    'unknown'
  );
};

// General rate limiter middleware
const rateLimiterMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const key = getClientIP(req);
    await rateLimiter.consume(key);
    next();
  } catch (rejRes: any) {
    const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
    res.set('Retry-After', String(secs));
    res.status(429).json({
      success: false,
      message: 'Too many requests, please try again later.',
      retryAfter: secs,
    });
  }
};

// Strict rate limiter for sensitive endpoints
const strictRateLimiterMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const key = getClientIP(req);
    await strictRateLimiter.consume(key);
    next();
  } catch (rejRes: any) {
    const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
    res.set('Retry-After', String(secs));
    res.status(429).json({
      success: false,
      message: 'Too many requests to sensitive endpoint, please try again later.',
      retryAfter: secs,
    });
  }
};

// Auth rate limiter for login attempts
const authRateLimiterMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const key = getClientIP(req);
    await authRateLimiter.consume(key);
    next();
  } catch (rejRes: any) {
    const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
    res.set('Retry-After', String(secs));
    res.status(429).json({
      success: false,
      message: 'Too many login attempts, please try again later.',
      retryAfter: secs,
    });
  }
};

export {
  rateLimiterMiddleware as rateLimiter,
  strictRateLimiterMiddleware as strictRateLimiter,
  authRateLimiterMiddleware as authRateLimiter,
};
