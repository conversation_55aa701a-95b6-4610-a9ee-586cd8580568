import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';
import { AppError } from '@/middleware/errorHandler';

// Validation middleware factory
const validateRequest = (schema: {
  body?: Joi.ObjectSchema;
  query?: Joi.ObjectSchema;
  params?: Joi.ObjectSchema;
}) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const errors: string[] = [];

    // Validate request body
    if (schema.body) {
      const { error } = schema.body.validate(req.body);
      if (error) {
        errors.push(`Body: ${error.details.map(d => d.message).join(', ')}`);
      }
    }

    // Validate query parameters
    if (schema.query) {
      const { error } = schema.query.validate(req.query);
      if (error) {
        errors.push(`Query: ${error.details.map(d => d.message).join(', ')}`);
      }
    }

    // Validate route parameters
    if (schema.params) {
      const { error } = schema.params.validate(req.params);
      if (error) {
        errors.push(`Params: ${error.details.map(d => d.message).join(', ')}`);
      }
    }

    if (errors.length > 0) {
      next(new AppError(`Validation error: ${errors.join('; ')}`, 400));
      return;
    }

    next();
  };
};

// Common validation schemas
const commonSchemas = {
  // Pagination
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    sort: Joi.string().optional(),
    order: Joi.string().valid('asc', 'desc').default('desc'),
  }),

  // ID parameter
  id: Joi.object({
    id: Joi.string().required(),
  }),

  // Search query
  search: Joi.object({
    q: Joi.string().min(1).max(100).optional(),
    category: Joi.string().optional(),
    status: Joi.string().optional(),
  }),
};

// Auth validation schemas
const authSchemas = {
  login: Joi.object({
    username: Joi.string().alphanum().min(3).max(30).required(),
    password: Joi.string().min(6).max(128).required(),
  }),

  register: Joi.object({
    username: Joi.string().alphanum().min(3).max(30).required(),
    password: Joi.string().min(6).max(128).required(),
    email: Joi.string().email().required(),
    nickname: Joi.string().min(1).max(50).optional(),
    phone: Joi.string().pattern(/^[0-9+\-\s()]+$/).optional(),
  }),

  changePassword: Joi.object({
    currentPassword: Joi.string().required(),
    newPassword: Joi.string().min(6).max(128).required(),
  }),

  resetPassword: Joi.object({
    email: Joi.string().email().required(),
  }),
};

// User validation schemas
const userSchemas = {
  updateProfile: Joi.object({
    nickname: Joi.string().min(1).max(50).optional(),
    email: Joi.string().email().optional(),
    phone: Joi.string().pattern(/^[0-9+\-\s()]+$/).optional(),
    avatar: Joi.string().uri().optional(),
  }),

  updateRole: Joi.object({
    role: Joi.string().valid('user', 'department_admin', 'admin').required(),
  }),

  createUser: Joi.object({
    username: Joi.string().alphanum().min(3).max(30).required(),
    password: Joi.string().min(6).max(128).required(),
    email: Joi.string().email().required(),
    nickname: Joi.string().min(1).max(50).optional(),
    phone: Joi.string().pattern(/^[0-9+\-\s()]+$/).optional(),
    role: Joi.string().valid('user', 'department_admin', 'admin').default('user'),
    department_id: Joi.string().optional(),
  }),
};

// Agent validation schemas
const agentSchemas = {
  createAgent: Joi.object({
    name: Joi.string().min(1).max(100).required(),
    display_name: Joi.string().min(1).max(100).required(),
    description: Joi.string().max(500).optional(),
    avatar: Joi.string().uri().optional(),
    category: Joi.string().max(50).optional(),
    capabilities: Joi.array().items(Joi.string()).optional(),
    config: Joi.object().optional(),
    is_public: Joi.boolean().default(true),
    status: Joi.string().valid('active', 'inactive').default('active'),
  }),

  updateAgent: Joi.object({
    name: Joi.string().min(1).max(100).optional(),
    display_name: Joi.string().min(1).max(100).optional(),
    description: Joi.string().max(500).optional(),
    avatar: Joi.string().uri().optional(),
    category: Joi.string().max(50).optional(),
    capabilities: Joi.array().items(Joi.string()).optional(),
    config: Joi.object().optional(),
    is_public: Joi.boolean().optional(),
    status: Joi.string().valid('active', 'inactive').optional(),
  }),
};

// Group validation schemas
const groupSchemas = {
  createGroup: Joi.object({
    name: Joi.string().min(1).max(100).required(),
    description: Joi.string().max(500).optional(),
    avatar: Joi.string().uri().optional(),
    is_public: Joi.boolean().default(false),
    max_members: Joi.number().integer().min(2).max(100).default(10),
    agents: Joi.array().items(Joi.string()).optional(),
  }),

  updateGroup: Joi.object({
    name: Joi.string().min(1).max(100).optional(),
    description: Joi.string().max(500).optional(),
    avatar: Joi.string().uri().optional(),
    is_public: Joi.boolean().optional(),
    max_members: Joi.number().integer().min(2).max(100).optional(),
  }),

  addMember: Joi.object({
    user_id: Joi.string().required(),
    role: Joi.string().valid('member', 'admin').default('member'),
  }),

  addAgent: Joi.object({
    agent_id: Joi.string().required(),
  }),
};

// Message validation schemas
const messageSchemas = {
  sendMessage: Joi.object({
    content: Joi.string().min(1).max(4000).required(),
    type: Joi.string().valid('text', 'image', 'file').default('text'),
    reply_to: Joi.string().optional(),
    metadata: Joi.object().optional(),
  }),
};

// Billing validation schemas
const billingSchemas = {
  recharge: Joi.object({
    amount: Joi.number().positive().required(),
    payment_method: Joi.string().valid('internal', 'alipay', 'wechat').default('internal'),
  }),

  rechargeByKey: Joi.object({
    recharge_key: Joi.string().min(1).max(100).required(),
  }),

  generateRechargeKey: Joi.object({
    amount: Joi.number().positive().required(),
    extend_days: Joi.number().integer().positive().required(),
    expires_at: Joi.date().optional(),
  }),
};

export {
  validateRequest,
  commonSchemas,
  authSchemas,
  userSchemas,
  agentSchemas,
  groupSchemas,
  messageSchemas,
  billingSchemas,
};
