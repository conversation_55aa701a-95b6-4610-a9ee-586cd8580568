import express from 'express';

const router = express.Router();

// Placeholder routes for billing
router.get('/balance', (req, res) => {
  res.json({ success: true, data: { balance: 0 } });
});

router.post('/recharge', (req, res) => {
  res.json({ success: true, message: 'Recharge successful' });
});

router.post('/recharge-by-key', (req, res) => {
  res.json({ success: true, message: 'Recharge by key successful' });
});

router.get('/transactions', (req, res) => {
  res.json({ success: true, data: { transactions: [] } });
});

export default router;
