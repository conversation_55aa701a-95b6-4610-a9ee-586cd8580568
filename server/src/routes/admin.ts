import express from 'express';
import { adminMiddleware } from '@/middleware/auth';
import { ssoService, SSOConfig } from '@/services/ssoService';
import { validateRequest } from '@/middleware/validation';
import Joi from 'joi';

const router = express.Router();

// All admin routes require admin role
router.use(adminMiddleware);

// Placeholder routes for admin
router.get('/users', (req, res) => {
  res.json({ success: true, data: { users: [] } });
});

router.post('/users', (req, res) => {
  res.json({ success: true, message: 'User created' });
});

router.get('/recharge-keys', (req, res) => {
  res.json({ success: true, data: { keys: [] } });
});

router.post('/recharge-keys/generate', (req, res) => {
  res.json({ success: true, message: 'Recharge key generated' });
});

router.get('/system/stats', (req, res) => {
  res.json({ success: true, data: { stats: {} } });
});

// SSO配置验证schema
const ssoConfigSchema = Joi.object({
  enabled: Joi.boolean().required(),
  type: Joi.string().valid('ldap', 'radius').required(),
  server: Joi.string().when('enabled', { is: true, then: Joi.required(), otherwise: Joi.optional() }),
  port: Joi.number().integer().min(1).max(65535).when('enabled', { is: true, then: Joi.required(), otherwise: Joi.optional() }),
  baseDN: Joi.string().when('type', { is: 'ldap', then: Joi.when('enabled', { is: true, then: Joi.required() }) }),
  bindDN: Joi.string().when('type', { is: 'ldap', then: Joi.when('enabled', { is: true, then: Joi.required() }) }),
  bindPassword: Joi.string().when('type', { is: 'ldap', then: Joi.when('enabled', { is: true, then: Joi.required() }) }),
  userFilter: Joi.string().optional(),
  secret: Joi.string().when('type', { is: 'radius', then: Joi.when('enabled', { is: true, then: Joi.required() }) })
});

// 获取SSO配置
router.get('/sso-config', async (req, res, next) => {
  try {
    const config = ssoService.getConfig();
    res.json({
      success: true,
      data: config
    });
  } catch (error) {
    next(error);
  }
});

// 保存SSO配置
router.post('/sso-config',
  validateRequest({ body: ssoConfigSchema }),
  async (req, res, next) => {
    try {
      const config: SSOConfig = req.body;

      // 验证配置
      const validation = ssoService.validateConfig(config);
      if (!validation.valid) {
        return res.status(400).json({
          success: false,
          message: validation.message
        });
      }

      // 如果启用SSO，测试连接
      if (config.enabled) {
        const testResult = await ssoService.testConnection(config);
        if (!testResult.success) {
          return res.status(400).json({
            success: false,
            message: `连接测试失败: ${testResult.message}`
          });
        }
      }

      // 保存配置
      ssoService.setConfig(config);

      res.json({
        success: true,
        message: 'SSO配置保存成功'
      });
    } catch (error) {
      next(error);
    }
  }
);

// 测试SSO连接
router.post('/sso-test',
  validateRequest({ body: ssoConfigSchema }),
  async (req, res, next) => {
    try {
      const config: SSOConfig = req.body;
      const testResult = await ssoService.testConnection(config);

      res.json({
        success: testResult.success,
        message: testResult.message
      });
    } catch (error) {
      next(error);
    }
  }
);

// 同步SSO用户
router.post('/sso-sync', async (req, res, next) => {
  try {
    const result = await ssoService.syncUsers();

    // 这里应该将用户保存到数据库
    // 由于我们还没有实现数据库层，这里只返回同步结果

    res.json({
      success: true,
      message: `成功同步 ${result.count} 个用户`,
      data: {
        syncedCount: result.count,
        users: result.users
      }
    });
  } catch (error) {
    next(error);
  }
});

export default router;
