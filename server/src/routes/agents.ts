import express from 'express';

const router = express.Router();

// Placeholder routes for agents
router.get('/', (req, res) => {
  res.json({ success: true, data: { agents: [] } });
});

router.post('/', (req, res) => {
  res.json({ success: true, message: 'Agent created' });
});

router.get('/:id', (req, res) => {
  res.json({ success: true, data: { agent: null } });
});

router.put('/:id', (req, res) => {
  res.json({ success: true, message: 'Agent updated' });
});

router.delete('/:id', (req, res) => {
  res.json({ success: true, message: 'Agent deleted' });
});

export default router;
