import express from 'express';
import { validateRequest, authSchemas } from '@/middleware/validation';
import { authRateLimiter } from '@/middleware/rateLimiter';
import { AppError } from '@/middleware/errorHandler';
import { generateToken, generateRefreshToken } from '@/middleware/auth';
import { logger } from '@/utils/logger';

const router = express.Router();

// Mock user data (replace with database queries)
const mockUsers = [
  {
    id: '1',
    username: 'admin',
    password: 'admin123', // In real app, this should be hashed
    email: '<EMAIL>',
    nickname: '系统管理员',
    role: 'admin',
    status: 1,
  },
];

// Login endpoint
router.post(
  '/login',
  authRateLimiter,
  validateRequest({ body: authSchemas.login }),
  async (req, res, next) => {
    try {
      const { username, password } = req.body;

      // Find user (replace with database query)
      const user = mockUsers.find(
        u => u.username === username && u.status === 1
      );

      if (!user) {
        throw new AppError('Invalid username or password', 401);
      }

      // Verify password (in real app, use bcrypt.compare)
      if (user.password !== password) {
        throw new AppError('Invalid username or password', 401);
      }

      // Generate tokens
      const tokenPayload = {
        userId: user.id,
        username: user.username,
        role: user.role,
        email: user.email,
      };

      const accessToken = generateToken(tokenPayload);
      const refreshToken = generateRefreshToken({
        userId: user.id,
        username: user.username,
      });

      logger.info(`User ${username} logged in successfully`);

      res.json({
        success: true,
        message: 'Login successful',
        data: {
          user: {
            id: user.id,
            username: user.username,
            email: user.email,
            nickname: user.nickname,
            role: user.role,
          },
          tokens: {
            accessToken,
            refreshToken,
            expiresIn: '7d',
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }
);

// Logout endpoint
router.post('/logout', async (req, res, next) => {
  try {
    // In a real app, you might want to blacklist the token
    // or remove it from a token store
    
    res.json({
      success: true,
      message: 'Logout successful',
    });
  } catch (error) {
    next(error);
  }
});

// Refresh token endpoint
router.post('/refresh', async (req, res, next) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      throw new AppError('Refresh token is required', 400);
    }

    // Verify refresh token (implement proper verification)
    // For now, just return a new access token
    
    res.json({
      success: true,
      message: 'Token refreshed successfully',
      data: {
        accessToken: 'new-access-token',
        expiresIn: '7d',
      },
    });
  } catch (error) {
    next(error);
  }
});

// Get current user info
router.get('/me', async (req, res, next) => {
  try {
    // This would normally require authentication middleware
    // For now, return mock data
    
    res.json({
      success: true,
      data: {
        user: mockUsers[0],
      },
    });
  } catch (error) {
    next(error);
  }
});

export default router;
